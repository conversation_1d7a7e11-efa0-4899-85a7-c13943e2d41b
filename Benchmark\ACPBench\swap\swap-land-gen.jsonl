{"id": 1650924803652700607, "group": "landmarks_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 4 agents: zoe, bob, vic, and steve. There are 4 items/roles: book02, book04, book01, and book03. Currently, bob is assigned book04, steve is assigned book03, vic is assigned book01, and zoe is assigned book02. The goal is to reach a state where the following facts hold: bob is assigned book02, steve is assigned book03, zoe is assigned book01, and vic is assigned book04. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(assigned steve book02)", "(assigned zoe book04)", "(assigned zoe book03)", "(assigned bob book03)", "(assigned steve book01)", "(assigned vic book02)", "(assigned bob book01)", "(assigned vic book03)", "(assigned steve book04)"], "yes": []}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-4-12)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects bob steve vic zoe - agent book01 book02 book03 book04 - role)\n    (:init (assigned bob book04) (assigned steve book03) (assigned vic book01) (assigned zoe book02) (not-eq bob steve) (not-eq bob vic) (not-eq bob zoe) (not-eq book01 book02) (not-eq book01 book03) (not-eq book01 book04) (not-eq book02 book01) (not-eq book02 book03) (not-eq book02 book04) (not-eq book03 book01) (not-eq book03 book02) (not-eq book03 book04) (not-eq book04 book01) (not-eq book04 book02) (not-eq book04 book03) (not-eq steve bob) (not-eq steve vic) (not-eq steve zoe) (not-eq vic bob) (not-eq vic steve) (not-eq vic zoe) (not-eq zoe bob) (not-eq zoe steve) (not-eq zoe vic))\n    (:goal (and (assigned bob book02) (assigned steve book03) (assigned vic book04) (assigned zoe book01)))\n)"}
{"id": -4841016987737534524, "group": "landmarks_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 7 agents: bob, xena, heidi, kevin, alice, ted, and dave. There are 7 items/roles: valerian, yam, ulluco, quince, mushroom, leek, and parsnip. Currently, kevin is assigned yam, ted is assigned parsnip, bob is assigned quince, heidi is assigned valerian, xena is assigned leek, alice is assigned mushroom, and dave is assigned ulluco. The goal is to reach a state where the following facts hold: heidi is assigned mushroom, kevin is assigned yam, alice is assigned valerian, bob is assigned parsnip, xena is assigned quince, ted is assigned leek, and dave is assigned ulluco. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(assigned xena ulluco)", "(assigned kevin parsnip)", "(assigned heidi leek)", "(assigned dave mushroom)", "(assigned bob mushroom)", "(assigned heidi ulluco)", "(assigned alice ulluco)", "(assigned bob ulluco)", "(assigned kevin leek)", "(assigned dave parsnip)", "(assigned dave quince)", "(assigned alice yam)", "(assigned xena mushroom)", "(assigned alice quince)", "(assigned bob yam)", "(assigned dave leek)", "(assigned heidi parsnip)", "(assigned ted quince)", "(assigned ted mushroom)", "(assigned kevin quince)", "(assigned ted yam)", "(assigned heidi quince)", "(assigned dave valerian)", "(assigned kevin ulluco)", "(assigned xena valerian)", "(assigned ted ulluco)", "(assigned kevin valerian)", "(assigned alice parsnip)", "(assigned kevin mushroom)", "(assigned heidi yam)", "(assigned xena parsnip)", "(assigned alice leek)", "(assigned bob valerian)", "(assigned dave yam)", "(assigned ted valerian)", "(assigned xena yam)", "(assigned bob leek)"], "yes": []}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-7-28)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects alice bob dave heidi kevin ted xena - agent leek mushroom parsnip quince ulluco valerian yam - role)\n    (:init (assigned alice mushroom) (assigned bob quince) (assigned dave ulluco) (assigned heidi valerian) (assigned kevin yam) (assigned ted parsnip) (assigned xena leek) (not-eq alice bob) (not-eq alice dave) (not-eq alice heidi) (not-eq alice kevin) (not-eq alice ted) (not-eq alice xena) (not-eq bob alice) (not-eq bob dave) (not-eq bob heidi) (not-eq bob kevin) (not-eq bob ted) (not-eq bob xena) (not-eq dave alice) (not-eq dave bob) (not-eq dave heidi) (not-eq dave kevin) (not-eq dave ted) (not-eq dave xena) (not-eq heidi alice) (not-eq heidi bob) (not-eq heidi dave) (not-eq heidi kevin) (not-eq heidi ted) (not-eq heidi xena) (not-eq kevin alice) (not-eq kevin bob) (not-eq kevin dave) (not-eq kevin heidi) (not-eq kevin ted) (not-eq kevin xena) (not-eq leek mushroom) (not-eq leek parsnip) (not-eq leek quince) (not-eq leek ulluco) (not-eq leek valerian) (not-eq leek yam) (not-eq mushroom leek) (not-eq mushroom parsnip) (not-eq mushroom quince) (not-eq mushroom ulluco) (not-eq mushroom valerian) (not-eq mushroom yam) (not-eq parsnip leek) (not-eq parsnip mushroom) (not-eq parsnip quince) (not-eq parsnip ulluco) (not-eq parsnip valerian) (not-eq parsnip yam) (not-eq quince leek) (not-eq quince mushroom) (not-eq quince parsnip) (not-eq quince ulluco) (not-eq quince valerian) (not-eq quince yam) (not-eq ted alice) (not-eq ted bob) (not-eq ted dave) (not-eq ted heidi) (not-eq ted kevin) (not-eq ted xena) (not-eq ulluco leek) (not-eq ulluco mushroom) (not-eq ulluco parsnip) (not-eq ulluco quince) (not-eq ulluco valerian) (not-eq ulluco yam) (not-eq valerian leek) (not-eq valerian mushroom) (not-eq valerian parsnip) (not-eq valerian quince) (not-eq valerian ulluco) (not-eq valerian yam) (not-eq xena alice) (not-eq xena bob) (not-eq xena dave) (not-eq xena heidi) (not-eq xena kevin) (not-eq xena ted) (not-eq yam leek) (not-eq yam mushroom) (not-eq yam parsnip) (not-eq yam quince) (not-eq yam ulluco) (not-eq yam valerian))\n    (:goal (and (assigned xena quince) (assigned heidi mushroom) (assigned kevin yam) (assigned alice valerian) (assigned dave ulluco) (assigned ted leek) (assigned bob parsnip)))\n)"}
{"id": 7814228061187780697, "group": "landmarks_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: bob, xena, frank, quentin, liam, and vic. There are 6 items/roles: wrench, sander, knead, ratchet, nibbler, and pliers. Currently, bob is assigned pliers, liam is assigned wrench, quentin is assigned ratchet, xena is assigned knead, frank is assigned sander, and vic is assigned nibbler. The goal is to reach a state where the following facts hold: frank is assigned pliers, vic is assigned ratchet, xena is assigned wrench, liam is assigned sander, quentin is assigned knead, and bob is assigned nibbler. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(assigned quentin nibbler)", "(assigned frank wrench)", "(assigned liam pliers)", "(assigned liam nibbler)", "(assigned liam knead)", "(assigned quentin sander)", "(assigned bob knead)", "(assigned bob sander)", "(assigned vic knead)", "(assigned frank ratchet)", "(assigned vic pliers)", "(assigned xena sander)", "(assigned quentin pliers)", "(assigned frank knead)", "(assigned liam ratchet)", "(assigned frank nibbler)", "(assigned quentin wrench)", "(assigned vic wrench)", "(assigned xena ratchet)", "(assigned bob wrench)", "(assigned bob ratchet)", "(assigned vic sander)", "(assigned xena pliers)", "(assigned xena nibbler)"], "yes": []}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects bob frank liam quentin vic xena - agent knead nibbler pliers ratchet sander wrench - role)\n    (:init (assigned bob pliers) (assigned frank sander) (assigned liam wrench) (assigned quentin ratchet) (assigned vic nibbler) (assigned xena knead) (not-eq bob frank) (not-eq bob liam) (not-eq bob quentin) (not-eq bob vic) (not-eq bob xena) (not-eq frank bob) (not-eq frank liam) (not-eq frank quentin) (not-eq frank vic) (not-eq frank xena) (not-eq knead nibbler) (not-eq knead pliers) (not-eq knead ratchet) (not-eq knead sander) (not-eq knead wrench) (not-eq liam bob) (not-eq liam frank) (not-eq liam quentin) (not-eq liam vic) (not-eq liam xena) (not-eq nibbler knead) (not-eq nibbler pliers) (not-eq nibbler ratchet) (not-eq nibbler sander) (not-eq nibbler wrench) (not-eq pliers knead) (not-eq pliers nibbler) (not-eq pliers ratchet) (not-eq pliers sander) (not-eq pliers wrench) (not-eq quentin bob) (not-eq quentin frank) (not-eq quentin liam) (not-eq quentin vic) (not-eq quentin xena) (not-eq ratchet knead) (not-eq ratchet nibbler) (not-eq ratchet pliers) (not-eq ratchet sander) (not-eq ratchet wrench) (not-eq sander knead) (not-eq sander nibbler) (not-eq sander pliers) (not-eq sander ratchet) (not-eq sander wrench) (not-eq vic bob) (not-eq vic frank) (not-eq vic liam) (not-eq vic quentin) (not-eq vic xena) (not-eq wrench knead) (not-eq wrench nibbler) (not-eq wrench pliers) (not-eq wrench ratchet) (not-eq wrench sander) (not-eq xena bob) (not-eq xena frank) (not-eq xena liam) (not-eq xena quentin) (not-eq xena vic))\n    (:goal (and (assigned quentin knead) (assigned liam sander) (assigned frank pliers) (assigned xena wrench) (assigned bob nibbler) (assigned vic ratchet)))\n)"}
{"id": 5207947414361131946, "group": "landmarks_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: bob, xena, frank, quentin, liam, and vic. There are 6 items/roles: wrench, sander, knead, ratchet, nibbler, and pliers. Currently, xena is assigned ratchet, quentin is assigned pliers, bob is assigned wrench, vic is assigned knead, frank is assigned sander, and liam is assigned nibbler. The goal is to reach a state where the following facts hold: frank is assigned pliers, vic is assigned ratchet, xena is assigned wrench, liam is assigned sander, quentin is assigned knead, and bob is assigned nibbler. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(assigned bob pliers)", "(assigned quentin nibbler)", "(assigned frank wrench)", "(assigned liam pliers)", "(assigned liam knead)", "(assigned quentin sander)", "(assigned bob knead)", "(assigned liam wrench)", "(assigned bob sander)", "(assigned frank ratchet)", "(assigned vic pliers)", "(assigned xena sander)", "(assigned xena knead)", "(assigned frank knead)", "(assigned liam ratchet)", "(assigned frank nibbler)", "(assigned quentin wrench)", "(assigned vic wrench)", "(assigned quentin ratchet)", "(assigned bob ratchet)", "(assigned vic sander)", "(assigned xena pliers)", "(assigned xena nibbler)", "(assigned vic nibbler)"], "yes": []}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects bob frank liam quentin vic xena - agent knead nibbler pliers ratchet sander wrench - role)\n    (:init (assigned bob wrench) (assigned frank sander) (assigned liam nibbler) (assigned quentin pliers) (assigned vic knead) (assigned xena ratchet) (not-eq bob frank) (not-eq bob liam) (not-eq bob quentin) (not-eq bob vic) (not-eq bob xena) (not-eq frank bob) (not-eq frank liam) (not-eq frank quentin) (not-eq frank vic) (not-eq frank xena) (not-eq knead nibbler) (not-eq knead pliers) (not-eq knead ratchet) (not-eq knead sander) (not-eq knead wrench) (not-eq liam bob) (not-eq liam frank) (not-eq liam quentin) (not-eq liam vic) (not-eq liam xena) (not-eq nibbler knead) (not-eq nibbler pliers) (not-eq nibbler ratchet) (not-eq nibbler sander) (not-eq nibbler wrench) (not-eq pliers knead) (not-eq pliers nibbler) (not-eq pliers ratchet) (not-eq pliers sander) (not-eq pliers wrench) (not-eq quentin bob) (not-eq quentin frank) (not-eq quentin liam) (not-eq quentin vic) (not-eq quentin xena) (not-eq ratchet knead) (not-eq ratchet nibbler) (not-eq ratchet pliers) (not-eq ratchet sander) (not-eq ratchet wrench) (not-eq sander knead) (not-eq sander nibbler) (not-eq sander pliers) (not-eq sander ratchet) (not-eq sander wrench) (not-eq vic bob) (not-eq vic frank) (not-eq vic liam) (not-eq vic quentin) (not-eq vic xena) (not-eq wrench knead) (not-eq wrench nibbler) (not-eq wrench pliers) (not-eq wrench ratchet) (not-eq wrench sander) (not-eq xena bob) (not-eq xena frank) (not-eq xena liam) (not-eq xena quentin) (not-eq xena vic))\n    (:goal (and (assigned quentin knead) (assigned liam sander) (assigned frank pliers) (assigned xena wrench) (assigned bob nibbler) (assigned vic ratchet)))\n)"}
{"id": 202415298345573016, "group": "landmarks_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 8 agents: zoe, xena, heidi, carol, dave, alice, michelle, and vic. There are 8 items/roles: zebra, frisbee, necklace, iceskates, guitar, slinky, quadcopter, and whale. Currently, alice is assigned guitar, xena is assigned slinky, michelle is assigned quadcopter, zoe is assigned iceskates, vic is assigned frisbee, dave is assigned whale, heidi is assigned zebra, and carol is assigned necklace. The goal is to reach a state where the following facts hold: vic is assigned necklace, heidi is assigned guitar, zoe is assigned whale, dave is assigned iceskates, xena is assigned slinky, michelle is assigned quadcopter, carol is assigned frisbee, and alice is assigned zebra. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(assigned heidi whale)", "(assigned alice necklace)", "(assigned carol quadcopter)", "(assigned michelle whale)", "(assigned michelle slinky)", "(assigned zoe frisbee)", "(assigned heidi necklace)", "(assigned alice slinky)", "(assigned vic guitar)", "(assigned zoe necklace)", "(assigned michelle necklace)", "(assigned alice iceskates)", "(assigned michelle zebra)", "(assigned carol zebra)", "(assigned xena zebra)", "(assigned vic slinky)", "(assigned dave guitar)", "(assigned michelle guitar)", "(assigned carol slinky)", "(assigned alice frisbee)", "(assigned alice quadcopter)", "(assigned vic iceskates)", "(assigned xena guitar)", "(assigned carol whale)", "(assigned zoe guitar)", "(assigned xena whale)", "(assigned carol guitar)", "(assigned vic whale)", "(assigned alice whale)", "(assigned heidi quadcopter)", "(assigned dave frisbee)", "(assigned heidi frisbee)", "(assigned xena necklace)", "(assigned dave zebra)", "(assigned xena frisbee)", "(assigned michelle frisbee)", "(assigned heidi slinky)", "(assigned dave slinky)", "(assigned dave quadcopter)", "(assigned carol iceskates)", "(assigned xena quadcopter)", "(assigned dave necklace)", "(assigned xena iceskates)", "(assigned vic quadcopter)", "(assigned zoe quadcopter)", "(assigned zoe slinky)", "(assigned heidi iceskates)", "(assigned michelle iceskates)", "(assigned vic zebra)", "(assigned zoe zebra)"], "yes": []}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-8-36)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects alice carol dave heidi michelle vic xena zoe - agent frisbee guitar iceskates necklace quadcopter slinky whale zebra - role)\n    (:init (assigned alice guitar) (assigned carol necklace) (assigned dave whale) (assigned heidi zebra) (assigned michelle quadcopter) (assigned vic frisbee) (assigned xena slinky) (assigned zoe iceskates) (not-eq alice carol) (not-eq alice dave) (not-eq alice heidi) (not-eq alice michelle) (not-eq alice vic) (not-eq alice xena) (not-eq alice zoe) (not-eq carol alice) (not-eq carol dave) (not-eq carol heidi) (not-eq carol michelle) (not-eq carol vic) (not-eq carol xena) (not-eq carol zoe) (not-eq dave alice) (not-eq dave carol) (not-eq dave heidi) (not-eq dave michelle) (not-eq dave vic) (not-eq dave xena) (not-eq dave zoe) (not-eq frisbee guitar) (not-eq frisbee iceskates) (not-eq frisbee necklace) (not-eq frisbee quadcopter) (not-eq frisbee slinky) (not-eq frisbee whale) (not-eq frisbee zebra) (not-eq guitar frisbee) (not-eq guitar iceskates) (not-eq guitar necklace) (not-eq guitar quadcopter) (not-eq guitar slinky) (not-eq guitar whale) (not-eq guitar zebra) (not-eq heidi alice) (not-eq heidi carol) (not-eq heidi dave) (not-eq heidi michelle) (not-eq heidi vic) (not-eq heidi xena) (not-eq heidi zoe) (not-eq iceskates frisbee) (not-eq iceskates guitar) (not-eq iceskates necklace) (not-eq iceskates quadcopter) (not-eq iceskates slinky) (not-eq iceskates whale) (not-eq iceskates zebra) (not-eq michelle alice) (not-eq michelle carol) (not-eq michelle dave) (not-eq michelle heidi) (not-eq michelle vic) (not-eq michelle xena) (not-eq michelle zoe) (not-eq necklace frisbee) (not-eq necklace guitar) (not-eq necklace iceskates) (not-eq necklace quadcopter) (not-eq necklace slinky) (not-eq necklace whale) (not-eq necklace zebra) (not-eq quadcopter frisbee) (not-eq quadcopter guitar) (not-eq quadcopter iceskates) (not-eq quadcopter necklace) (not-eq quadcopter slinky) (not-eq quadcopter whale) (not-eq quadcopter zebra) (not-eq slinky frisbee) (not-eq slinky guitar) (not-eq slinky iceskates) (not-eq slinky necklace) (not-eq slinky quadcopter) (not-eq slinky whale) (not-eq slinky zebra) (not-eq vic alice) (not-eq vic carol) (not-eq vic dave) (not-eq vic heidi) (not-eq vic michelle) (not-eq vic xena) (not-eq vic zoe) (not-eq whale frisbee) (not-eq whale guitar) (not-eq whale iceskates) (not-eq whale necklace) (not-eq whale quadcopter) (not-eq whale slinky) (not-eq whale zebra) (not-eq xena alice) (not-eq xena carol) (not-eq xena dave) (not-eq xena heidi) (not-eq xena michelle) (not-eq xena vic) (not-eq xena zoe) (not-eq zebra frisbee) (not-eq zebra guitar) (not-eq zebra iceskates) (not-eq zebra necklace) (not-eq zebra quadcopter) (not-eq zebra slinky) (not-eq zebra whale) (not-eq zoe alice) (not-eq zoe carol) (not-eq zoe dave) (not-eq zoe heidi) (not-eq zoe michelle) (not-eq zoe vic) (not-eq zoe xena))\n    (:goal (and (assigned xena slinky) (assigned dave iceskates) (assigned alice zebra) (assigned michelle quadcopter) (assigned vic necklace) (assigned heidi guitar) (assigned carol frisbee) (assigned zoe whale)))\n)"}
{"id": -5983667419851236030, "group": "landmarks_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 7 agents: bob, xena, heidi, kevin, alice, ted, and dave. There are 7 items/roles: valerian, yam, ulluco, quince, mushroom, leek, and parsnip. Currently, kevin is assigned parsnip, heidi is assigned valerian, ted is assigned yam, xena is assigned quince, dave is assigned leek, alice is assigned mushroom, and bob is assigned ulluco. The goal is to reach a state where the following facts hold: heidi is assigned mushroom, kevin is assigned yam, alice is assigned valerian, bob is assigned parsnip, xena is assigned quince, ted is assigned leek, and dave is assigned ulluco. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(assigned xena ulluco)", "(assigned bob quince)", "(assigned heidi leek)", "(assigned dave mushroom)", "(assigned bob mushroom)", "(assigned heidi ulluco)", "(assigned alice ulluco)", "(assigned kevin leek)", "(assigned dave parsnip)", "(assigned ted parsnip)", "(assigned dave quince)", "(assigned alice yam)", "(assigned xena mushroom)", "(assigned alice quince)", "(assigned bob yam)", "(assigned heidi parsnip)", "(assigned ted quince)", "(assigned ted mushroom)", "(assigned kevin quince)", "(assigned heidi quince)", "(assigned dave valerian)", "(assigned kevin ulluco)", "(assigned xena valerian)", "(assigned ted ulluco)", "(assigned kevin valerian)", "(assigned alice parsnip)", "(assigned kevin mushroom)", "(assigned heidi yam)", "(assigned xena parsnip)", "(assigned alice leek)", "(assigned bob valerian)", "(assigned dave yam)", "(assigned ted valerian)", "(assigned xena leek)", "(assigned xena yam)", "(assigned bob leek)"], "yes": []}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-7-28)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects alice bob dave heidi kevin ted xena - agent leek mushroom parsnip quince ulluco valerian yam - role)\n    (:init (assigned alice mushroom) (assigned bob ulluco) (assigned dave leek) (assigned heidi valerian) (assigned kevin parsnip) (assigned ted yam) (assigned xena quince) (not-eq alice bob) (not-eq alice dave) (not-eq alice heidi) (not-eq alice kevin) (not-eq alice ted) (not-eq alice xena) (not-eq bob alice) (not-eq bob dave) (not-eq bob heidi) (not-eq bob kevin) (not-eq bob ted) (not-eq bob xena) (not-eq dave alice) (not-eq dave bob) (not-eq dave heidi) (not-eq dave kevin) (not-eq dave ted) (not-eq dave xena) (not-eq heidi alice) (not-eq heidi bob) (not-eq heidi dave) (not-eq heidi kevin) (not-eq heidi ted) (not-eq heidi xena) (not-eq kevin alice) (not-eq kevin bob) (not-eq kevin dave) (not-eq kevin heidi) (not-eq kevin ted) (not-eq kevin xena) (not-eq leek mushroom) (not-eq leek parsnip) (not-eq leek quince) (not-eq leek ulluco) (not-eq leek valerian) (not-eq leek yam) (not-eq mushroom leek) (not-eq mushroom parsnip) (not-eq mushroom quince) (not-eq mushroom ulluco) (not-eq mushroom valerian) (not-eq mushroom yam) (not-eq parsnip leek) (not-eq parsnip mushroom) (not-eq parsnip quince) (not-eq parsnip ulluco) (not-eq parsnip valerian) (not-eq parsnip yam) (not-eq quince leek) (not-eq quince mushroom) (not-eq quince parsnip) (not-eq quince ulluco) (not-eq quince valerian) (not-eq quince yam) (not-eq ted alice) (not-eq ted bob) (not-eq ted dave) (not-eq ted heidi) (not-eq ted kevin) (not-eq ted xena) (not-eq ulluco leek) (not-eq ulluco mushroom) (not-eq ulluco parsnip) (not-eq ulluco quince) (not-eq ulluco valerian) (not-eq ulluco yam) (not-eq valerian leek) (not-eq valerian mushroom) (not-eq valerian parsnip) (not-eq valerian quince) (not-eq valerian ulluco) (not-eq valerian yam) (not-eq xena alice) (not-eq xena bob) (not-eq xena dave) (not-eq xena heidi) (not-eq xena kevin) (not-eq xena ted) (not-eq yam leek) (not-eq yam mushroom) (not-eq yam parsnip) (not-eq yam quince) (not-eq yam ulluco) (not-eq yam valerian))\n    (:goal (and (assigned xena quince) (assigned heidi mushroom) (assigned kevin yam) (assigned alice valerian) (assigned dave ulluco) (assigned ted leek) (assigned bob parsnip)))\n)"}
{"id": 2852305707467014471, "group": "landmarks_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: bob, xena, frank, quentin, liam, and vic. There are 6 items/roles: wrench, sander, knead, ratchet, nibbler, and pliers. Currently, xena is assigned sander, vic is assigned ratchet, quentin is assigned pliers, frank is assigned knead, bob is assigned wrench, and liam is assigned nibbler. The goal is to reach a state where the following facts hold: frank is assigned pliers, vic is assigned ratchet, xena is assigned wrench, liam is assigned sander, quentin is assigned knead, and bob is assigned nibbler. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(assigned bob pliers)", "(assigned quentin nibbler)", "(assigned frank wrench)", "(assigned liam pliers)", "(assigned liam knead)", "(assigned quentin sander)", "(assigned bob knead)", "(assigned liam wrench)", "(assigned bob sander)", "(assigned vic knead)", "(assigned frank sander)", "(assigned frank ratchet)", "(assigned vic pliers)", "(assigned xena knead)", "(assigned liam ratchet)", "(assigned frank nibbler)", "(assigned quentin wrench)", "(assigned vic wrench)", "(assigned xena ratchet)", "(assigned quentin ratchet)", "(assigned bob ratchet)", "(assigned vic sander)", "(assigned xena pliers)", "(assigned xena nibbler)", "(assigned vic nibbler)"], "yes": []}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects bob frank liam quentin vic xena - agent knead nibbler pliers ratchet sander wrench - role)\n    (:init (assigned bob wrench) (assigned frank knead) (assigned liam nibbler) (assigned quentin pliers) (assigned vic ratchet) (assigned xena sander) (not-eq bob frank) (not-eq bob liam) (not-eq bob quentin) (not-eq bob vic) (not-eq bob xena) (not-eq frank bob) (not-eq frank liam) (not-eq frank quentin) (not-eq frank vic) (not-eq frank xena) (not-eq knead nibbler) (not-eq knead pliers) (not-eq knead ratchet) (not-eq knead sander) (not-eq knead wrench) (not-eq liam bob) (not-eq liam frank) (not-eq liam quentin) (not-eq liam vic) (not-eq liam xena) (not-eq nibbler knead) (not-eq nibbler pliers) (not-eq nibbler ratchet) (not-eq nibbler sander) (not-eq nibbler wrench) (not-eq pliers knead) (not-eq pliers nibbler) (not-eq pliers ratchet) (not-eq pliers sander) (not-eq pliers wrench) (not-eq quentin bob) (not-eq quentin frank) (not-eq quentin liam) (not-eq quentin vic) (not-eq quentin xena) (not-eq ratchet knead) (not-eq ratchet nibbler) (not-eq ratchet pliers) (not-eq ratchet sander) (not-eq ratchet wrench) (not-eq sander knead) (not-eq sander nibbler) (not-eq sander pliers) (not-eq sander ratchet) (not-eq sander wrench) (not-eq vic bob) (not-eq vic frank) (not-eq vic liam) (not-eq vic quentin) (not-eq vic xena) (not-eq wrench knead) (not-eq wrench nibbler) (not-eq wrench pliers) (not-eq wrench ratchet) (not-eq wrench sander) (not-eq xena bob) (not-eq xena frank) (not-eq xena liam) (not-eq xena quentin) (not-eq xena vic))\n    (:goal (and (assigned quentin knead) (assigned liam sander) (assigned frank pliers) (assigned xena wrench) (assigned bob nibbler) (assigned vic ratchet)))\n)"}
{"id": 86387402855353987, "group": "landmarks_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: bob, xena, frank, quentin, liam, and vic. There are 6 items/roles: wrench, sander, knead, ratchet, nibbler, and pliers. Currently, quentin is assigned wrench, bob is assigned pliers, vic is assigned ratchet, frank is assigned knead, liam is assigned sander, and xena is assigned nibbler. The goal is to reach a state where the following facts hold: frank is assigned pliers, vic is assigned ratchet, xena is assigned wrench, liam is assigned sander, quentin is assigned knead, and bob is assigned nibbler. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(assigned quentin nibbler)", "(assigned frank wrench)", "(assigned liam pliers)", "(assigned liam nibbler)", "(assigned liam knead)", "(assigned quentin sander)", "(assigned bob sander)", "(assigned liam wrench)", "(assigned bob knead)", "(assigned vic knead)", "(assigned frank sander)", "(assigned frank ratchet)", "(assigned vic pliers)", "(assigned xena sander)", "(assigned xena knead)", "(assigned quentin pliers)", "(assigned liam ratchet)", "(assigned frank nibbler)", "(assigned vic wrench)", "(assigned xena ratchet)", "(assigned quentin ratchet)", "(assigned bob wrench)", "(assigned bob ratchet)", "(assigned vic sander)", "(assigned xena pliers)", "(assigned vic nibbler)"], "yes": []}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects bob frank liam quentin vic xena - agent knead nibbler pliers ratchet sander wrench - role)\n    (:init (assigned bob pliers) (assigned frank knead) (assigned liam sander) (assigned quentin wrench) (assigned vic ratchet) (assigned xena nibbler) (not-eq bob frank) (not-eq bob liam) (not-eq bob quentin) (not-eq bob vic) (not-eq bob xena) (not-eq frank bob) (not-eq frank liam) (not-eq frank quentin) (not-eq frank vic) (not-eq frank xena) (not-eq knead nibbler) (not-eq knead pliers) (not-eq knead ratchet) (not-eq knead sander) (not-eq knead wrench) (not-eq liam bob) (not-eq liam frank) (not-eq liam quentin) (not-eq liam vic) (not-eq liam xena) (not-eq nibbler knead) (not-eq nibbler pliers) (not-eq nibbler ratchet) (not-eq nibbler sander) (not-eq nibbler wrench) (not-eq pliers knead) (not-eq pliers nibbler) (not-eq pliers ratchet) (not-eq pliers sander) (not-eq pliers wrench) (not-eq quentin bob) (not-eq quentin frank) (not-eq quentin liam) (not-eq quentin vic) (not-eq quentin xena) (not-eq ratchet knead) (not-eq ratchet nibbler) (not-eq ratchet pliers) (not-eq ratchet sander) (not-eq ratchet wrench) (not-eq sander knead) (not-eq sander nibbler) (not-eq sander pliers) (not-eq sander ratchet) (not-eq sander wrench) (not-eq vic bob) (not-eq vic frank) (not-eq vic liam) (not-eq vic quentin) (not-eq vic xena) (not-eq wrench knead) (not-eq wrench nibbler) (not-eq wrench pliers) (not-eq wrench ratchet) (not-eq wrench sander) (not-eq xena bob) (not-eq xena frank) (not-eq xena liam) (not-eq xena quentin) (not-eq xena vic))\n    (:goal (and (assigned quentin knead) (assigned liam sander) (assigned frank pliers) (assigned xena wrench) (assigned bob nibbler) (assigned vic ratchet)))\n)"}
{"id": -3013253270653405025, "group": "landmarks_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 8 agents: zoe, xena, heidi, carol, dave, alice, michelle, and vic. There are 8 items/roles: zebra, frisbee, necklace, iceskates, guitar, slinky, quadcopter, and whale. Currently, heidi is assigned quadcopter, zoe is assigned slinky, carol is assigned guitar, michelle is assigned iceskates, dave is assigned frisbee, xena is assigned necklace, vic is assigned zebra, and alice is assigned whale. The goal is to reach a state where the following facts hold: vic is assigned necklace, heidi is assigned guitar, zoe is assigned whale, dave is assigned iceskates, xena is assigned slinky, michelle is assigned quadcopter, carol is assigned frisbee, and alice is assigned zebra. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(assigned zoe guitar)", "(assigned xena whale)", "(assigned heidi whale)", "(assigned zoe iceskates)", "(assigned carol quadcopter)", "(assigned alice necklace)", "(assigned michelle whale)", "(assigned dave whale)", "(assigned heidi zebra)", "(assigned vic whale)", "(assigned carol necklace)", "(assigned michelle slinky)", "(assigned zoe frisbee)", "(assigned heidi necklace)", "(assigned alice slinky)", "(assigned heidi frisbee)", "(assigned vic guitar)", "(assigned zoe necklace)", "(assigned dave zebra)", "(assigned michelle necklace)", "(assigned xena frisbee)", "(assigned michelle frisbee)", "(assigned heidi slinky)", "(assigned dave slinky)", "(assigned dave quadcopter)", "(assigned carol iceskates)", "(assigned alice guitar)", "(assigned alice iceskates)", "(assigned michelle zebra)", "(assigned xena quadcopter)", "(assigned dave necklace)", "(assigned carol zebra)", "(assigned xena zebra)", "(assigned xena iceskates)", "(assigned vic slinky)", "(assigned vic quadcopter)", "(assigned dave guitar)", "(assigned zoe quadcopter)", "(assigned michelle guitar)", "(assigned carol slinky)", "(assigned heidi iceskates)", "(assigned alice frisbee)", "(assigned alice quadcopter)", "(assigned vic iceskates)", "(assigned xena guitar)", "(assigned carol whale)", "(assigned zoe zebra)", "(assigned vic frisbee)"], "yes": []}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-8-36)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects alice carol dave heidi michelle vic xena zoe - agent frisbee guitar iceskates necklace quadcopter slinky whale zebra - role)\n    (:init (assigned alice whale) (assigned carol guitar) (assigned dave frisbee) (assigned heidi quadcopter) (assigned michelle iceskates) (assigned vic zebra) (assigned xena necklace) (assigned zoe slinky) (not-eq alice carol) (not-eq alice dave) (not-eq alice heidi) (not-eq alice michelle) (not-eq alice vic) (not-eq alice xena) (not-eq alice zoe) (not-eq carol alice) (not-eq carol dave) (not-eq carol heidi) (not-eq carol michelle) (not-eq carol vic) (not-eq carol xena) (not-eq carol zoe) (not-eq dave alice) (not-eq dave carol) (not-eq dave heidi) (not-eq dave michelle) (not-eq dave vic) (not-eq dave xena) (not-eq dave zoe) (not-eq frisbee guitar) (not-eq frisbee iceskates) (not-eq frisbee necklace) (not-eq frisbee quadcopter) (not-eq frisbee slinky) (not-eq frisbee whale) (not-eq frisbee zebra) (not-eq guitar frisbee) (not-eq guitar iceskates) (not-eq guitar necklace) (not-eq guitar quadcopter) (not-eq guitar slinky) (not-eq guitar whale) (not-eq guitar zebra) (not-eq heidi alice) (not-eq heidi carol) (not-eq heidi dave) (not-eq heidi michelle) (not-eq heidi vic) (not-eq heidi xena) (not-eq heidi zoe) (not-eq iceskates frisbee) (not-eq iceskates guitar) (not-eq iceskates necklace) (not-eq iceskates quadcopter) (not-eq iceskates slinky) (not-eq iceskates whale) (not-eq iceskates zebra) (not-eq michelle alice) (not-eq michelle carol) (not-eq michelle dave) (not-eq michelle heidi) (not-eq michelle vic) (not-eq michelle xena) (not-eq michelle zoe) (not-eq necklace frisbee) (not-eq necklace guitar) (not-eq necklace iceskates) (not-eq necklace quadcopter) (not-eq necklace slinky) (not-eq necklace whale) (not-eq necklace zebra) (not-eq quadcopter frisbee) (not-eq quadcopter guitar) (not-eq quadcopter iceskates) (not-eq quadcopter necklace) (not-eq quadcopter slinky) (not-eq quadcopter whale) (not-eq quadcopter zebra) (not-eq slinky frisbee) (not-eq slinky guitar) (not-eq slinky iceskates) (not-eq slinky necklace) (not-eq slinky quadcopter) (not-eq slinky whale) (not-eq slinky zebra) (not-eq vic alice) (not-eq vic carol) (not-eq vic dave) (not-eq vic heidi) (not-eq vic michelle) (not-eq vic xena) (not-eq vic zoe) (not-eq whale frisbee) (not-eq whale guitar) (not-eq whale iceskates) (not-eq whale necklace) (not-eq whale quadcopter) (not-eq whale slinky) (not-eq whale zebra) (not-eq xena alice) (not-eq xena carol) (not-eq xena dave) (not-eq xena heidi) (not-eq xena michelle) (not-eq xena vic) (not-eq xena zoe) (not-eq zebra frisbee) (not-eq zebra guitar) (not-eq zebra iceskates) (not-eq zebra necklace) (not-eq zebra quadcopter) (not-eq zebra slinky) (not-eq zebra whale) (not-eq zoe alice) (not-eq zoe carol) (not-eq zoe dave) (not-eq zoe heidi) (not-eq zoe michelle) (not-eq zoe vic) (not-eq zoe xena))\n    (:goal (and (assigned xena slinky) (assigned dave iceskates) (assigned alice zebra) (assigned michelle quadcopter) (assigned vic necklace) (assigned heidi guitar) (assigned carol frisbee) (assigned zoe whale)))\n)"}
{"id": -6582948589492044901, "group": "landmarks_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 7 agents: bob, xena, heidi, kevin, alice, ted, and dave. There are 7 items/roles: valerian, yam, ulluco, quince, mushroom, leek, and parsnip. Currently, alice is assigned yam, bob is assigned quince, kevin is assigned parsnip, heidi is assigned leek, dave is assigned valerian, xena is assigned mushroom, and ted is assigned ulluco. The goal is to reach a state where the following facts hold: heidi is assigned mushroom, kevin is assigned yam, alice is assigned valerian, bob is assigned parsnip, xena is assigned quince, ted is assigned leek, and dave is assigned ulluco. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(assigned xena ulluco)", "(assigned dave mushroom)", "(assigned bob mushroom)", "(assigned heidi ulluco)", "(assigned alice ulluco)", "(assigned bob ulluco)", "(assigned kevin leek)", "(assigned dave parsnip)", "(assigned ted parsnip)", "(assigned dave quince)", "(assigned alice quince)", "(assigned bob yam)", "(assigned dave leek)", "(assigned alice mushroom)", "(assigned heidi parsnip)", "(assigned ted quince)", "(assigned ted mushroom)", "(assigned kevin quince)", "(assigned heidi valerian)", "(assigned ted yam)", "(assigned heidi quince)", "(assigned kevin ulluco)", "(assigned xena valerian)", "(assigned kevin valerian)", "(assigned alice parsnip)", "(assigned kevin mushroom)", "(assigned heidi yam)", "(assigned xena parsnip)", "(assigned alice leek)", "(assigned bob valerian)", "(assigned dave yam)", "(assigned ted valerian)", "(assigned xena leek)", "(assigned xena yam)", "(assigned bob leek)"], "yes": []}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-7-28)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects alice bob dave heidi kevin ted xena - agent leek mushroom parsnip quince ulluco valerian yam - role)\n    (:init (assigned alice yam) (assigned bob quince) (assigned dave valerian) (assigned heidi leek) (assigned kevin parsnip) (assigned ted ulluco) (assigned xena mushroom) (not-eq alice bob) (not-eq alice dave) (not-eq alice heidi) (not-eq alice kevin) (not-eq alice ted) (not-eq alice xena) (not-eq bob alice) (not-eq bob dave) (not-eq bob heidi) (not-eq bob kevin) (not-eq bob ted) (not-eq bob xena) (not-eq dave alice) (not-eq dave bob) (not-eq dave heidi) (not-eq dave kevin) (not-eq dave ted) (not-eq dave xena) (not-eq heidi alice) (not-eq heidi bob) (not-eq heidi dave) (not-eq heidi kevin) (not-eq heidi ted) (not-eq heidi xena) (not-eq kevin alice) (not-eq kevin bob) (not-eq kevin dave) (not-eq kevin heidi) (not-eq kevin ted) (not-eq kevin xena) (not-eq leek mushroom) (not-eq leek parsnip) (not-eq leek quince) (not-eq leek ulluco) (not-eq leek valerian) (not-eq leek yam) (not-eq mushroom leek) (not-eq mushroom parsnip) (not-eq mushroom quince) (not-eq mushroom ulluco) (not-eq mushroom valerian) (not-eq mushroom yam) (not-eq parsnip leek) (not-eq parsnip mushroom) (not-eq parsnip quince) (not-eq parsnip ulluco) (not-eq parsnip valerian) (not-eq parsnip yam) (not-eq quince leek) (not-eq quince mushroom) (not-eq quince parsnip) (not-eq quince ulluco) (not-eq quince valerian) (not-eq quince yam) (not-eq ted alice) (not-eq ted bob) (not-eq ted dave) (not-eq ted heidi) (not-eq ted kevin) (not-eq ted xena) (not-eq ulluco leek) (not-eq ulluco mushroom) (not-eq ulluco parsnip) (not-eq ulluco quince) (not-eq ulluco valerian) (not-eq ulluco yam) (not-eq valerian leek) (not-eq valerian mushroom) (not-eq valerian parsnip) (not-eq valerian quince) (not-eq valerian ulluco) (not-eq valerian yam) (not-eq xena alice) (not-eq xena bob) (not-eq xena dave) (not-eq xena heidi) (not-eq xena kevin) (not-eq xena ted) (not-eq yam leek) (not-eq yam mushroom) (not-eq yam parsnip) (not-eq yam quince) (not-eq yam ulluco) (not-eq yam valerian))\n    (:goal (and (assigned xena quince) (assigned heidi mushroom) (assigned kevin yam) (assigned alice valerian) (assigned dave ulluco) (assigned ted leek) (assigned bob parsnip)))\n)"}
