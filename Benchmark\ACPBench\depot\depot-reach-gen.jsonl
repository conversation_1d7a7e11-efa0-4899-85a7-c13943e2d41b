{"id": -2538497112112194281, "group": "reachable_atom_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 5 hoists, 3 depots, 2 trucks, 5 pallets, 2 distributors, 2 crates, numbered consecutively. Currently, pallet3, pallet4, pallet2, crate0, and pallet0 are clear; hoist0, hoist2, hoist3, and hoist4 are available; truck0 is at depot0, pallet4 is at distributor1, pallet0 is at depot0, pallet3 is at distributor0, hoist1 is at depot1, truck1 is at distributor1, hoist0 is at depot0, hoist3 is at distributor0, crate0 is at depot1, pallet1 is at depot1, pallet2 is at depot2, hoist4 is at distributor1, and hoist2 is at depot2; crate0 is on pallet1; hoist1 is lifting crate1. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at hoist3 depot1", "at hoist0 distributor1", "at hoist4 depot1", "at hoist1 depot0", "at hoist0 depot2", "at hoist1 distributor1", "at pallet1 depot2", "at pallet1 depot0", "at hoist0 distributor0", "at pallet3 depot1", "at pallet1 distributor1", "at pallet0 distributor1", "at pallet2 distributor0", "at pallet3 depot0", "at pallet2 depot1", "at hoist2 distributor0", "at hoist0 depot1", "at hoist3 depot2", "at pallet0 depot2", "at hoist3 depot0", "at pallet3 depot2", "at pallet2 depot0", "at hoist4 depot2", "at pallet4 depot0", "at hoist2 distributor1", "at pallet0 distributor0", "at pallet4 depot1", "at pallet4 depot2", "at pallet3 distributor1", "at pallet4 distributor0", "at hoist2 depot0", "at hoist4 depot0", "at pallet1 distributor0", "at pallet2 distributor1", "at hoist1 distributor0", "at hoist3 distributor1", "at hoist1 depot2", "at hoist2 depot1", "at pallet0 depot1", "at hoist4 distributor0"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-3-2-2-5-5-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - crate depot0 depot1 depot2 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 - pallet truck0 truck1 - truck)\n    (:init (at crate0 depot1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 distributor0) (at hoist4 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 distributor0) (at pallet4 distributor1) (at truck0 depot0) (at truck1 distributor1) (available hoist0) (available hoist2) (available hoist3) (available hoist4) (clear crate0) (clear pallet0) (clear pallet2) (clear pallet3) (clear pallet4) (lifting hoist1 crate1) (on crate0 pallet1))\n    (:goal (and (on crate0 pallet1) (on crate1 pallet2)))\n)"}
{"id": -7365428780853021873, "group": "reachable_atom_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 6 hoists, 4 depots, 2 trucks, 6 pallets, 2 distributors, 3 crates, numbered consecutively. Currently, pallet3, pallet4, pallet1, pallet2, pallet5, and pallet0 are clear; hoist5, hoist0, hoist2, hoist3, hoist4, and hoist1 are available; hoist5 is at distributor1, pallet5 is at distributor1, pallet0 is at depot0, hoist1 is at depot1, hoist0 is at depot0, truck1 is at depot0, pallet1 is at depot1, pallet2 is at depot2, truck0 is at depot1, hoist3 is at depot3, pallet4 is at distributor0, hoist2 is at depot2, pallet3 is at depot3, and hoist4 is at distributor0; crate2 is in truck0, crate0 is in truck0, and crate1 is in truck0. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at hoist0 distributor0", "at hoist3 depot0", "at pallet0 depot1", "at pallet0 distributor0", "at hoist4 depot1", "at pallet2 depot1", "at pallet2 distributor1", "at hoist5 distributor0", "at pallet5 depot0", "at pallet4 distributor1", "at hoist4 depot2", "at hoist1 depot3", "at hoist0 depot2", "at pallet1 distributor0", "at pallet2 distributor0", "at pallet2 depot3", "at hoist3 depot1", "at pallet3 depot0", "at hoist2 depot0", "at pallet3 depot1", "at hoist0 depot1", "at hoist3 distributor0", "at hoist4 depot0", "at hoist5 depot1", "at pallet3 distributor1", "at hoist5 depot3", "at pallet1 depot0", "at hoist4 distributor1", "at pallet1 distributor1", "at hoist5 depot2", "at pallet5 depot3", "at hoist2 depot1", "at pallet5 distributor0", "at pallet5 depot1", "at pallet5 depot2", "at pallet3 distributor0", "at hoist1 distributor0", "at pallet2 depot0", "at hoist2 distributor1", "at pallet0 depot2", "at hoist3 depot2", "at hoist4 depot3", "at pallet0 distributor1", "at hoist0 depot3", "at pallet1 depot2", "at pallet4 depot3", "at hoist1 distributor1", "at pallet0 depot3", "at hoist2 distributor0", "at pallet4 depot2"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-4-2-2-6-6-3)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 crate2 - crate depot0 depot1 depot2 depot3 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 - pallet truck0 truck1 - truck)\n    (:init (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 distributor0) (at hoist5 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 distributor0) (at pallet5 distributor1) (at truck0 depot1) (at truck1 depot0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet3) (clear pallet4) (clear pallet5) (in crate0 truck0) (in crate1 truck0) (in crate2 truck0))\n    (:goal (and (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4)))\n)"}
{"id": 7785091969936518374, "group": "reachable_atom_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 9 hoists, 7 depots, 2 trucks, 9 pallets, 2 distributors, 2 crates, numbered consecutively. Currently, pallet6, pallet4, pallet1, pallet2, pallet5, pallet8, pallet7, crate1, and pallet0 are clear; hoist5, hoist8, hoist0, hoist2, hoist3, hoist4, hoist6, and hoist7 are available; pallet7 is at distributor0, hoist5 is at depot5, hoist8 is at distributor1, pallet4 is at depot4, pallet0 is at depot0, hoist1 is at depot1, truck0 is at depot6, hoist6 is at depot6, hoist0 is at depot0, truck1 is at depot0, hoist7 is at distributor0, pallet5 is at depot5, pallet8 is at distributor1, pallet1 is at depot1, pallet2 is at depot2, hoist4 is at depot4, hoist3 is at depot3, pallet6 is at depot6, hoist2 is at depot2, crate1 is at depot3, and pallet3 is at depot3; crate1 is on pallet3; hoist1 is lifting crate0. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at hoist1 depot2", "at pallet0 distributor1", "at pallet7 depot5", "at pallet3 depot0", "at hoist2 depot1", "at hoist2 depot3", "at hoist3 depot5", "at hoist4 depot2", "at pallet5 depot6", "at pallet6 depot4", "at pallet4 depot5", "at hoist2 depot6", "at hoist1 depot3", "at pallet4 distributor1", "at pallet5 depot0", "at pallet4 depot6", "at pallet6 depot0", "at hoist4 distributor1", "at hoist4 depot1", "at hoist5 depot4", "at pallet2 depot0", "at hoist0 depot4", "at hoist4 depot5", "at hoist2 depot0", "at hoist5 distributor0", "at hoist2 depot4", "at hoist0 depot2", "at hoist6 depot0", "at hoist6 depot5", "at pallet1 depot0", "at pallet8 depot3", "at pallet8 depot0", "at pallet2 depot3", "at pallet0 depot5", "at pallet2 depot1", "at hoist3 distributor0", "at hoist3 depot4", "at hoist0 distributor0", "at pallet2 depot6", "at pallet6 depot1", "at pallet1 distributor1", "at hoist7 distributor1", "at pallet4 depot1", "at hoist0 depot3", "at pallet1 depot2", "at hoist0 depot1", "at hoist0 depot6", "at hoist6 distributor1", "at pallet4 distributor0", "at hoist8 depot1"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-7-2-2-9-9-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 - pallet truck0 truck1 - truck)\n    (:init (at crate1 depot3) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 distributor0) (at hoist8 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 distributor0) (at pallet8 distributor1) (at truck0 depot6) (at truck1 depot0) (available hoist0) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (clear crate1) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (lifting hoist1 crate0) (on crate1 pallet3))\n    (:goal (and (on crate0 pallet8) (on crate1 pallet3)))\n)"}
{"id": -5082976085483993283, "group": "reachable_atom_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 6 hoists, 4 depots, 2 trucks, 6 pallets, 2 distributors, 3 crates, numbered consecutively. Currently, pallet3, pallet4, pallet1, pallet2, pallet5, and pallet0 are clear; hoist0, hoist2, hoist3, and hoist4 are available; hoist5 is at distributor1, pallet5 is at distributor1, pallet0 is at depot0, hoist1 is at depot1, hoist4 is at distributor0, hoist0 is at depot0, truck1 is at depot1, truck0 is at distributor1, pallet1 is at depot1, pallet2 is at depot2, hoist3 is at depot3, pallet4 is at distributor0, hoist2 is at depot2, and pallet3 is at depot3; crate0 is in truck1; hoist5 is lifting crate1 and hoist1 is lifting crate2. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at hoist5 depot0", "at pallet5 depot3", "at hoist3 depot1", "at hoist0 distributor1", "at hoist4 depot2", "at pallet0 distributor1", "at hoist4 depot0", "at pallet3 depot2", "at pallet2 distributor1", "at pallet2 depot3", "at hoist0 depot1", "at hoist2 distributor0", "at pallet0 depot3", "at hoist3 depot2", "at pallet5 depot2", "at pallet1 distributor0", "at pallet1 depot0", "at hoist3 depot0", "at hoist4 depot3", "at hoist5 distributor0", "at hoist5 depot2", "at hoist2 depot1", "at hoist2 depot0", "at hoist4 depot1", "at pallet3 depot0", "at pallet0 distributor0", "at pallet2 depot1", "at pallet5 distributor0", "at pallet3 distributor0", "at hoist0 distributor0", "at pallet3 distributor1", "at hoist5 depot1", "at hoist5 depot3", "at pallet5 depot1", "at hoist0 depot3", "at pallet1 distributor1", "at pallet4 depot2", "at hoist3 distributor1", "at hoist2 depot3", "at pallet1 depot2", "at pallet2 depot0", "at pallet4 depot1", "at pallet2 distributor0", "at hoist1 depot2", "at pallet0 depot1", "at hoist2 distributor1", "at pallet0 depot2", "at pallet4 distributor1", "at hoist3 distributor0", "at hoist1 depot3"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-4-2-2-6-6-3)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 crate2 - crate depot0 depot1 depot2 depot3 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 - pallet truck0 truck1 - truck)\n    (:init (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 distributor0) (at hoist5 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 distributor0) (at pallet5 distributor1) (at truck0 distributor1) (at truck1 depot1) (available hoist0) (available hoist2) (available hoist3) (available hoist4) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet3) (clear pallet4) (clear pallet5) (in crate0 truck1) (lifting hoist1 crate2) (lifting hoist5 crate1))\n    (:goal (and (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4)))\n)"}
{"id": 7275217234086006669, "group": "reachable_atom_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 6 hoists, 4 depots, 2 trucks, 6 pallets, 2 distributors, 3 crates, numbered consecutively. Currently, pallet3, pallet4, pallet1, pallet2, pallet5, and pallet0 are clear; hoist5, hoist0, hoist2, hoist3, and hoist4 are available; hoist5 is at distributor1, pallet5 is at distributor1, pallet0 is at depot0, hoist1 is at depot1, hoist0 is at depot0, truck1 is at depot1, pallet1 is at depot1, pallet2 is at depot2, truck0 is at depot1, hoist3 is at depot3, pallet4 is at distributor0, hoist2 is at depot2, pallet3 is at depot3, and hoist4 is at distributor0; crate2 is in truck0 and crate1 is in truck0; hoist1 is lifting crate0. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at pallet3 distributor1", "at pallet2 depot3", "at pallet1 distributor1", "at pallet0 distributor0", "at hoist1 depot2", "at pallet3 depot2", "at hoist3 depot2", "at hoist1 distributor1", "at hoist5 depot1", "at hoist2 depot3", "at pallet5 depot1", "at hoist4 depot2", "at hoist2 distributor1", "at pallet1 depot3", "at pallet4 distributor1", "at pallet0 depot1", "at pallet1 depot0", "at hoist4 depot1", "at hoist5 depot3", "at pallet2 distributor0", "at hoist0 distributor1", "at hoist4 depot3", "at hoist5 depot0", "at hoist0 depot2", "at pallet3 depot0", "at pallet2 distributor1", "at hoist2 depot1", "at pallet5 depot3", "at pallet5 depot2", "at pallet1 depot2", "at pallet4 depot3", "at hoist3 distributor1", "at pallet0 depot2", "at hoist3 distributor0", "at pallet1 distributor0", "at hoist5 depot2", "at hoist2 distributor0", "at pallet5 depot0", "at pallet4 depot1", "at pallet0 distributor1", "at hoist1 depot3", "at hoist4 depot0", "at hoist0 depot1", "at hoist0 distributor0", "at pallet2 depot1", "at hoist4 distributor1", "at pallet0 depot3", "at pallet2 depot0", "at hoist3 depot1", "at hoist5 distributor0"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-4-2-2-6-6-3)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 crate2 - crate depot0 depot1 depot2 depot3 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 - pallet truck0 truck1 - truck)\n    (:init (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 distributor0) (at hoist5 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 distributor0) (at pallet5 distributor1) (at truck0 depot1) (at truck1 depot1) (available hoist0) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet3) (clear pallet4) (clear pallet5) (in crate1 truck0) (in crate2 truck0) (lifting hoist1 crate0))\n    (:goal (and (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4)))\n)"}
{"id": 4618629709879465997, "group": "reachable_atom_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 4 hoists, 2 depots, 2 trucks, 4 pallets, 2 distributors, 2 crates, numbered consecutively. Currently, pallet3, pallet1, pallet2, and pallet0 are clear; hoist0, hoist2, hoist3, and hoist1 are available; pallet0 is at depot0, hoist1 is at depot1, hoist0 is at depot0, pallet2 is at distributor0, truck1 is at depot0, hoist2 is at distributor0, pallet1 is at depot1, truck0 is at depot1, pallet3 is at distributor1, and hoist3 is at distributor1; crate0 is in truck0 and crate1 is in truck1. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at hoist3 depot1", "at hoist0 depot1", "at pallet2 depot1", "at hoist3 depot0", "at pallet2 depot0", "at hoist0 distributor1", "at hoist2 distributor1", "at pallet0 distributor0", "at hoist1 distributor1", "at pallet1 depot0", "at hoist0 distributor0", "at pallet3 depot1", "at pallet3 distributor0", "at hoist2 depot0", "at pallet1 distributor1", "at pallet0 distributor1", "at hoist3 distributor0", "at pallet1 distributor0", "at pallet3 depot0", "at pallet2 distributor1", "at hoist1 distributor0", "at pallet0 depot1", "at hoist2 depot1", "at hoist1 depot0"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-2-2-2-4-4-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - crate depot0 depot1 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 - hoist pallet0 pallet1 pallet2 pallet3 - pallet truck0 truck1 - truck)\n    (:init (at hoist0 depot0) (at hoist1 depot1) (at hoist2 distributor0) (at hoist3 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 distributor0) (at pallet3 distributor1) (at truck0 depot1) (at truck1 depot0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet3) (in crate0 truck0) (in crate1 truck1))\n    (:goal (and (on crate0 crate1) (on crate1 pallet0)))\n)"}
{"id": -5065828067504112767, "group": "reachable_atom_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 5 hoists, 3 depots, 2 trucks, 5 pallets, 2 distributors, 2 crates, numbered consecutively. Currently, pallet3, pallet4, pallet2, crate0, and pallet0 are clear; hoist2, hoist3, hoist4, and hoist1 are available; pallet4 is at distributor1, pallet0 is at depot0, pallet3 is at distributor0, hoist1 is at depot1, hoist0 is at depot0, hoist3 is at distributor0, crate0 is at depot1, pallet1 is at depot1, pallet2 is at depot2, truck0 is at depot1, hoist4 is at distributor1, truck1 is at depot2, and hoist2 is at depot2; crate0 is on pallet1; hoist0 is lifting crate1. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at hoist3 depot1", "at hoist0 distributor1", "at hoist4 depot1", "at hoist1 depot0", "at hoist0 depot2", "at hoist1 distributor1", "at pallet1 depot2", "at pallet1 depot0", "at hoist0 distributor0", "at pallet3 depot1", "at pallet1 distributor1", "at pallet0 distributor1", "at pallet2 distributor0", "at pallet3 depot0", "at pallet2 depot1", "at hoist2 distributor0", "at hoist0 depot1", "at hoist3 depot2", "at pallet0 depot2", "at hoist3 depot0", "at pallet3 depot2", "at pallet2 depot0", "at hoist4 depot2", "at pallet4 depot0", "at hoist2 distributor1", "at pallet0 distributor0", "at pallet4 depot1", "at pallet4 depot2", "at pallet3 distributor1", "at pallet4 distributor0", "at hoist2 depot0", "at hoist4 depot0", "at pallet1 distributor0", "at pallet2 distributor1", "at hoist1 distributor0", "at hoist3 distributor1", "at hoist1 depot2", "at hoist2 depot1", "at pallet0 depot1", "at hoist4 distributor0"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-3-2-2-5-5-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - crate depot0 depot1 depot2 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 - pallet truck0 truck1 - truck)\n    (:init (at crate0 depot1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 distributor0) (at hoist4 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 distributor0) (at pallet4 distributor1) (at truck0 depot1) (at truck1 depot2) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (clear crate0) (clear pallet0) (clear pallet2) (clear pallet3) (clear pallet4) (lifting hoist0 crate1) (on crate0 pallet1))\n    (:goal (and (on crate0 pallet1) (on crate1 pallet2)))\n)"}
{"id": 1579520414496344928, "group": "reachable_atom_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 9 hoists, 7 depots, 2 trucks, 9 pallets, 2 distributors, 2 crates, numbered consecutively. Currently, pallet6, pallet4, pallet1, pallet2, pallet5, pallet8, pallet7, crate1, and pallet0 are clear; hoist5, hoist8, hoist0, hoist2, hoist3, hoist4, hoist6, hoist1, and hoist7 are available; pallet7 is at distributor0, hoist5 is at depot5, hoist8 is at distributor1, pallet4 is at depot4, truck1 is at depot6, pallet0 is at depot0, hoist1 is at depot1, hoist6 is at depot6, hoist0 is at depot0, truck0 is at depot5, hoist7 is at distributor0, pallet5 is at depot5, pallet8 is at distributor1, pallet1 is at depot1, pallet2 is at depot2, hoist4 is at depot4, hoist3 is at depot3, pallet6 is at depot6, hoist2 is at depot2, crate1 is at depot3, and pallet3 is at depot3; crate1 is on pallet3; crate0 is in truck1. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at pallet4 depot0", "at hoist5 depot0", "at hoist8 depot6", "at hoist0 depot2", "at pallet4 depot1", "at hoist0 depot3", "at hoist6 distributor1", "at pallet3 depot5", "at hoist8 depot5", "at hoist5 depot2", "at hoist4 depot5", "at pallet0 distributor1", "at pallet7 depot4", "at hoist3 distributor0", "at hoist6 depot3", "at pallet6 depot2", "at pallet5 distributor1", "at pallet4 depot5", "at pallet6 depot3", "at pallet8 depot2", "at pallet5 depot3", "at pallet0 depot5", "at pallet4 depot3", "at pallet2 depot6", "at hoist8 distributor0", "at hoist3 distributor1", "at hoist3 depot2", "at pallet7 depot5", "at hoist4 depot1", "at hoist6 depot5", "at hoist7 depot4", "at hoist1 depot3", "at pallet6 distributor0", "at pallet7 depot3", "at hoist4 distributor1", "at hoist5 depot6", "at pallet3 distributor0", "at hoist7 distributor1", "at hoist8 depot2", "at hoist5 depot1", "at pallet2 depot1", "at pallet3 distributor1", "at pallet7 depot2", "at hoist7 depot0", "at pallet7 distributor1", "at hoist3 depot4", "at hoist7 depot6", "at hoist0 depot5", "at hoist2 depot5", "at hoist6 depot2"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-7-2-2-9-9-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 - pallet truck0 truck1 - truck)\n    (:init (at crate1 depot3) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 distributor0) (at hoist8 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 distributor0) (at pallet8 distributor1) (at truck0 depot5) (at truck1 depot6) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (clear crate1) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (in crate0 truck1) (on crate1 pallet3))\n    (:goal (and (on crate0 pallet8) (on crate1 pallet3)))\n)"}
{"id": 6214331537637380590, "group": "reachable_atom_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 9 hoists, 7 depots, 2 trucks, 9 pallets, 2 distributors, 2 crates, numbered consecutively. Currently, pallet6, pallet4, pallet1, pallet2, pallet5, pallet8, pallet7, crate1, and pallet0 are clear; hoist5, hoist8, hoist0, hoist2, hoist3, hoist4, hoist6, hoist1, and hoist7 are available; pallet7 is at distributor0, hoist5 is at depot5, hoist8 is at distributor1, pallet4 is at depot4, pallet0 is at depot0, hoist1 is at depot1, hoist6 is at depot6, hoist0 is at depot0, hoist7 is at distributor0, pallet5 is at depot5, pallet8 is at distributor1, pallet1 is at depot1, pallet2 is at depot2, truck0 is at depot1, hoist4 is at depot4, hoist3 is at depot3, pallet6 is at depot6, truck1 is at distributor0, hoist2 is at depot2, crate1 is at depot3, and pallet3 is at depot3; crate1 is on pallet3; crate0 is in truck0. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at hoist6 distributor0", "at hoist3 depot2", "at hoist7 depot4", "at pallet1 depot2", "at hoist2 depot6", "at pallet1 depot6", "at hoist0 depot1", "at hoist8 depot4", "at hoist1 distributor1", "at pallet2 depot6", "at pallet5 distributor0", "at pallet4 depot3", "at pallet1 depot0", "at pallet0 distributor1", "at pallet8 depot5", "at hoist7 depot2", "at hoist2 distributor0", "at hoist1 depot2", "at pallet3 distributor0", "at hoist7 depot6", "at pallet6 depot4", "at hoist4 depot0", "at hoist5 depot4", "at hoist7 depot1", "at hoist0 depot6", "at pallet5 depot0", "at hoist5 distributor0", "at pallet4 depot5", "at hoist8 depot6", "at pallet6 depot1", "at pallet4 distributor0", "at hoist1 depot3", "at pallet3 depot6", "at hoist4 depot2", "at hoist6 depot1", "at pallet2 depot3", "at hoist3 distributor1", "at pallet3 depot2", "at hoist6 distributor1", "at pallet1 distributor0", "at pallet1 distributor1", "at hoist8 depot3", "at pallet6 depot3", "at pallet0 depot3", "at hoist7 depot5", "at hoist2 depot3", "at pallet7 depot2", "at pallet5 depot6", "at pallet7 depot4", "at hoist5 distributor1"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-7-2-2-9-9-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 - pallet truck0 truck1 - truck)\n    (:init (at crate1 depot3) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 distributor0) (at hoist8 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 distributor0) (at pallet8 distributor1) (at truck0 depot1) (at truck1 distributor0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (clear crate1) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (in crate0 truck0) (on crate1 pallet3))\n    (:goal (and (on crate0 pallet8) (on crate1 pallet3)))\n)"}
{"id": -4124983001843205153, "group": "reachable_atom_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 9 hoists, 7 depots, 2 trucks, 9 pallets, 2 distributors, 2 crates, numbered consecutively. Currently, pallet6, pallet4, pallet2, pallet5, pallet8, pallet7, crate0, crate1, and pallet0 are clear; hoist5, hoist8, hoist0, hoist2, hoist3, hoist4, hoist6, hoist1, and hoist7 are available; pallet7 is at distributor0, hoist5 is at depot5, hoist8 is at distributor1, pallet4 is at depot4, pallet0 is at depot0, hoist1 is at depot1, hoist6 is at depot6, hoist0 is at depot0, hoist7 is at distributor0, pallet5 is at depot5, truck0 is at distributor1, pallet8 is at distributor1, truck1 is at depot5, crate0 is at depot1, pallet1 is at depot1, pallet2 is at depot2, hoist4 is at depot4, hoist3 is at depot3, pallet6 is at depot6, crate1 is at depot3, hoist2 is at depot2, and pallet3 is at depot3; crate0 is on pallet1 and crate1 is on pallet3. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at hoist5 depot0", "at pallet1 depot0", "at hoist1 distributor1", "at pallet6 depot3", "at hoist1 depot2", "at hoist0 distributor0", "at pallet7 depot6", "at pallet4 depot6", "at hoist4 depot3", "at pallet0 depot6", "at hoist0 distributor1", "at hoist2 distributor1", "at pallet0 distributor1", "at hoist6 depot0", "at hoist6 depot2", "at hoist1 depot0", "at hoist2 depot6", "at hoist5 distributor1", "at pallet5 depot1", "at hoist0 depot4", "at hoist7 depot2", "at pallet6 depot5", "at pallet5 distributor1", "at pallet0 depot3", "at pallet7 distributor1", "at hoist4 distributor0", "at hoist5 depot4", "at pallet8 depot2", "at hoist8 depot5", "at pallet2 depot3", "at hoist2 depot1", "at pallet7 depot2", "at hoist1 distributor0", "at pallet5 distributor0", "at hoist4 depot5", "at pallet3 depot5", "at hoist4 depot2", "at hoist3 depot6", "at hoist5 depot1", "at pallet6 depot0", "at pallet1 depot3", "at hoist7 depot6", "at hoist1 depot5", "at pallet8 depot1", "at hoist0 depot2", "at pallet7 depot4", "at hoist5 distributor0", "at hoist8 depot2", "at pallet1 distributor0", "at pallet3 distributor1"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-7-2-2-9-9-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 - pallet truck0 truck1 - truck)\n    (:init (at crate0 depot1) (at crate1 depot3) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 distributor0) (at hoist8 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 distributor0) (at pallet8 distributor1) (at truck0 distributor1) (at truck1 depot5) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (clear crate0) (clear crate1) (clear pallet0) (clear pallet2) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (on crate0 pallet1) (on crate1 pallet3))\n    (:goal (and (on crate0 pallet8) (on crate1 pallet3)))\n)"}
