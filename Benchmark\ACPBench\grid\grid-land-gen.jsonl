{"id": -825906638400504005, "group": "landmarks_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 0 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f3-2f and its arm is empty. All the positions are open except the following: f3-4f has shape1 shaped lock, f2-1f has shape1 shaped lock. Key key1-1 is at position f4-3f. Key key1-0 is at position f1-0f. The goal is to reach a state where the following facts hold: Key key1-1 is at f4-4f location and Key key1-0 is at f1-0f location. The available propositions are: (at ?r ?x) - Key ?r is at ?x location, (at-robot ?x) - Robot is at ?x location, (locked ?x) - Location ?x is locked, (holding ?k) - Robot is holding ?k, (open ?x) - Location ?x is open, and (arm-empty) - Robot's arm is empty.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at key1-1 f0-4f)", "(at key1-0 f4-1f)", "(at key1-0 f0-0f)", "(at key1-0 f3-2f)", "(at-robot f1-2f)", "(open f2-1f)", "(at key1-0 f3-0f)", "(holding key1-0)", "(at key1-0 f3-1f)", "(at key1-1 f4-1f)", "(at key1-1 f1-2f)", "(at-robot f0-2f)", "(at-robot f0-1f)", "(at-robot f2-4f)", "(at key1-1 f0-1f)", "(at key1-0 f0-3f)", "(at key1-1 f0-2f)", "(at key1-1 f2-4f)", "(at-robot f3-0f)", "(at-robot f0-4f)", "(at-robot f0-0f)", "(at key1-0 f2-4f)", "(at key1-1 f3-1f)", "(at key1-1 f1-1f)", "(at-robot f3-3f)", "(at key1-1 f4-2f)", "(at key1-1 f3-0f)", "(at key1-0 f3-3f)", "(at key1-0 f4-2f)", "(at key1-0 f4-3f)", "(at key1-1 f0-3f)", "(at key1-0 f3-4f)", "(at-robot f3-1f)", "(at key1-0 f2-1f)", "(at key1-1 f3-3f)", "(at key1-1 f2-3f)", "(at-robot f3-4f)", "(at key1-0 f4-0f)", "(at key1-0 f0-1f)", "(at key1-1 f1-0f)", "(at-robot f1-3f)", "(at key1-0 f1-4f)", "(at-robot f4-2f)", "(at-robot f4-0f)", "(at key1-1 f3-4f)", "(at-robot f1-1f)", "(at-robot f4-1f)", "(at key1-0 f2-0f)", "(at-robot f2-2f)", "(at key1-0 f4-4f)", "(at key1-0 f1-2f)", "(open f3-4f)", "(at key1-1 f1-4f)", "(at key1-1 f3-2f)", "(at key1-0 f2-2f)", "(at-robot f2-1f)", "(at-robot f1-4f)", "(at key1-0 f0-2f)", "(at-robot f2-0f)", "(at key1-1 f2-2f)", "(at key1-0 f2-3f)", "(at key1-1 f4-0f)", "(at key1-1 f0-0f)", "(at-robot f2-3f)", "(at key1-0 f0-4f)", "(at-robot f0-3f)", "(at key1-1 f2-0f)", "(at key1-0 f1-1f)", "(at-robot f1-0f)", "(at key1-0 f1-3f)", "(at key1-1 f1-3f)", "(at key1-1 f2-1f)"], "yes": ["(at-robot f4-4f)", "(holding key1-1)", "(at-robot f4-3f)"]}, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t2-k2-l2-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key1-0 key1-1 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 shape1 - shape)\n    (:init (arm-empty) (at key1-0 f1-0f) (at key1-1 f4-3f) (at-robot f3-2f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (key-shape key1-0 shape1) (key-shape key1-1 shape1) (lock-shape f2-1f shape1) (lock-shape f3-4f shape1) (locked f2-1f) (locked f3-4f) (open f0-0f) (open f0-1f) (open f0-2f) (open f0-3f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-0f) (open f2-2f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f4-0f) (open f4-1f) (open f4-2f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key1-0 f1-0f) (at key1-1 f4-4f)))\n)"}
{"id": 1725040956209329983, "group": "landmarks_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f2-2f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-4f has shape0 shaped lock, f4-0f has shape0 shaped lock. Key key0-0 is at position f2-2f. Key key0-2 is at position f1-4f. Key key0-1 is at position f1-2f. The goal is to reach a state where the following facts hold: Key key0-0 is at f2-2f location, Key key0-1 is at f3-2f location, and Key key0-2 is at f1-4f location. The available propositions are: (at ?r ?x) - Key ?r is at ?x location, (at-robot ?x) - Robot is at ?x location, (locked ?x) - Location ?x is locked, (holding ?k) - Robot is holding ?k, (open ?x) - Location ?x is open, and (arm-empty) - Robot's arm is empty.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at key0-2 f2-0f)", "(at key0-1 f1-0f)", "(at key0-0 f3-2f)", "(at-robot f2-4f)", "(at-robot f3-0f)", "(at-robot f0-0f)", "(at key0-1 f0-0f)", "(at key0-2 f0-3f)", "(at key0-2 f4-1f)", "(at key0-0 f2-0f)", "(at key0-1 f3-4f)", "(at key0-1 f3-1f)", "(at-robot f3-4f)", "(at-robot f4-3f)", "(at-robot f4-0f)", "(at key0-1 f0-1f)", "(at key0-0 f0-0f)", "(at key0-2 f1-3f)", "(at-robot f2-1f)", "(at key0-0 f0-3f)", "(at key0-2 f4-0f)", "(at key0-0 f0-2f)", "(at key0-0 f1-0f)", "(at key0-1 f2-0f)", "(at key0-1 f2-1f)", "(at key0-1 f2-4f)", "(at key0-2 f1-0f)", "(at key0-2 f2-4f)", "(at key0-1 f4-4f)", "(at key0-0 f2-3f)", "(at key0-1 f0-3f)", "(at-robot f1-3f)", "(open f4-4f)", "(at-robot f1-1f)", "(holding key0-2)", "(at key0-1 f1-4f)", "(at key0-1 f3-0f)", "(at key0-0 f1-3f)", "(at key0-0 f3-1f)", "(at key0-2 f1-1f)", "(at key0-1 f3-3f)", "(at key0-2 f0-1f)", "(at key0-2 f3-4f)", "(open f0-3f)", "(at key0-0 f2-1f)", "(at key0-1 f4-2f)", "(at key0-0 f2-4f)", "(at key0-2 f4-4f)", "(at key0-2 f4-3f)", "(at-robot f0-1f)", "(at-robot f0-4f)", "(at-robot f3-3f)", "(at key0-0 f0-1f)", "(at key0-0 f3-0f)", "(at key0-0 f1-1f)", "(at key0-1 f2-3f)", "(at key0-0 f1-4f)", "(at-robot f3-1f)", "(at key0-2 f1-2f)", "(at key0-2 f3-3f)", "(at key0-1 f1-3f)", "(at-robot f4-4f)", "(open f4-0f)", "(at-robot f4-1f)", "(at key0-2 f0-4f)", "(at key0-0 f4-2f)", "(at key0-1 f4-1f)", "(at-robot f1-4f)", "(at key0-0 f3-4f)", "(at key0-1 f1-1f)", "(at key0-0 f0-4f)", "(at key0-0 f4-4f)", "(at key0-2 f2-3f)", "(at key0-2 f2-2f)", "(at key0-2 f3-0f)", "(at key0-2 f3-1f)", "(at-robot f0-2f)", "(at key0-0 f4-0f)", "(at key0-2 f4-2f)", "(holding key0-0)", "(at key0-2 f0-2f)", "(at key0-2 f0-0f)", "(at key0-1 f0-4f)", "(at key0-1 f0-2f)", "(at-robot f4-2f)", "(at key0-1 f4-3f)", "(at key0-2 f2-1f)", "(at key0-0 f4-3f)", "(at key0-2 f3-2f)", "(at key0-1 f2-2f)", "(at key0-1 f4-0f)", "(at-robot f2-0f)", "(at key0-0 f1-2f)", "(at-robot f2-3f)", "(at-robot f0-3f)", "(at key0-0 f3-3f)", "(at-robot f1-0f)", "(at key0-0 f4-1f)"], "yes": ["(holding key0-1)", "(at-robot f3-2f)", "(at-robot f1-2f)"]}, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k3-l3-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 key0-2 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (arm-empty) (at key0-0 f2-2f) (at key0-1 f1-2f) (at key0-2 f1-4f) (at-robot f2-2f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (key-shape key0-2 shape0) (lock-shape f0-3f shape0) (lock-shape f4-0f shape0) (lock-shape f4-4f shape0) (locked f0-3f) (locked f4-0f) (locked f4-4f) (open f0-0f) (open f0-1f) (open f0-2f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-0f) (open f2-1f) (open f2-2f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-1f) (open f4-2f) (open f4-3f))\n    (:goal (and (at key0-0 f2-2f) (at key0-1 f3-2f) (at key0-2 f1-4f)))\n)"}
{"id": 8611879225970411197, "group": "landmarks_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f3-2f and its arm is empty. All the positions are open except the following: f0-1f has shape0 shaped lock. Key key0-0 is at position f2-2f. Key key0-1 is at position f4-1f. The goal is to reach a state where the following facts hold: Key key0-0 is at f3-1f location and Key key0-1 is at f4-1f location. The available propositions are: (at ?r ?x) - Key ?r is at ?x location, (at-robot ?x) - Robot is at ?x location, (locked ?x) - Location ?x is locked, (holding ?k) - Robot is holding ?k, (open ?x) - Location ?x is open, and (arm-empty) - Robot's arm is empty.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at key0-0 f2-1f)", "(at-robot f1-2f)", "(at key0-1 f4-2f)", "(at key0-0 f2-4f)", "(at key0-1 f1-0f)", "(at-robot f0-2f)", "(at key0-0 f3-2f)", "(at key0-1 f4-4f)", "(at-robot f2-4f)", "(at-robot f0-1f)", "(at key0-1 f3-2f)", "(at key0-0 f4-0f)", "(holding key0-1)", "(at-robot f3-0f)", "(at-robot f0-4f)", "(at-robot f0-0f)", "(at key0-0 f2-3f)", "(at-robot f3-3f)", "(at key0-0 f0-1f)", "(at key0-0 f3-0f)", "(at key0-0 f1-1f)", "(at key0-1 f2-3f)", "(at key0-1 f0-0f)", "(at key0-1 f0-4f)", "(at key0-0 f1-4f)", "(at key0-0 f2-0f)", "(at key0-1 f3-4f)", "(at key0-1 f3-1f)", "(at key0-1 f0-3f)", "(at-robot f3-4f)", "(at key0-1 f0-2f)", "(at key0-1 f1-3f)", "(at-robot f1-3f)", "(at-robot f4-3f)", "(at-robot f4-4f)", "(at-robot f4-2f)", "(at-robot f4-0f)", "(at key0-1 f4-3f)", "(at key0-0 f4-3f)", "(locked f2-1f)", "(at-robot f1-1f)", "(at key0-1 f0-1f)", "(at key0-1 f1-2f)", "(at key0-1 f1-4f)", "(at-robot f4-1f)", "(at key0-1 f3-0f)", "(open f0-1f)", "(at key0-1 f2-2f)", "(at key0-0 f4-2f)", "(at key0-0 f0-0f)", "(at key0-0 f1-3f)", "(at key0-1 f4-0f)", "(at-robot f2-1f)", "(at key0-0 f0-3f)", "(at-robot f1-4f)", "(at-robot f2-0f)", "(at key0-0 f0-2f)", "(at key0-1 f3-3f)", "(at key0-0 f3-4f)", "(at key0-0 f1-2f)", "(at key0-0 f1-0f)", "(at key0-1 f1-1f)", "(at-robot f2-3f)", "(at key0-1 f2-0f)", "(at key0-0 f0-4f)", "(at-robot f0-3f)", "(at key0-0 f4-4f)", "(at key0-1 f2-1f)", "(at key0-0 f3-3f)", "(at key0-1 f2-4f)", "(at-robot f1-0f)", "(at key0-0 f4-1f)"], "yes": ["(at-robot f3-1f)", "(holding key0-0)", "(at-robot f2-2f)"]}, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k2-l2-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (arm-empty) (at key0-0 f2-2f) (at key0-1 f4-1f) (at-robot f3-2f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (lock-shape f0-1f shape0) (lock-shape f2-1f shape0) (locked f0-1f) (open f0-0f) (open f0-2f) (open f0-3f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-0f) (open f2-1f) (open f2-2f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-0f) (open f4-1f) (open f4-2f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key0-0 f3-1f) (at key0-1 f4-1f)))\n)"}
{"id": 8868927608328675600, "group": "landmarks_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f3-1f and is holding key0-0. All the positions are open except the following: f0-1f has shape0 shaped lock. Key key0-1 is at position f4-1f. The goal is to reach a state where the following facts hold: Key key0-0 is at f3-1f location and Key key0-1 is at f4-1f location. The available propositions are: (at ?r ?x) - Key ?r is at ?x location, (at-robot ?x) - Robot is at ?x location, (locked ?x) - Location ?x is locked, (holding ?k) - Robot is holding ?k, (open ?x) - Location ?x is open, and (arm-empty) - Robot is not holding anything.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at key0-0 f2-1f)", "(at-robot f1-2f)", "(at key0-1 f4-2f)", "(at key0-0 f2-4f)", "(at key0-1 f1-0f)", "(at-robot f0-2f)", "(at key0-0 f3-2f)", "(at key0-1 f4-4f)", "(at-robot f2-4f)", "(at-robot f0-1f)", "(at key0-1 f3-2f)", "(at key0-0 f4-0f)", "(at key0-0 f2-2f)", "(holding key0-1)", "(at-robot f3-0f)", "(at-robot f0-4f)", "(at-robot f0-0f)", "(at key0-0 f2-3f)", "(at-robot f3-3f)", "(at key0-0 f0-1f)", "(at key0-0 f3-0f)", "(at key0-0 f1-1f)", "(at key0-1 f2-3f)", "(at key0-1 f0-0f)", "(at key0-1 f0-4f)", "(at key0-0 f1-4f)", "(at key0-0 f2-0f)", "(at key0-1 f3-4f)", "(at key0-1 f3-1f)", "(at key0-1 f0-3f)", "(at-robot f3-4f)", "(at key0-1 f0-2f)", "(at key0-1 f1-3f)", "(at-robot f1-3f)", "(at-robot f4-3f)", "(at-robot f4-4f)", "(at-robot f4-2f)", "(at-robot f4-0f)", "(at key0-1 f4-3f)", "(at key0-0 f4-3f)", "(locked f2-1f)", "(at-robot f1-1f)", "(at key0-1 f0-1f)", "(at key0-1 f1-2f)", "(at key0-1 f1-4f)", "(at-robot f4-1f)", "(at key0-1 f3-0f)", "(at-robot f2-2f)", "(open f0-1f)", "(at key0-1 f2-2f)", "(at key0-0 f4-2f)", "(at key0-0 f0-0f)", "(at key0-0 f1-3f)", "(at key0-1 f4-0f)", "(at-robot f2-1f)", "(at key0-0 f0-3f)", "(at-robot f1-4f)", "(at-robot f2-0f)", "(at key0-0 f0-2f)", "(at key0-1 f3-3f)", "(at key0-0 f3-4f)", "(at key0-0 f1-2f)", "(at key0-0 f1-0f)", "(at key0-1 f1-1f)", "(at-robot f2-3f)", "(at key0-1 f2-0f)", "(at key0-0 f0-4f)", "(at-robot f0-3f)", "(at key0-0 f4-4f)", "(at key0-1 f2-1f)", "(at key0-0 f3-3f)", "(at key0-1 f2-4f)", "(at-robot f1-0f)", "(arm-empty)", "(at key0-0 f4-1f)", "(at-robot f3-2f)"], "yes": []}, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k2-l2-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (at key0-1 f4-1f) (at-robot f3-1f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (holding key0-0) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (lock-shape f0-1f shape0) (lock-shape f2-1f shape0) (locked f0-1f) (open f0-0f) (open f0-2f) (open f0-3f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-0f) (open f2-1f) (open f2-2f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-0f) (open f4-1f) (open f4-2f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key0-0 f3-1f) (at key0-1 f4-1f)))\n)"}
{"id": -4537200623399278007, "group": "landmarks_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f2-3f and its arm is empty. All the positions are open except the following: f2-0f has shape0 shaped lock, f4-2f has shape0 shaped lock. Key key0-1 is at position f1-2f. Key key0-2 is at position f0-4f. Key key0-0 is at position f3-3f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-2f location, Key key0-0 is at f3-3f location, and Key key0-2 is at f3-3f location. The available propositions are: (at ?r ?x) - Key ?r is at ?x location, (at-robot ?x) - Robot is at ?x location, (locked ?x) - Location ?x is locked, (holding ?k) - Robot is holding ?k, (open ?x) - Location ?x is open, and (arm-empty) - Robot's arm is empty.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at key0-2 f2-0f)", "(at key0-1 f1-0f)", "(at key0-0 f3-2f)", "(at-robot f2-4f)", "(at-robot f3-0f)", "(at-robot f0-0f)", "(at key0-2 f1-4f)", "(at key0-1 f0-0f)", "(at key0-2 f0-3f)", "(at key0-2 f4-1f)", "(at key0-0 f2-0f)", "(at key0-1 f3-4f)", "(at key0-1 f3-1f)", "(at-robot f3-4f)", "(at-robot f4-3f)", "(at-robot f4-0f)", "(at key0-1 f0-1f)", "(at-robot f2-2f)", "(at key0-0 f0-0f)", "(at key0-2 f1-3f)", "(at-robot f2-1f)", "(at key0-0 f0-3f)", "(at key0-2 f4-0f)", "(at key0-0 f0-2f)", "(at key0-0 f1-0f)", "(at key0-1 f2-0f)", "(at key0-1 f2-1f)", "(at key0-1 f2-4f)", "(at key0-2 f1-0f)", "(at-robot f3-2f)", "(at key0-2 f2-4f)", "(at key0-1 f4-4f)", "(at key0-0 f2-3f)", "(open f2-0f)", "(at key0-1 f0-3f)", "(at-robot f1-3f)", "(at-robot f1-1f)", "(at key0-1 f3-0f)", "(at key0-1 f1-4f)", "(at key0-0 f1-3f)", "(at key0-0 f3-1f)", "(at key0-2 f1-1f)", "(at key0-1 f3-3f)", "(locked f2-2f)", "(at key0-2 f0-1f)", "(at key0-2 f3-4f)", "(at key0-0 f2-1f)", "(at key0-1 f4-2f)", "(at key0-0 f2-4f)", "(at key0-2 f4-4f)", "(at key0-2 f4-3f)", "(at-robot f0-1f)", "(at key0-1 f3-2f)", "(at key0-0 f0-1f)", "(at key0-0 f3-0f)", "(at key0-0 f1-1f)", "(at key0-1 f2-3f)", "(at key0-0 f1-4f)", "(at-robot f3-1f)", "(at key0-2 f1-2f)", "(at key0-1 f1-3f)", "(at-robot f4-4f)", "(at-robot f4-1f)", "(at key0-0 f4-2f)", "(at key0-1 f4-1f)", "(at-robot f1-4f)", "(at key0-0 f3-4f)", "(at key0-1 f1-1f)", "(at key0-0 f0-4f)", "(at key0-0 f4-4f)", "(at key0-2 f2-3f)", "(at key0-2 f2-2f)", "(at key0-2 f3-0f)", "(at-robot f1-2f)", "(at key0-2 f3-1f)", "(at-robot f0-2f)", "(at key0-0 f4-0f)", "(at key0-0 f2-2f)", "(at key0-2 f4-2f)", "(holding key0-0)", "(holding key0-1)", "(at key0-2 f0-2f)", "(at key0-2 f0-0f)", "(at key0-1 f0-4f)", "(at key0-1 f0-2f)", "(at-robot f4-2f)", "(at key0-1 f4-3f)", "(at key0-2 f2-1f)", "(at key0-0 f4-3f)", "(at key0-2 f3-2f)", "(open f4-2f)", "(at key0-1 f2-2f)", "(at key0-1 f4-0f)", "(at-robot f2-0f)", "(at key0-0 f1-2f)", "(at-robot f0-3f)", "(at-robot f1-0f)", "(at key0-0 f4-1f)"], "yes": ["(holding key0-2)", "(at-robot f3-3f)", "(at-robot f0-4f)"]}, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k3-l3-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 key0-2 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (arm-empty) (at key0-0 f3-3f) (at key0-1 f1-2f) (at key0-2 f0-4f) (at-robot f2-3f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (key-shape key0-2 shape0) (lock-shape f2-0f shape0) (lock-shape f2-2f shape0) (lock-shape f4-2f shape0) (locked f2-0f) (locked f4-2f) (open f0-0f) (open f0-1f) (open f0-2f) (open f0-3f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-1f) (open f2-2f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-0f) (open f4-1f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key0-0 f3-3f) (at key0-1 f1-2f) (at key0-2 f3-3f)))\n)"}
{"id": 1812656314912770346, "group": "landmarks_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f1-4f and its arm is empty. All the positions are open except the following: f2-0f has shape0 shaped lock, f4-2f has shape0 shaped lock. Key key0-2 is at position f3-3f. Key key0-1 is at position f1-2f. Key key0-0 is at position f1-3f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-2f location, Key key0-0 is at f3-3f location, and Key key0-2 is at f3-3f location. The available propositions are: (at ?r ?x) - Key ?r is at ?x location, (at-robot ?x) - Robot is at ?x location, (locked ?x) - Location ?x is locked, (holding ?k) - Robot is holding ?k, (open ?x) - Location ?x is open, and (arm-empty) - Robot is not holding anything.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at key0-2 f2-0f)", "(at key0-1 f1-0f)", "(at key0-0 f3-2f)", "(at-robot f2-4f)", "(at-robot f3-0f)", "(at-robot f0-0f)", "(at key0-2 f1-4f)", "(at key0-1 f0-0f)", "(at key0-2 f0-3f)", "(at key0-2 f4-1f)", "(at key0-0 f2-0f)", "(at key0-1 f3-4f)", "(at key0-1 f3-1f)", "(at-robot f3-4f)", "(at-robot f4-3f)", "(at-robot f4-0f)", "(at key0-1 f0-1f)", "(at-robot f2-2f)", "(at key0-0 f0-0f)", "(at key0-2 f1-3f)", "(at-robot f2-1f)", "(at key0-0 f0-3f)", "(at key0-2 f4-0f)", "(at key0-0 f0-2f)", "(at key0-0 f1-0f)", "(at key0-1 f2-0f)", "(at key0-1 f2-1f)", "(at key0-1 f2-4f)", "(at key0-2 f1-0f)", "(at-robot f3-2f)", "(at key0-2 f2-4f)", "(at key0-1 f4-4f)", "(at key0-0 f2-3f)", "(open f2-0f)", "(at key0-1 f0-3f)", "(at-robot f1-1f)", "(holding key0-2)", "(at key0-1 f1-4f)", "(at key0-1 f3-0f)", "(at key0-0 f3-1f)", "(at key0-2 f1-1f)", "(at key0-1 f3-3f)", "(locked f2-2f)", "(at key0-2 f0-1f)", "(at key0-2 f3-4f)", "(at key0-0 f2-1f)", "(at key0-1 f4-2f)", "(at key0-0 f2-4f)", "(at key0-2 f4-4f)", "(at key0-2 f4-3f)", "(at-robot f0-1f)", "(at key0-1 f3-2f)", "(at-robot f0-4f)", "(at key0-0 f0-1f)", "(at key0-0 f3-0f)", "(at key0-0 f1-1f)", "(at key0-1 f2-3f)", "(at key0-0 f1-4f)", "(at-robot f3-1f)", "(at key0-2 f1-2f)", "(at key0-1 f1-3f)", "(at-robot f4-4f)", "(at-robot f4-1f)", "(at key0-2 f0-4f)", "(at key0-0 f4-2f)", "(at key0-1 f4-1f)", "(at key0-0 f3-4f)", "(at key0-1 f1-1f)", "(at key0-0 f0-4f)", "(at key0-0 f4-4f)", "(at key0-2 f2-3f)", "(at key0-2 f2-2f)", "(at key0-2 f3-0f)", "(at-robot f1-2f)", "(at key0-2 f3-1f)", "(at-robot f0-2f)", "(at key0-0 f4-0f)", "(at key0-0 f2-2f)", "(at key0-2 f4-2f)", "(holding key0-1)", "(at key0-2 f0-2f)", "(at key0-2 f0-0f)", "(at key0-1 f0-4f)", "(at key0-1 f0-2f)", "(at-robot f4-2f)", "(at key0-1 f4-3f)", "(at key0-2 f2-1f)", "(at key0-0 f4-3f)", "(at key0-2 f3-2f)", "(open f4-2f)", "(at key0-1 f2-2f)", "(at key0-1 f4-0f)", "(at-robot f2-0f)", "(at key0-0 f1-2f)", "(at-robot f2-3f)", "(at-robot f0-3f)", "(at-robot f1-0f)", "(at key0-0 f4-1f)"], "yes": ["(holding key0-0)", "(at-robot f3-3f)", "(at-robot f1-3f)"]}, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k3-l3-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 key0-2 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (arm-empty) (at key0-0 f1-3f) (at key0-1 f1-2f) (at key0-2 f3-3f) (at-robot f1-4f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (key-shape key0-2 shape0) (lock-shape f2-0f shape0) (lock-shape f2-2f shape0) (lock-shape f4-2f shape0) (locked f2-0f) (locked f4-2f) (open f0-0f) (open f0-1f) (open f0-2f) (open f0-3f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-1f) (open f2-2f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-0f) (open f4-1f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key0-0 f3-3f) (at key0-1 f1-2f) (at key0-2 f3-3f)))\n)"}
{"id": 6724187827384983583, "group": "landmarks_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f2-2f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-4f has shape0 shaped lock, f4-0f has shape0 shaped lock. Key key0-0 is at position f0-1f. Key key0-2 is at position f1-4f. Key key0-1 is at position f1-2f. The goal is to reach a state where the following facts hold: Key key0-0 is at f2-2f location, Key key0-1 is at f3-2f location, and Key key0-2 is at f1-4f location. The available propositions are: (at ?r ?x) - Key ?r is at ?x location, (at-robot ?x) - Robot is at ?x location, (locked ?x) - Location ?x is locked, (holding ?k) - Robot is holding ?k, (open ?x) - Location ?x is open, and (arm-empty) - Robot is not holding anything.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at key0-2 f2-0f)", "(at key0-1 f1-0f)", "(at key0-0 f3-2f)", "(at-robot f2-4f)", "(at-robot f3-0f)", "(at-robot f0-0f)", "(at key0-1 f0-0f)", "(at key0-2 f0-3f)", "(at key0-2 f4-1f)", "(at key0-0 f2-0f)", "(at key0-1 f3-4f)", "(at key0-1 f3-1f)", "(at-robot f3-4f)", "(at-robot f4-3f)", "(at-robot f4-0f)", "(at key0-1 f0-1f)", "(at key0-0 f0-0f)", "(at key0-2 f1-3f)", "(at-robot f2-1f)", "(at key0-0 f0-3f)", "(at key0-2 f4-0f)", "(at key0-0 f0-2f)", "(at key0-0 f1-0f)", "(at key0-1 f2-0f)", "(at key0-1 f2-1f)", "(at key0-1 f2-4f)", "(at key0-2 f1-0f)", "(at key0-2 f2-4f)", "(at key0-1 f4-4f)", "(at key0-0 f2-3f)", "(at key0-1 f0-3f)", "(at-robot f1-3f)", "(open f4-4f)", "(at-robot f1-1f)", "(holding key0-2)", "(at key0-1 f1-4f)", "(at key0-1 f3-0f)", "(at key0-0 f1-3f)", "(at key0-0 f3-1f)", "(at key0-2 f1-1f)", "(at key0-1 f3-3f)", "(at key0-2 f0-1f)", "(at key0-2 f3-4f)", "(open f0-3f)", "(at key0-0 f2-1f)", "(at key0-1 f4-2f)", "(at key0-0 f2-4f)", "(at key0-2 f4-4f)", "(at key0-2 f4-3f)", "(at-robot f0-4f)", "(at-robot f3-3f)", "(at key0-0 f3-0f)", "(at key0-0 f1-1f)", "(at key0-1 f2-3f)", "(at key0-0 f1-4f)", "(at-robot f3-1f)", "(at key0-2 f1-2f)", "(at key0-2 f3-3f)", "(at key0-1 f1-3f)", "(at-robot f4-4f)", "(open f4-0f)", "(at-robot f4-1f)", "(at key0-2 f0-4f)", "(at key0-0 f4-2f)", "(at key0-1 f4-1f)", "(at-robot f1-4f)", "(at key0-0 f3-4f)", "(at key0-1 f1-1f)", "(at key0-0 f0-4f)", "(at key0-0 f4-4f)", "(at key0-2 f2-3f)", "(at key0-2 f2-2f)", "(at key0-2 f3-0f)", "(at key0-2 f3-1f)", "(at-robot f0-2f)", "(at key0-0 f4-0f)", "(at key0-2 f4-2f)", "(at key0-2 f0-2f)", "(at key0-2 f0-0f)", "(at key0-1 f0-4f)", "(at key0-1 f0-2f)", "(at-robot f4-2f)", "(at key0-1 f4-3f)", "(at key0-2 f2-1f)", "(at key0-0 f4-3f)", "(at key0-2 f3-2f)", "(at key0-1 f2-2f)", "(at key0-1 f4-0f)", "(at-robot f2-0f)", "(at key0-0 f1-2f)", "(at-robot f2-3f)", "(at-robot f0-3f)", "(at key0-0 f3-3f)", "(at-robot f1-0f)", "(at key0-0 f4-1f)"], "yes": ["(holding key0-0)", "(holding key0-1)", "(at-robot f3-2f)", "(at-robot f0-1f)", "(at-robot f1-2f)"]}, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k3-l3-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 key0-2 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (arm-empty) (at key0-0 f0-1f) (at key0-1 f1-2f) (at key0-2 f1-4f) (at-robot f2-2f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (key-shape key0-2 shape0) (lock-shape f0-3f shape0) (lock-shape f4-0f shape0) (lock-shape f4-4f shape0) (locked f0-3f) (locked f4-0f) (locked f4-4f) (open f0-0f) (open f0-1f) (open f0-2f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-0f) (open f2-1f) (open f2-2f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-1f) (open f4-2f) (open f4-3f))\n    (:goal (and (at key0-0 f2-2f) (at key0-1 f3-2f) (at key0-2 f1-4f)))\n)"}
{"id": -7078084170422273772, "group": "landmarks_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f3-4f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-4f has shape0 shaped lock, f4-0f has shape0 shaped lock. Key key0-2 is at position f4-3f. Key key0-1 is at position f3-2f. Key key0-0 is at position f2-2f. The goal is to reach a state where the following facts hold: Key key0-0 is at f2-2f location, Key key0-1 is at f3-2f location, and Key key0-2 is at f1-4f location. The available propositions are: (at ?r ?x) - Key ?r is at ?x location, (at-robot ?x) - Robot is at ?x location, (locked ?x) - Location ?x is locked, (holding ?k) - Robot is holding ?k, (open ?x) - Location ?x is open, and (arm-empty) - Robot's arm is empty.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at key0-2 f2-0f)", "(at key0-1 f1-0f)", "(at key0-0 f3-2f)", "(at-robot f2-4f)", "(at-robot f3-0f)", "(at-robot f0-0f)", "(at key0-1 f0-0f)", "(at key0-2 f0-3f)", "(at key0-2 f4-1f)", "(at key0-0 f2-0f)", "(at key0-1 f3-4f)", "(at key0-1 f3-1f)", "(at-robot f4-0f)", "(at key0-1 f0-1f)", "(at-robot f2-2f)", "(at key0-0 f0-0f)", "(at key0-2 f1-3f)", "(at-robot f2-1f)", "(at key0-0 f0-3f)", "(at key0-2 f4-0f)", "(at key0-0 f0-2f)", "(at key0-0 f1-0f)", "(at key0-1 f2-0f)", "(at key0-1 f2-1f)", "(at key0-1 f2-4f)", "(at key0-2 f1-0f)", "(at-robot f3-2f)", "(at key0-2 f2-4f)", "(at key0-1 f4-4f)", "(at key0-0 f2-3f)", "(at key0-1 f0-3f)", "(at-robot f1-3f)", "(open f4-4f)", "(at-robot f1-1f)", "(at key0-1 f1-2f)", "(at key0-1 f1-4f)", "(at key0-1 f3-0f)", "(at key0-0 f1-3f)", "(at key0-0 f3-1f)", "(at key0-2 f1-1f)", "(at key0-1 f3-3f)", "(at key0-2 f0-1f)", "(at key0-2 f3-4f)", "(open f0-3f)", "(at key0-0 f2-1f)", "(at key0-1 f4-2f)", "(at key0-0 f2-4f)", "(at key0-2 f4-4f)", "(at-robot f0-1f)", "(at-robot f0-4f)", "(at-robot f3-3f)", "(at key0-0 f0-1f)", "(at key0-0 f3-0f)", "(at key0-0 f1-1f)", "(at key0-1 f2-3f)", "(at key0-0 f1-4f)", "(at-robot f3-1f)", "(at key0-2 f1-2f)", "(at key0-2 f3-3f)", "(at key0-1 f1-3f)", "(at-robot f4-4f)", "(open f4-0f)", "(at-robot f4-1f)", "(at key0-2 f0-4f)", "(at key0-0 f4-2f)", "(at key0-1 f4-1f)", "(at key0-0 f3-4f)", "(at key0-1 f1-1f)", "(at key0-0 f0-4f)", "(at key0-0 f4-4f)", "(at key0-2 f2-3f)", "(at key0-2 f2-2f)", "(at key0-2 f3-0f)", "(at-robot f1-2f)", "(at key0-2 f3-1f)", "(at-robot f0-2f)", "(at key0-0 f4-0f)", "(at key0-2 f4-2f)", "(holding key0-0)", "(holding key0-1)", "(at key0-2 f0-2f)", "(at key0-2 f0-0f)", "(at key0-1 f0-4f)", "(at key0-1 f0-2f)", "(at-robot f4-2f)", "(at key0-1 f4-3f)", "(at key0-2 f2-1f)", "(at key0-0 f4-3f)", "(at key0-2 f3-2f)", "(at key0-1 f2-2f)", "(at key0-1 f4-0f)", "(at-robot f2-0f)", "(at key0-0 f1-2f)", "(at-robot f2-3f)", "(at-robot f0-3f)", "(at key0-0 f3-3f)", "(at-robot f1-0f)", "(at key0-0 f4-1f)"], "yes": ["(holding key0-2)", "(at-robot f1-4f)", "(at-robot f4-3f)"]}, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k3-l3-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 key0-2 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (arm-empty) (at key0-0 f2-2f) (at key0-1 f3-2f) (at key0-2 f4-3f) (at-robot f3-4f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (key-shape key0-2 shape0) (lock-shape f0-3f shape0) (lock-shape f4-0f shape0) (lock-shape f4-4f shape0) (locked f0-3f) (locked f4-0f) (locked f4-4f) (open f0-0f) (open f0-1f) (open f0-2f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-0f) (open f2-1f) (open f2-2f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-1f) (open f4-2f) (open f4-3f))\n    (:goal (and (at key0-0 f2-2f) (at key0-1 f3-2f) (at key0-2 f1-4f)))\n)"}
{"id": -110907653380782597, "group": "landmarks_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f0-3f and is holding key0-2. All the positions are open except the following: f2-0f has shape0 shaped lock, f4-2f has shape0 shaped lock, f2-2f has shape0 shaped lock. Key key0-1 is at position f2-2f. Key key0-0 is at position f1-3f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-2f location, Key key0-0 is at f3-3f location, and Key key0-2 is at f3-3f location. The available propositions are: (at ?r ?x) - Key ?r is at ?x location, (at-robot ?x) - Robot is at ?x location, (locked ?x) - Location ?x is locked, (holding ?k) - Robot is holding ?k, (open ?x) - Location ?x is open, and (arm-empty) - Robot is not holding anything.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at key0-2 f2-0f)", "(at key0-1 f1-0f)", "(at key0-0 f3-2f)", "(at-robot f2-4f)", "(at-robot f3-0f)", "(at-robot f0-0f)", "(at key0-2 f1-4f)", "(at key0-1 f0-0f)", "(at key0-2 f0-3f)", "(at key0-2 f4-1f)", "(at key0-0 f2-0f)", "(at key0-1 f3-4f)", "(at key0-1 f3-1f)", "(at-robot f3-4f)", "(at-robot f4-3f)", "(at-robot f4-0f)", "(at key0-1 f0-1f)", "(at key0-0 f0-0f)", "(at key0-2 f1-3f)", "(at-robot f2-1f)", "(at key0-0 f0-3f)", "(at key0-2 f4-0f)", "(at key0-0 f0-2f)", "(at key0-0 f1-0f)", "(at key0-1 f2-0f)", "(at key0-1 f2-1f)", "(at key0-1 f2-4f)", "(at key0-2 f1-0f)", "(arm-empty)", "(at-robot f3-2f)", "(at key0-2 f2-4f)", "(at key0-1 f4-4f)", "(at key0-0 f2-3f)", "(open f2-0f)", "(at key0-1 f0-3f)", "(at-robot f1-1f)", "(at key0-1 f3-0f)", "(at key0-1 f1-4f)", "(at key0-0 f3-1f)", "(at key0-2 f1-1f)", "(at key0-1 f3-3f)", "(at key0-2 f0-1f)", "(at key0-2 f3-4f)", "(at key0-0 f2-1f)", "(at key0-1 f4-2f)", "(at key0-0 f2-4f)", "(at key0-2 f4-4f)", "(at key0-2 f4-3f)", "(at-robot f0-1f)", "(at key0-1 f3-2f)", "(at-robot f0-4f)", "(at key0-0 f0-1f)", "(at key0-0 f3-0f)", "(at key0-0 f1-1f)", "(at key0-1 f2-3f)", "(at key0-0 f1-4f)", "(at-robot f3-1f)", "(at key0-2 f1-2f)", "(at key0-1 f1-3f)", "(at-robot f4-4f)", "(at-robot f4-1f)", "(at key0-2 f0-4f)", "(at key0-0 f4-2f)", "(at key0-1 f4-1f)", "(at-robot f1-4f)", "(at key0-0 f3-4f)", "(at key0-1 f1-1f)", "(at key0-0 f0-4f)", "(at key0-0 f4-4f)", "(at key0-2 f2-3f)", "(at key0-2 f2-2f)", "(at key0-2 f3-0f)", "(at key0-2 f3-1f)", "(at-robot f0-2f)", "(at key0-0 f4-0f)", "(at key0-0 f2-2f)", "(at key0-2 f4-2f)", "(at key0-2 f0-2f)", "(at key0-2 f0-0f)", "(at key0-1 f0-4f)", "(at key0-1 f0-2f)", "(at-robot f4-2f)", "(at key0-1 f4-3f)", "(at key0-2 f2-1f)", "(at key0-0 f4-3f)", "(at key0-2 f3-2f)", "(open f4-2f)", "(at key0-1 f4-0f)", "(at-robot f2-0f)", "(at key0-0 f1-2f)", "(at-robot f2-3f)", "(at-robot f1-0f)", "(at key0-0 f4-1f)"], "yes": ["(holding key0-0)", "(at-robot f3-3f)", "(holding key0-1)", "(at-robot f1-2f)", "(at-robot f1-3f)", "(at-robot f2-2f)", "(open f2-2f)"]}, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k3-l3-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 key0-2 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (at key0-0 f1-3f) (at key0-1 f2-2f) (at-robot f0-3f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (holding key0-2) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (key-shape key0-2 shape0) (lock-shape f2-0f shape0) (lock-shape f2-2f shape0) (lock-shape f4-2f shape0) (locked f2-0f) (locked f2-2f) (locked f4-2f) (open f0-0f) (open f0-1f) (open f0-2f) (open f0-3f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-1f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-0f) (open f4-1f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key0-0 f3-3f) (at key0-1 f1-2f) (at key0-2 f3-3f)))\n)"}
{"id": 866626836411267754, "group": "landmarks_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-1f and its arm is empty. All the positions are open except the following: f0-1f has shape0 shaped lock. Key key0-0 is at position f2-1f. Key key0-1 is at position f4-1f. The goal is to reach a state where the following facts hold: Key key0-0 is at f3-1f location and Key key0-1 is at f4-1f location. The available propositions are: (at ?r ?x) - Key ?r is at ?x location, (at-robot ?x) - Robot is at ?x location, (locked ?x) - Location ?x is locked, (holding ?k) - Robot is holding ?k, (open ?x) - Location ?x is open, and (arm-empty) - Robot is not holding anything.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at-robot f1-2f)", "(at key0-1 f4-2f)", "(at key0-0 f2-4f)", "(at key0-1 f1-0f)", "(at-robot f0-2f)", "(at key0-0 f3-2f)", "(at key0-1 f4-4f)", "(at-robot f2-4f)", "(at-robot f0-1f)", "(at key0-1 f3-2f)", "(at key0-0 f4-0f)", "(at key0-0 f2-2f)", "(holding key0-1)", "(at-robot f3-0f)", "(at-robot f0-4f)", "(at-robot f0-0f)", "(at key0-0 f2-3f)", "(at-robot f3-3f)", "(at key0-0 f0-1f)", "(at key0-0 f3-0f)", "(at key0-0 f1-1f)", "(at key0-1 f2-3f)", "(at key0-1 f0-0f)", "(at key0-1 f0-4f)", "(at key0-0 f1-4f)", "(at key0-0 f2-0f)", "(at key0-1 f3-4f)", "(at key0-1 f3-1f)", "(at key0-1 f0-3f)", "(at-robot f3-4f)", "(at key0-1 f0-2f)", "(at key0-1 f1-3f)", "(at-robot f1-3f)", "(at-robot f4-3f)", "(at-robot f4-4f)", "(at-robot f4-2f)", "(at-robot f4-0f)", "(at key0-1 f4-3f)", "(at key0-0 f4-3f)", "(locked f2-1f)", "(at-robot f1-1f)", "(at key0-1 f0-1f)", "(at key0-1 f1-2f)", "(at key0-1 f1-4f)", "(at-robot f4-1f)", "(at key0-1 f3-0f)", "(at-robot f2-2f)", "(open f0-1f)", "(at key0-1 f2-2f)", "(at key0-0 f4-2f)", "(at key0-0 f0-0f)", "(at key0-0 f1-3f)", "(at key0-1 f4-0f)", "(at key0-0 f0-3f)", "(at-robot f1-4f)", "(at-robot f2-0f)", "(at key0-0 f0-2f)", "(at key0-1 f3-3f)", "(at key0-0 f3-4f)", "(at key0-0 f1-2f)", "(at key0-0 f1-0f)", "(at key0-1 f1-1f)", "(at-robot f2-3f)", "(at key0-1 f2-0f)", "(at key0-0 f0-4f)", "(at-robot f0-3f)", "(at key0-0 f4-4f)", "(at key0-1 f2-1f)", "(at key0-0 f3-3f)", "(at key0-1 f2-4f)", "(at-robot f1-0f)", "(at key0-0 f4-1f)", "(at-robot f3-2f)"], "yes": ["(at-robot f3-1f)", "(holding key0-0)"]}, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k2-l2-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (arm-empty) (at key0-0 f2-1f) (at key0-1 f4-1f) (at-robot f2-1f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (lock-shape f0-1f shape0) (lock-shape f2-1f shape0) (locked f0-1f) (open f0-0f) (open f0-2f) (open f0-3f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-0f) (open f2-1f) (open f2-2f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-0f) (open f4-1f) (open f4-2f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key0-0 f3-1f) (at key0-1 f4-1f)))\n)"}
{"id": -5718162202945933497, "group": "landmarks_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. \nThe grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  \nThere are 3 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0.  \nCurrently, the robot is at position f1-2f and its arm is empty. All the positions are open except the following: f0-1f has shape0 shaped lock, f2-1f has shape0 shaped lock, f2-2f has shape0 shaped lock. Key key0-2 is at position f1-2f. Key key0-1 is at position f3-3f. Key key0-0 is at position f4-1f. The goal is to reach a state where the following facts hold: Key key0-2 is at f1-1f location, Key key0-0 is at f1-0f location, and Key key0-1 is at f2-4f location. The available propositions are: (at ?r ?x) - Key ?r is at ?x location, (at-robot ?x) - Robot is at ?x location, (locked ?x) - Location ?x is locked, (holding ?k) - Robot is holding ?k, (open ?x) - Location ?x is open, and (arm-empty) - Robot is not holding anything.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(open f2-1f)", "(at key0-2 f2-0f)", "(at key0-1 f1-0f)", "(at key0-0 f3-2f)", "(at-robot f3-0f)", "(at-robot f0-0f)", "(at key0-2 f1-4f)", "(at key0-1 f0-0f)", "(at key0-2 f0-3f)", "(at key0-2 f4-1f)", "(at key0-0 f2-0f)", "(at key0-1 f3-4f)", "(at key0-1 f3-1f)", "(at-robot f3-4f)", "(at-robot f4-3f)", "(at-robot f4-0f)", "(at key0-1 f0-1f)", "(at-robot f2-2f)", "(open f0-1f)", "(at key0-0 f0-0f)", "(at key0-2 f1-3f)", "(at-robot f2-1f)", "(at key0-0 f0-3f)", "(at key0-2 f4-0f)", "(at key0-0 f0-2f)", "(at key0-1 f2-0f)", "(at key0-1 f2-1f)", "(at key0-2 f1-0f)", "(at-robot f3-2f)", "(at key0-2 f2-4f)", "(at key0-1 f4-4f)", "(at key0-0 f2-3f)", "(at key0-1 f0-3f)", "(at-robot f1-3f)", "(at key0-1 f1-2f)", "(at key0-1 f1-4f)", "(at key0-0 f1-3f)", "(at key0-0 f3-1f)", "(at key0-2 f0-1f)", "(at key0-2 f3-4f)", "(at key0-0 f2-1f)", "(at key0-1 f4-2f)", "(at key0-0 f2-4f)", "(at key0-2 f4-4f)", "(at key0-2 f4-3f)", "(at-robot f0-1f)", "(at key0-1 f3-2f)", "(open f2-2f)", "(at-robot f0-4f)", "(at key0-0 f0-1f)", "(at key0-0 f3-0f)", "(at key0-0 f1-1f)", "(at key0-1 f2-3f)", "(at key0-0 f1-4f)", "(at-robot f3-1f)", "(at key0-2 f3-3f)", "(at key0-1 f1-3f)", "(at-robot f4-4f)", "(at key0-2 f0-4f)", "(at key0-0 f4-2f)", "(at key0-1 f4-1f)", "(at-robot f1-4f)", "(at key0-0 f3-4f)", "(at key0-1 f1-1f)", "(at key0-0 f0-4f)", "(at key0-0 f4-4f)", "(at key0-2 f2-3f)", "(at key0-2 f2-2f)", "(at key0-2 f3-0f)", "(at key0-2 f3-1f)", "(at-robot f0-2f)", "(at key0-0 f4-0f)", "(at key0-0 f2-2f)", "(at key0-2 f4-2f)", "(at key0-2 f0-2f)", "(at key0-2 f0-0f)", "(at key0-1 f0-4f)", "(at key0-1 f0-2f)", "(at-robot f4-2f)", "(at key0-1 f4-3f)", "(at key0-2 f2-1f)", "(at key0-0 f4-3f)", "(at key0-2 f3-2f)", "(at key0-1 f2-2f)", "(at key0-1 f4-0f)", "(at-robot f2-0f)", "(at key0-0 f1-2f)", "(at-robot f2-3f)", "(at-robot f0-3f)", "(at key0-0 f3-3f)", "(at key0-1 f3-0f)"], "yes": ["(holding key0-0)", "(at-robot f1-0f)", "(holding key0-1)", "(at-robot f2-4f)", "(holding key0-2)", "(at-robot f1-1f)", "(at-robot f4-1f)", "(at-robot f3-3f)"]}, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k3-l3-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 key0-2 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (arm-empty) (at key0-0 f4-1f) (at key0-1 f3-3f) (at key0-2 f1-2f) (at-robot f1-2f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (key-shape key0-2 shape0) (lock-shape f0-1f shape0) (lock-shape f2-1f shape0) (lock-shape f2-2f shape0) (locked f0-1f) (locked f2-1f) (locked f2-2f) (open f0-0f) (open f0-2f) (open f0-3f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-0f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-0f) (open f4-1f) (open f4-2f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key0-0 f1-0f) (at key0-1 f2-4f) (at key0-2 f1-1f)))\n)"}
{"id": -4162812061202576052, "group": "landmarks_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. \nThe grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  \nThere are 2 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  \nCurrently, the robot is at position f2-3f and its arm is empty. All the positions are open except the following: f0-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-0 is at position f1-2f. Key key0-1 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key0-1 is at f3-0f location and Key key0-0 is at f0-4f location. The available propositions are: (at ?r ?x) - Key ?r is at ?x location, (at-robot ?x) - Robot is at ?x location, (locked ?x) - Location ?x is locked, (holding ?k) - Robot is holding ?k, (open ?x) - Location ?x is open, and (arm-empty) - Robot is not holding anything.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at key0-0 f2-1f)", "(at key0-1 f4-2f)", "(at key0-0 f2-4f)", "(at key0-1 f1-0f)", "(at-robot f0-2f)", "(at key0-0 f3-2f)", "(at key0-1 f4-4f)", "(at-robot f2-4f)", "(at-robot f0-1f)", "(at key0-1 f3-2f)", "(at key0-0 f4-0f)", "(at key0-0 f2-2f)", "(open f0-0f)", "(holding key0-1)", "(at-robot f3-0f)", "(at-robot f3-3f)", "(at-robot f0-0f)", "(at key0-0 f2-3f)", "(at key0-0 f0-1f)", "(at key0-0 f3-0f)", "(at key0-0 f1-1f)", "(at key0-1 f2-3f)", "(at key0-1 f0-0f)", "(at key0-1 f0-4f)", "(at key0-0 f1-4f)", "(at key0-0 f2-0f)", "(at key0-1 f3-4f)", "(at-robot f3-1f)", "(at key0-1 f3-1f)", "(at key0-1 f0-3f)", "(at-robot f3-4f)", "(at key0-1 f0-2f)", "(at key0-1 f1-3f)", "(at-robot f1-3f)", "(at-robot f4-3f)", "(at-robot f4-4f)", "(at-robot f4-2f)", "(at-robot f4-0f)", "(at key0-1 f4-3f)", "(at key0-0 f4-3f)", "(at-robot f1-1f)", "(at key0-1 f0-1f)", "(at key0-1 f1-2f)", "(at key0-1 f1-4f)", "(at-robot f4-1f)", "(at-robot f2-2f)", "(at key0-1 f2-2f)", "(at key0-0 f4-2f)", "(at key0-0 f0-0f)", "(at key0-0 f1-3f)", "(at key0-1 f4-1f)", "(at key0-1 f4-0f)", "(at-robot f2-1f)", "(at key0-0 f0-3f)", "(at key0-0 f3-1f)", "(at-robot f2-0f)", "(at-robot f1-4f)", "(at key0-0 f0-2f)", "(at key0-1 f3-3f)", "(at key0-0 f3-4f)", "(at key0-0 f1-0f)", "(at key0-1 f1-1f)", "(at key0-1 f2-0f)", "(at-robot f0-3f)", "(at key0-0 f4-4f)", "(at key0-1 f2-1f)", "(at key0-0 f3-3f)", "(at key0-1 f2-4f)", "(at-robot f1-0f)", "(open f0-3f)", "(at key0-0 f4-1f)", "(at-robot f3-2f)"], "yes": ["(at-robot f0-4f)", "(holding key0-0)", "(at-robot f1-2f)"]}, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k2-l2-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (arm-empty) (at key0-0 f1-2f) (at key0-1 f3-0f) (at-robot f2-3f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (lock-shape f0-0f shape0) (lock-shape f0-3f shape0) (locked f0-0f) (locked f0-3f) (open f0-1f) (open f0-2f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-0f) (open f2-1f) (open f2-2f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-0f) (open f4-1f) (open f4-2f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key0-0 f0-4f) (at key0-1 f3-0f)))\n)"}
{"id": -7273913249539910170, "group": "landmarks_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. \nThe grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  \nThere are 2 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  \nCurrently, the robot is at position f0-0f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock. Key key0-1 is at position f0-0f. Key key0-0 is at position f4-4f. The goal is to reach a state where the following facts hold: Key key0-1 is at f4-2f location and Key key0-0 is at f2-4f location. The available propositions are: (at ?r ?x) - Key ?r is at ?x location, (at-robot ?x) - Robot is at ?x location, (locked ?x) - Location ?x is locked, (holding ?k) - Robot is holding ?k, (open ?x) - Location ?x is open, and (arm-empty) - Robot's arm is empty.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at key0-0 f2-1f)", "(at-robot f1-2f)", "(at key0-1 f1-0f)", "(at-robot f0-2f)", "(at key0-0 f3-2f)", "(at key0-1 f4-4f)", "(at-robot f0-1f)", "(at key0-1 f3-2f)", "(at key0-0 f4-0f)", "(at key0-0 f2-2f)", "(at-robot f3-0f)", "(at-robot f0-4f)", "(at-robot f3-3f)", "(at key0-0 f2-3f)", "(at key0-0 f0-1f)", "(at key0-0 f3-0f)", "(at key0-0 f1-1f)", "(at key0-1 f2-3f)", "(at key0-1 f0-4f)", "(at key0-0 f1-4f)", "(at key0-0 f2-0f)", "(at key0-1 f3-4f)", "(at-robot f3-1f)", "(at key0-1 f3-1f)", "(at key0-1 f0-3f)", "(at key0-1 f0-2f)", "(at-robot f3-4f)", "(at key0-1 f1-3f)", "(at-robot f1-3f)", "(at-robot f4-3f)", "(at key0-1 f4-3f)", "(at-robot f4-0f)", "(at key0-0 f4-3f)", "(open f4-0f)", "(at-robot f1-1f)", "(at key0-1 f0-1f)", "(at key0-1 f1-2f)", "(at key0-1 f1-4f)", "(at-robot f4-1f)", "(at key0-1 f3-0f)", "(at-robot f2-2f)", "(at key0-1 f2-2f)", "(at key0-0 f4-2f)", "(at key0-0 f0-0f)", "(at key0-0 f1-3f)", "(at key0-1 f4-1f)", "(at key0-1 f4-0f)", "(at-robot f2-1f)", "(at key0-0 f0-3f)", "(at key0-0 f3-1f)", "(at-robot f1-4f)", "(at-robot f2-0f)", "(at key0-0 f0-2f)", "(at key0-1 f3-3f)", "(at key0-0 f3-4f)", "(at key0-0 f1-2f)", "(at key0-0 f1-0f)", "(at key0-1 f1-1f)", "(at-robot f2-3f)", "(at key0-1 f2-0f)", "(at key0-0 f0-4f)", "(at-robot f0-3f)", "(at key0-1 f2-1f)", "(at key0-0 f3-3f)", "(at key0-1 f2-4f)", "(at-robot f1-0f)", "(open f0-3f)", "(at key0-0 f4-1f)", "(at-robot f3-2f)"], "yes": ["(at-robot f2-4f)", "(holding key0-0)", "(at-robot f4-2f)", "(holding key0-1)", "(at-robot f4-4f)"]}, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k2-l2-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (arm-empty) (at key0-0 f4-4f) (at key0-1 f0-0f) (at-robot f0-0f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (lock-shape f0-3f shape0) (lock-shape f4-0f shape0) (locked f0-3f) (locked f4-0f) (open f0-0f) (open f0-1f) (open f0-2f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-0f) (open f2-1f) (open f2-2f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-1f) (open f4-2f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key0-0 f2-4f) (at key0-1 f4-2f)))\n)"}
{"id": 8452927514841302640, "group": "landmarks_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. \nThe grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  \nThere are 2 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  \nCurrently, the robot is at position f1-4f and its arm is empty. All the positions are open except the following: f0-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-0 is at position f1-2f. Key key0-1 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key0-1 is at f3-0f location and Key key0-0 is at f0-4f location. The available propositions are: (at ?r ?x) - Key ?r is at ?x location, (at-robot ?x) - Robot is at ?x location, (locked ?x) - Location ?x is locked, (holding ?k) - Robot is holding ?k, (open ?x) - Location ?x is open, and (arm-empty) - Robot's arm is empty.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at key0-0 f2-1f)", "(at key0-1 f4-2f)", "(at key0-0 f2-4f)", "(at key0-1 f1-0f)", "(at-robot f0-2f)", "(at key0-0 f3-2f)", "(at key0-1 f4-4f)", "(at-robot f2-4f)", "(at-robot f0-1f)", "(at key0-1 f3-2f)", "(at key0-0 f4-0f)", "(at key0-0 f2-2f)", "(open f0-0f)", "(holding key0-1)", "(at-robot f3-0f)", "(at-robot f3-3f)", "(at-robot f0-0f)", "(at key0-0 f2-3f)", "(at key0-0 f0-1f)", "(at key0-0 f3-0f)", "(at key0-0 f1-1f)", "(at key0-1 f2-3f)", "(at key0-1 f0-0f)", "(at key0-1 f0-4f)", "(at key0-0 f1-4f)", "(at key0-0 f2-0f)", "(at key0-1 f3-4f)", "(at-robot f3-1f)", "(at key0-1 f3-1f)", "(at key0-1 f0-3f)", "(at-robot f3-4f)", "(at key0-1 f0-2f)", "(at key0-1 f1-3f)", "(at-robot f1-3f)", "(at-robot f4-3f)", "(at-robot f4-4f)", "(at-robot f4-2f)", "(at-robot f4-0f)", "(at key0-1 f4-3f)", "(at key0-0 f4-3f)", "(at-robot f1-1f)", "(at key0-1 f0-1f)", "(at key0-1 f1-2f)", "(at key0-1 f1-4f)", "(at-robot f4-1f)", "(at-robot f2-2f)", "(at key0-1 f2-2f)", "(at key0-0 f4-2f)", "(at key0-0 f0-0f)", "(at key0-0 f1-3f)", "(at key0-1 f4-1f)", "(at key0-1 f4-0f)", "(at-robot f2-1f)", "(at key0-0 f0-3f)", "(at key0-0 f3-1f)", "(at-robot f2-0f)", "(at key0-0 f0-2f)", "(at key0-1 f3-3f)", "(at key0-0 f3-4f)", "(at key0-0 f1-0f)", "(at key0-1 f1-1f)", "(at-robot f2-3f)", "(at key0-1 f2-0f)", "(at-robot f0-3f)", "(at key0-0 f4-4f)", "(at key0-1 f2-1f)", "(at key0-0 f3-3f)", "(at key0-1 f2-4f)", "(at-robot f1-0f)", "(open f0-3f)", "(at key0-0 f4-1f)", "(at-robot f3-2f)"], "yes": ["(at-robot f0-4f)", "(holding key0-0)", "(at-robot f1-2f)"]}, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k2-l2-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (arm-empty) (at key0-0 f1-2f) (at key0-1 f3-0f) (at-robot f1-4f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (lock-shape f0-0f shape0) (lock-shape f0-3f shape0) (locked f0-0f) (locked f0-3f) (open f0-1f) (open f0-2f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-0f) (open f2-1f) (open f2-2f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-0f) (open f4-1f) (open f4-2f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key0-0 f0-4f) (at key0-1 f3-0f)))\n)"}
{"id": -1206225347505945318, "group": "landmarks_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. \nThe grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  \nThere are 2 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  \nCurrently, the robot is at position f3-2f and is holding key0-1. All the positions are open except the following: f4-0f has shape0 shaped lock. Key key0-0 is at position f4-4f. The goal is to reach a state where the following facts hold: Key key0-1 is at f4-2f location and Key key0-0 is at f2-4f location. The available propositions are: (at ?r ?x) - Key ?r is at ?x location, (at-robot ?x) - Robot is at ?x location, (locked ?x) - Location ?x is locked, (holding ?k) - Robot is holding ?k, (open ?x) - Location ?x is open, and (arm-empty) - Robot's arm is empty.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at key0-0 f2-1f)", "(at-robot f1-2f)", "(at key0-1 f1-0f)", "(at-robot f0-2f)", "(at key0-0 f3-2f)", "(at key0-1 f4-4f)", "(at-robot f0-1f)", "(at key0-1 f3-2f)", "(at key0-0 f4-0f)", "(at key0-0 f2-2f)", "(at-robot f3-0f)", "(at-robot f0-4f)", "(at-robot f0-0f)", "(at key0-0 f2-3f)", "(at-robot f3-3f)", "(at key0-0 f0-1f)", "(at key0-0 f3-0f)", "(at key0-0 f1-1f)", "(at key0-1 f2-3f)", "(at key0-1 f0-0f)", "(at key0-1 f0-4f)", "(at key0-0 f1-4f)", "(at key0-0 f2-0f)", "(at key0-1 f3-4f)", "(locked f0-3f)", "(at-robot f3-1f)", "(at key0-1 f3-1f)", "(at key0-1 f0-3f)", "(at key0-1 f0-2f)", "(at-robot f3-4f)", "(at key0-1 f1-3f)", "(at-robot f1-3f)", "(at-robot f4-3f)", "(at key0-1 f4-3f)", "(at-robot f4-0f)", "(at key0-0 f4-3f)", "(open f4-0f)", "(at-robot f1-1f)", "(at key0-1 f0-1f)", "(at key0-1 f1-2f)", "(at key0-1 f1-4f)", "(at-robot f4-1f)", "(at key0-1 f3-0f)", "(at-robot f2-2f)", "(at key0-1 f2-2f)", "(at key0-0 f4-2f)", "(at key0-0 f0-0f)", "(at key0-0 f1-3f)", "(at key0-1 f4-1f)", "(at key0-1 f4-0f)", "(at-robot f2-1f)", "(at key0-0 f0-3f)", "(at key0-0 f3-1f)", "(at-robot f1-4f)", "(at-robot f2-0f)", "(at key0-0 f0-2f)", "(at key0-1 f3-3f)", "(at key0-0 f3-4f)", "(at key0-0 f1-2f)", "(at key0-0 f1-0f)", "(at key0-1 f1-1f)", "(at-robot f2-3f)", "(at key0-1 f2-0f)", "(at key0-0 f0-4f)", "(at-robot f0-3f)", "(at key0-1 f2-1f)", "(at key0-0 f3-3f)", "(at key0-1 f2-4f)", "(at-robot f1-0f)", "(arm-empty)", "(at key0-0 f4-1f)"], "yes": ["(at-robot f2-4f)", "(holding key0-0)", "(at-robot f4-2f)", "(at-robot f4-4f)"]}, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k2-l2-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (at key0-0 f4-4f) (at-robot f3-2f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (holding key0-1) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (lock-shape f0-3f shape0) (lock-shape f4-0f shape0) (locked f4-0f) (open f0-0f) (open f0-1f) (open f0-2f) (open f0-3f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-0f) (open f2-1f) (open f2-2f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-1f) (open f4-2f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key0-0 f2-4f) (at key0-1 f4-2f)))\n)"}
