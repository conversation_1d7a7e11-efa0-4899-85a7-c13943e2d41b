{"id": 3147596864867792497, "group": "action_justification_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera1 supports high_res and low_res. Camera camera0 supports low_res. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint0. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rocks can be sampled at the following location(s): waypoint1. Soil can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective0 was communicated in mode low_res, Image objective1 was communicated in mode low_res, Rock data was communicated from waypoint waypoint1;, and Soil data was communicated from waypoint waypoint1;.", "question": "Given the plan: \"navigate rover rover0 from waypoint waypoint2 to waypoint waypoint1, adjust the camera camera1 on the rover rover0 for the objective objective0 at waypoint waypoint1, capture an image of the objective objective0 in mode low_res with the camera camera1 on the rover rover0 at waypoint waypoint1, adjust the camera camera1 on the rover rover0 for the objective objective0 at waypoint waypoint1, collect a sample from the waypoint waypoint1 using the rover rover0 and store it in the store store0, drop store store0 of rover rover0, adjust the camera camera1 on the rover rover0 for the objective objective0 at waypoint waypoint1, capture an image of the objective objective1 in mode low_res with the camera camera1 on the rover rover0 at waypoint waypoint1, sample the soil at waypoint waypoint1 with rover rover0 and store it in store store0, navigate rover rover0 from waypoint waypoint1 to waypoint waypoint2, communicate rock data from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1 via waypoint waypoint1, communicate the image data of target objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of target objective0 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, navigate rover rover0 from waypoint waypoint2 to waypoint waypoint0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint1\"; which of the following actions can be removed from this plan and still have a valid plan? A. navigate rover rover0 from waypoint waypoint1 to waypoint waypoint2. B. adjust the camera camera1 on the rover rover0 for the objective objective0 at waypoint waypoint1. C. communicate the image data of target objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1. D. communicate the image data of target objective0 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["navigate rover rover0 from waypoint waypoint1 to waypoint waypoint2", "adjust the camera camera1 on the rover rover0 for the objective objective0 at waypoint waypoint1", "communicate the image data of target objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1", "communicate the image data of target objective0 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1"]}, "query": "Given the plan: \"navigate rover rover0 from waypoint waypoint2 to waypoint waypoint1, adjust the camera camera1 on the rover rover0 for the objective objective0 at waypoint waypoint1, capture an image of the objective objective0 in mode low_res with the camera camera1 on the rover rover0 at waypoint waypoint1, adjust the camera camera1 on the rover rover0 for the objective objective0 at waypoint waypoint1, collect a sample from the waypoint waypoint1 using the rover rover0 and store it in the store store0, drop store store0 of rover rover0, adjust the camera camera1 on the rover rover0 for the objective objective0 at waypoint waypoint1, capture an image of the objective objective1 in mode low_res with the camera camera1 on the rover rover0 at waypoint waypoint1, sample the soil at waypoint waypoint1 with rover rover0 and store it in store store0, navigate rover rover0 from waypoint waypoint1 to waypoint waypoint2, communicate rock data from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1 via waypoint waypoint1, communicate the image data of target objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of target objective0 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, navigate rover rover0 from waypoint waypoint2 to waypoint waypoint0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint1\"; which action can be removed from this plan?", "answer": "B"}
{"id": 5799515994567508044, "group": "action_justification_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective0. Camera camera2 supports high_res and colour. Camera camera0 supports colour. Camera camera1 supports low_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint2. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, and Image objective1 was communicated in mode colour.", "question": "Given the plan: \"move the rover rover1 from waypoint waypoint2 to waypoint waypoint0, use the rover rover0 to collect soil samples at waypoint waypoint0 and store them in the store store0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, move the rover rover0 from waypoint waypoint0 to waypoint waypoint2, sample rock at waypoint waypoint0 with rover rover1 and store in store store1, transmit soil data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, communicate the rock data from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint1 via waypoint waypoint0, calibrate the camera camera0 on the rover rover0 for the objective objective1 at waypoint waypoint2, capture an image of the objective objective1 in mode colour with the camera camera0 on the rover rover0 at waypoint waypoint2, communicate the image data of objective objective1 in mode colour from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, move the rover rover0 from waypoint waypoint2 to waypoint waypoint0\"; which of the following actions can be removed from this plan and still have a valid plan? A. capture an image of the objective objective1 in mode colour with the camera camera0 on the rover rover0 at waypoint waypoint2. B. communicate the rock data from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint1 via waypoint waypoint0. C. transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0. D. move the rover rover0 from waypoint waypoint0 to waypoint waypoint2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["capture an image of the objective objective1 in mode colour with the camera camera0 on the rover rover0 at waypoint waypoint2", "communicate the rock data from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint1 via waypoint waypoint0", "transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0", "move the rover rover0 from waypoint waypoint0 to waypoint waypoint2"]}, "query": "Given the plan: \"move the rover rover1 from waypoint waypoint2 to waypoint waypoint0, use the rover rover0 to collect soil samples at waypoint waypoint0 and store them in the store store0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, move the rover rover0 from waypoint waypoint0 to waypoint waypoint2, sample rock at waypoint waypoint0 with rover rover1 and store in store store1, transmit soil data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, communicate the rock data from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint1 via waypoint waypoint0, calibrate the camera camera0 on the rover rover0 for the objective objective1 at waypoint waypoint2, capture an image of the objective objective1 in mode colour with the camera camera0 on the rover rover0 at waypoint waypoint2, communicate the image data of objective objective1 in mode colour from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, move the rover rover0 from waypoint waypoint2 to waypoint waypoint0\"; which action can be removed from this plan?", "answer": "C"}
{"id": -46429587192181422, "group": "action_justification_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 7 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  No rovers are equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 and camera1 on board. Rover rover0 has camera2 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective1. Camera camera2 supports colour and low_res. Camera camera0 supports high_res and low_res and colour. Camera camera1 supports colour. Rover rover1 can traverse from waypoint3 to waypoint0, waypoint0 to waypoint2, waypoint0 to waypoint3, waypoint2 to waypoint0, waypoint6 to waypoint0, waypoint4 to waypoint2, waypoint1 to waypoint6, waypoint6 to waypoint1, waypoint2 to waypoint4, waypoint0 to waypoint5, waypoint5 to waypoint0, waypoint0 to waypoint6. Rover rover0 can traverse from waypoint0 to waypoint3, waypoint5 to waypoint4, waypoint5 to waypoint1, waypoint1 to waypoint5, waypoint5 to waypoint6, waypoint0 to waypoint5, waypoint6 to waypoint5, waypoint2 to waypoint5, waypoint5 to waypoint2, waypoint4 to waypoint5, waypoint5 to waypoint0, waypoint3 to waypoint0. Waypoint(s) are visible from waypoint4: waypoint6, waypoint2, waypoint5, waypoint1, and waypoint3. Waypoint(s) are visible from waypoint0: waypoint5, waypoint3, waypoint2, and waypoint6. Waypoint(s) are visible from waypoint1: waypoint6, waypoint4, waypoint5, waypoint3, and waypoint2. Waypoint(s) are visible from waypoint3: waypoint5, waypoint1, waypoint0, waypoint6, waypoint2, and waypoint4. Waypoint(s) are visible from waypoint5: waypoint2, waypoint6, waypoint4, waypoint0, waypoint1, and waypoint3. Waypoint(s) are visible from waypoint6: waypoint0, waypoint1, waypoint3, waypoint4, and waypoint5. Waypoint(s) are visible from waypoint2: waypoint0, waypoint1, waypoint3, waypoint4, and waypoint5. Objective objective1 is visible from waypoint2, waypoint5, waypoint0, and waypoint4. Objective objective0 is visible from waypoint0. Lander general is at waypoint waypoint3.  Currently, Rover rover0 is at waypoint5. Rover rover1 is at waypoint0. Rocks can be sampled at the following location(s): waypoint5, waypoint6, waypoint2, waypoint4, and waypoint3. Soil can be sampled at the following location(s): waypoint1, waypoint4, waypoint3, and waypoint6. Rovers rover0 and rover1 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Rock data was communicated from waypoint waypoint5;, Image objective0 was communicated in mode high_res, Image objective1 was communicated in mode high_res, Rock data was communicated from waypoint waypoint3;, Rock data was communicated from waypoint waypoint4;, Image objective1 was communicated in mode colour, and Image objective1 was communicated in mode low_res.", "question": "Given the plan: \"calibrate the camera camera0 on rover rover1 for the objective objective1 at the waypoint waypoint0, take an image of the objective objective0 in mode high_res using the camera camera0 on the rover rover1 from the waypoint waypoint0, communicate the image data of objective objective0 in mode high_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint3, navigate with rover rover1 to waypoint waypoint3 from waypoint waypoint0, sample rock at waypoint waypoint3 with rover rover1 and store in store store1, sample rock at waypoint waypoint5 with rover rover0 and store in store store0, communicate the rock data from the rover rover0 at waypoint waypoint5 to the lander general at waypoint waypoint3 via waypoint waypoint5, navigate with rover rover0 to waypoint waypoint4 from waypoint waypoint5, navigate with rover rover1 to waypoint waypoint0 from waypoint waypoint3, communicate the rock data from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3 via waypoint waypoint3, calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint0, take an image of the objective objective1 in mode colour using the camera camera1 on the rover rover1 from the waypoint waypoint0, communicate the image data of objective objective1 in mode colour from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint3, calibrate the camera camera0 on rover rover1 for the objective objective1 at the waypoint waypoint0, take an image of the objective objective1 in mode high_res using the camera camera0 on the rover rover1 from the waypoint waypoint0, communicate the image data of objective objective1 in mode high_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint3, calibrate the camera camera0 on rover rover1 for the objective objective1 at the waypoint waypoint0, take an image of the objective objective1 in mode low_res using the camera camera0 on the rover rover1 from the waypoint waypoint0, communicate the image data of objective objective1 in mode low_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint3, unload the store store0 from the rover rover0, sample rock at waypoint waypoint4 with rover rover0 and store in store store0, communicate the rock data from the rover rover0 at waypoint waypoint4 to the lander general at waypoint waypoint3 via waypoint waypoint4, calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint0\"; which of the following actions can be removed from this plan and still have a valid plan? A. communicate the rock data from the rover rover0 at waypoint waypoint5 to the lander general at waypoint waypoint3 via waypoint waypoint5. B. unload the store store0 from the rover rover0. C. take an image of the objective objective0 in mode high_res using the camera camera0 on the rover rover1 from the waypoint waypoint0. D. calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["communicate the rock data from the rover rover0 at waypoint waypoint5 to the lander general at waypoint waypoint3 via waypoint waypoint5", "unload the store store0 from the rover rover0", "take an image of the objective objective0 in mode high_res using the camera camera0 on the rover rover1 from the waypoint waypoint0", "calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint0"]}, "query": "Given the plan: \"calibrate the camera camera0 on rover rover1 for the objective objective1 at the waypoint waypoint0, take an image of the objective objective0 in mode high_res using the camera camera0 on the rover rover1 from the waypoint waypoint0, communicate the image data of objective objective0 in mode high_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint3, navigate with rover rover1 to waypoint waypoint3 from waypoint waypoint0, sample rock at waypoint waypoint3 with rover rover1 and store in store store1, sample rock at waypoint waypoint5 with rover rover0 and store in store store0, communicate the rock data from the rover rover0 at waypoint waypoint5 to the lander general at waypoint waypoint3 via waypoint waypoint5, navigate with rover rover0 to waypoint waypoint4 from waypoint waypoint5, navigate with rover rover1 to waypoint waypoint0 from waypoint waypoint3, communicate the rock data from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3 via waypoint waypoint3, calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint0, take an image of the objective objective1 in mode colour using the camera camera1 on the rover rover1 from the waypoint waypoint0, communicate the image data of objective objective1 in mode colour from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint3, calibrate the camera camera0 on rover rover1 for the objective objective1 at the waypoint waypoint0, take an image of the objective objective1 in mode high_res using the camera camera0 on the rover rover1 from the waypoint waypoint0, communicate the image data of objective objective1 in mode high_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint3, calibrate the camera camera0 on rover rover1 for the objective objective1 at the waypoint waypoint0, take an image of the objective objective1 in mode low_res using the camera camera0 on the rover rover1 from the waypoint waypoint0, communicate the image data of objective objective1 in mode low_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint3, unload the store store0 from the rover rover0, sample rock at waypoint waypoint4 with rover rover0 and store in store store0, communicate the rock data from the rover rover0 at waypoint waypoint4 to the lander general at waypoint waypoint3 via waypoint waypoint4, calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint0\"; which action can be removed from this plan?", "answer": "D"}
{"id": 1165940311680938554, "group": "action_justification_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 supports low_res and colour. Camera camera1 supports colour and low_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint4, waypoint4 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint0: waypoint4, waypoint2, waypoint3, and waypoint1. Waypoint(s) are visible from waypoint4: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0, waypoint4, and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0, waypoint1, and waypoint3. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Objective objective0 is visible from waypoint1 and waypoint2. Objective objective1 is visible from waypoint4. Lander general is at waypoint waypoint3.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint2. Rocks can be sampled at the following location(s): waypoint1 and waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Image objective0 was communicated in mode colour, Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, Rock data was communicated from waypoint waypoint1;, and Image objective0 was communicated in mode low_res.", "question": "Given the plan: \"calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint2, navigate rover rover1 from waypoint waypoint2 to waypoint waypoint0, sample rock at waypoint waypoint0 with rover rover1 and store in store store1, transmit the rock data from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3 using waypoint waypoint0 as a relay, navigate rover rover0 from waypoint waypoint0 to waypoint waypoint1, calibrate the camera camera0 on rover rover0 for the objective objective0 at the waypoint waypoint1, navigate rover rover0 from waypoint waypoint1 to waypoint waypoint4, capture an image of objective objective1 in mode low_res using the camera camera0 on the rover rover0 from waypoint waypoint4, navigate rover rover1 from waypoint waypoint0 to waypoint waypoint2, navigate rover rover1 from waypoint waypoint2 to waypoint waypoint1, capture an image of objective objective0 in mode low_res using the camera camera1 on the rover rover1 from waypoint waypoint1, calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint1, capture an image of objective objective0 in mode colour using the camera camera1 on the rover rover1 from waypoint waypoint1, navigate rover rover0 from waypoint waypoint4 to waypoint waypoint1, navigate rover rover1 from waypoint waypoint1 to waypoint waypoint2, communicate image data of objective objective0 in mode colour from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3, communicate image data of objective objective0 in mode low_res from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3, navigate rover rover0 from waypoint waypoint1 to waypoint waypoint0, communicate image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint3, collect a soil sample from waypoint waypoint0 using rover rover0 and deposit it in the store store0, communicate the soil data from rover rover0 at waypoint waypoint0 with the soil analysis of waypoint waypoint0 to lander general at waypoint waypoint3, navigate rover rover1 from waypoint waypoint2 to waypoint waypoint1, empty the content from store store1 of the rover rover1, sample rock at waypoint waypoint1 with rover rover1 and store in store store1, navigate rover rover1 from waypoint waypoint1 to waypoint waypoint2, transmit the rock data from the rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint3 using waypoint waypoint1 as a relay, navigate rover rover1 from waypoint waypoint2 to waypoint waypoint0\"; which of the following actions can be removed from this plan and still have a valid plan? A. navigate rover rover1 from waypoint waypoint2 to waypoint waypoint0. B. sample rock at waypoint waypoint0 with rover rover1 and store in store store1. C. calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint2. D. navigate rover rover0 from waypoint waypoint4 to waypoint waypoint1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["navigate rover rover1 from waypoint waypoint2 to waypoint waypoint0", "sample rock at waypoint waypoint0 with rover rover1 and store in store store1", "calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint2", "navigate rover rover0 from waypoint waypoint4 to waypoint waypoint1"]}, "query": "Given the plan: \"calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint2, navigate rover rover1 from waypoint waypoint2 to waypoint waypoint0, sample rock at waypoint waypoint0 with rover rover1 and store in store store1, transmit the rock data from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3 using waypoint waypoint0 as a relay, navigate rover rover0 from waypoint waypoint0 to waypoint waypoint1, calibrate the camera camera0 on rover rover0 for the objective objective0 at the waypoint waypoint1, navigate rover rover0 from waypoint waypoint1 to waypoint waypoint4, capture an image of objective objective1 in mode low_res using the camera camera0 on the rover rover0 from waypoint waypoint4, navigate rover rover1 from waypoint waypoint0 to waypoint waypoint2, navigate rover rover1 from waypoint waypoint2 to waypoint waypoint1, capture an image of objective objective0 in mode low_res using the camera camera1 on the rover rover1 from waypoint waypoint1, calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint1, capture an image of objective objective0 in mode colour using the camera camera1 on the rover rover1 from waypoint waypoint1, navigate rover rover0 from waypoint waypoint4 to waypoint waypoint1, navigate rover rover1 from waypoint waypoint1 to waypoint waypoint2, communicate image data of objective objective0 in mode colour from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3, communicate image data of objective objective0 in mode low_res from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3, navigate rover rover0 from waypoint waypoint1 to waypoint waypoint0, communicate image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint3, collect a soil sample from waypoint waypoint0 using rover rover0 and deposit it in the store store0, communicate the soil data from rover rover0 at waypoint waypoint0 with the soil analysis of waypoint waypoint0 to lander general at waypoint waypoint3, navigate rover rover1 from waypoint waypoint2 to waypoint waypoint1, empty the content from store store1 of the rover rover1, sample rock at waypoint waypoint1 with rover rover1 and store in store store1, navigate rover rover1 from waypoint waypoint1 to waypoint waypoint2, transmit the rock data from the rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint3 using waypoint waypoint1 as a relay, navigate rover rover1 from waypoint waypoint2 to waypoint waypoint0\"; which action can be removed from this plan?", "answer": "A"}
{"id": 153146091927571572, "group": "action_justification_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 supports low_res and colour. Camera camera1 supports colour and low_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint4, waypoint4 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint0: waypoint4, waypoint2, waypoint3, and waypoint1. Waypoint(s) are visible from waypoint4: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0, waypoint4, and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0, waypoint1, and waypoint3. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Objective objective0 is visible from waypoint1 and waypoint2. Objective objective1 is visible from waypoint4. Lander general is at waypoint waypoint3.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint2. Rocks can be sampled at the following location(s): waypoint1 and waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Image objective0 was communicated in mode colour, Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, Rock data was communicated from waypoint waypoint1;, and Image objective0 was communicated in mode low_res.", "question": "Given the plan: \"calibrate the camera camera1 on the rover rover1 for the objective objective0 at the waypoint waypoint2, collect a soil sample from waypoint waypoint0 using rover rover0 and deposit it in the store store0, navigate with rover rover1 from waypoint waypoint2 to waypoint waypoint0, communicate the soil data from rover rover0 at waypoint waypoint0 with the soil analysis of waypoint waypoint0 to lander general at waypoint waypoint3, navigate with rover rover0 from waypoint waypoint0 to waypoint waypoint1, calibrate the camera camera0 on the rover rover0 for the objective objective0 at the waypoint waypoint1, navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint4, capture an image of objective objective1 in mode low_res using the camera camera0 on the rover rover0 from waypoint waypoint4, navigate with rover rover0 from waypoint waypoint4 to waypoint waypoint1, calibrate the camera camera0 on the rover rover0 for the objective objective0 at the waypoint waypoint1, collect a sample from the waypoint waypoint0 using the rover rover1 and store it in the store store1, transmit the rock data from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3 through waypoint waypoint0, capture an image of objective objective0 in mode colour using the camera camera0 on the rover rover0 from waypoint waypoint1, navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint0, transmit image data of objective objective0 in mode colour from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint3, transmit image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint3, transmit image data of objective objective0 in mode colour from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint3, transmit image data of objective objective0 in mode colour from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint3, navigate with rover rover1 from waypoint waypoint0 to waypoint waypoint2, navigate with rover rover1 from waypoint waypoint2 to waypoint waypoint1, capture an image of objective objective0 in mode low_res using the camera camera1 on the rover rover1 from waypoint waypoint1, navigate with rover rover1 from waypoint waypoint1 to waypoint waypoint2, transmit image data of objective objective0 in mode low_res from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint3, empty the content from store store1 of the rover rover1, navigate with rover rover1 from waypoint waypoint2 to waypoint waypoint1, collect a sample from the waypoint waypoint1 using the rover rover1 and store it in the store store1, navigate with rover rover1 from waypoint waypoint1 to waypoint waypoint2, transmit the rock data from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint3 through waypoint waypoint1\"; which of the following actions can be removed from this plan and still have a valid plan? A. transmit image data of objective objective0 in mode colour from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint3. B. transmit the rock data from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint3 through waypoint waypoint1. C. transmit image data of objective objective0 in mode low_res from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint3. D. navigate with rover rover1 from waypoint waypoint2 to waypoint waypoint1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["transmit image data of objective objective0 in mode colour from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint3", "transmit the rock data from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint3 through waypoint waypoint1", "transmit image data of objective objective0 in mode low_res from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint3", "navigate with rover rover1 from waypoint waypoint2 to waypoint waypoint1"]}, "query": "Given the plan: \"calibrate the camera camera1 on the rover rover1 for the objective objective0 at the waypoint waypoint2, collect a soil sample from waypoint waypoint0 using rover rover0 and deposit it in the store store0, navigate with rover rover1 from waypoint waypoint2 to waypoint waypoint0, communicate the soil data from rover rover0 at waypoint waypoint0 with the soil analysis of waypoint waypoint0 to lander general at waypoint waypoint3, navigate with rover rover0 from waypoint waypoint0 to waypoint waypoint1, calibrate the camera camera0 on the rover rover0 for the objective objective0 at the waypoint waypoint1, navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint4, capture an image of objective objective1 in mode low_res using the camera camera0 on the rover rover0 from waypoint waypoint4, navigate with rover rover0 from waypoint waypoint4 to waypoint waypoint1, calibrate the camera camera0 on the rover rover0 for the objective objective0 at the waypoint waypoint1, collect a sample from the waypoint waypoint0 using the rover rover1 and store it in the store store1, transmit the rock data from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3 through waypoint waypoint0, capture an image of objective objective0 in mode colour using the camera camera0 on the rover rover0 from waypoint waypoint1, navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint0, transmit image data of objective objective0 in mode colour from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint3, transmit image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint3, transmit image data of objective objective0 in mode colour from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint3, transmit image data of objective objective0 in mode colour from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint3, navigate with rover rover1 from waypoint waypoint0 to waypoint waypoint2, navigate with rover rover1 from waypoint waypoint2 to waypoint waypoint1, capture an image of objective objective0 in mode low_res using the camera camera1 on the rover rover1 from waypoint waypoint1, navigate with rover rover1 from waypoint waypoint1 to waypoint waypoint2, transmit image data of objective objective0 in mode low_res from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint3, empty the content from store store1 of the rover rover1, navigate with rover rover1 from waypoint waypoint2 to waypoint waypoint1, collect a sample from the waypoint waypoint1 using the rover rover1 and store it in the store store1, navigate with rover rover1 from waypoint waypoint1 to waypoint waypoint2, transmit the rock data from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint3 through waypoint waypoint1\"; which action can be removed from this plan?", "answer": "A"}
{"id": -510643026920839175, "group": "action_justification_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 7 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  No rovers are equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 and camera1 on board. Rover rover0 has camera2 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective1. Camera camera2 supports colour and low_res. Camera camera0 supports high_res and low_res and colour. Camera camera1 supports colour. Rover rover1 can traverse from waypoint3 to waypoint0, waypoint0 to waypoint2, waypoint0 to waypoint3, waypoint2 to waypoint0, waypoint6 to waypoint0, waypoint4 to waypoint2, waypoint1 to waypoint6, waypoint6 to waypoint1, waypoint2 to waypoint4, waypoint0 to waypoint5, waypoint5 to waypoint0, waypoint0 to waypoint6. Rover rover0 can traverse from waypoint0 to waypoint3, waypoint5 to waypoint4, waypoint5 to waypoint1, waypoint1 to waypoint5, waypoint5 to waypoint6, waypoint0 to waypoint5, waypoint6 to waypoint5, waypoint2 to waypoint5, waypoint5 to waypoint2, waypoint4 to waypoint5, waypoint5 to waypoint0, waypoint3 to waypoint0. Waypoint(s) are visible from waypoint4: waypoint6, waypoint2, waypoint5, waypoint1, and waypoint3. Waypoint(s) are visible from waypoint0: waypoint5, waypoint3, waypoint2, and waypoint6. Waypoint(s) are visible from waypoint1: waypoint6, waypoint4, waypoint5, waypoint3, and waypoint2. Waypoint(s) are visible from waypoint3: waypoint5, waypoint1, waypoint0, waypoint6, waypoint2, and waypoint4. Waypoint(s) are visible from waypoint5: waypoint2, waypoint6, waypoint4, waypoint0, waypoint1, and waypoint3. Waypoint(s) are visible from waypoint6: waypoint0, waypoint1, waypoint3, waypoint4, and waypoint5. Waypoint(s) are visible from waypoint2: waypoint0, waypoint1, waypoint3, waypoint4, and waypoint5. Objective objective1 is visible from waypoint2, waypoint5, waypoint0, and waypoint4. Objective objective0 is visible from waypoint0. Lander general is at waypoint waypoint3.  Currently, Rover rover0 is at waypoint5. Rover rover1 is at waypoint0. Rocks can be sampled at the following location(s): waypoint5, waypoint6, waypoint2, waypoint4, and waypoint3. Soil can be sampled at the following location(s): waypoint1, waypoint4, waypoint3, and waypoint6. Rovers rover0 and rover1 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Rock data was communicated from waypoint waypoint5;, Image objective0 was communicated in mode high_res, Image objective1 was communicated in mode high_res, Rock data was communicated from waypoint waypoint3;, Rock data was communicated from waypoint waypoint4;, Image objective1 was communicated in mode colour, and Image objective1 was communicated in mode low_res.", "question": "Given the plan: \"calibrate camera camera0 on rover rover1 for objective objective1 at waypoint waypoint0, capture an image of the objective objective0 in mode high_res with the camera camera0 on the rover rover1 at waypoint waypoint0, transmit the image data of the objective objective0 in mode high_res from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3, guide the rover rover1 from waypoint waypoint0 to waypoint waypoint3, sample rock at waypoint waypoint3 with rover rover1 and store in store store1, sample rock at waypoint waypoint5 with rover rover0 and store in store store0, transmit the rock data from the rover rover0 at waypoint waypoint5 to the lander general at waypoint waypoint3 using waypoint waypoint5 as a relay, guide the rover rover0 from waypoint waypoint5 to waypoint waypoint4, guide the rover rover1 from waypoint waypoint3 to waypoint waypoint0, transmit the rock data from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3 using waypoint waypoint3 as a relay, calibrate camera camera1 on rover rover1 for objective objective0 at waypoint waypoint0, capture an image of the objective objective1 in mode colour with the camera camera1 on the rover rover1 at waypoint waypoint0, transmit the image data of the objective objective1 in mode colour from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3, calibrate camera camera0 on rover rover1 for objective objective1 at waypoint waypoint0, capture an image of the objective objective1 in mode high_res with the camera camera0 on the rover rover1 at waypoint waypoint0, transmit the image data of the objective objective1 in mode high_res from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3, calibrate camera camera0 on rover rover1 for objective objective1 at waypoint waypoint0, capture an image of the objective objective1 in mode low_res with the camera camera0 on the rover rover1 at waypoint waypoint0, transmit the image data of the objective objective1 in mode low_res from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3, guide the rover rover1 from waypoint waypoint0 to waypoint waypoint5, transmit the rock data from the rover rover1 at waypoint waypoint5 to the lander general at waypoint waypoint3 using waypoint waypoint3 as a relay, empty the content from store store0 of the rover rover0, sample rock at waypoint waypoint4 with rover rover0 and store in store store0, transmit the rock data from the rover rover0 at waypoint waypoint4 to the lander general at waypoint waypoint3 using waypoint waypoint4 as a relay\"; which of the following actions can be removed from this plan and still have a valid plan? A. calibrate camera camera1 on rover rover1 for objective objective0 at waypoint waypoint0. B. guide the rover rover0 from waypoint waypoint5 to waypoint waypoint4. C. capture an image of the objective objective1 in mode high_res with the camera camera0 on the rover rover1 at waypoint waypoint0. D. transmit the rock data from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3 using waypoint waypoint3 as a relay.", "choices": {"label": ["A", "B", "C", "D"], "text": ["calibrate camera camera1 on rover rover1 for objective objective0 at waypoint waypoint0", "guide the rover rover0 from waypoint waypoint5 to waypoint waypoint4", "capture an image of the objective objective1 in mode high_res with the camera camera0 on the rover rover1 at waypoint waypoint0", "transmit the rock data from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3 using waypoint waypoint3 as a relay"]}, "query": "Given the plan: \"calibrate camera camera0 on rover rover1 for objective objective1 at waypoint waypoint0, capture an image of the objective objective0 in mode high_res with the camera camera0 on the rover rover1 at waypoint waypoint0, transmit the image data of the objective objective0 in mode high_res from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3, guide the rover rover1 from waypoint waypoint0 to waypoint waypoint3, sample rock at waypoint waypoint3 with rover rover1 and store in store store1, sample rock at waypoint waypoint5 with rover rover0 and store in store store0, transmit the rock data from the rover rover0 at waypoint waypoint5 to the lander general at waypoint waypoint3 using waypoint waypoint5 as a relay, guide the rover rover0 from waypoint waypoint5 to waypoint waypoint4, guide the rover rover1 from waypoint waypoint3 to waypoint waypoint0, transmit the rock data from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3 using waypoint waypoint3 as a relay, calibrate camera camera1 on rover rover1 for objective objective0 at waypoint waypoint0, capture an image of the objective objective1 in mode colour with the camera camera1 on the rover rover1 at waypoint waypoint0, transmit the image data of the objective objective1 in mode colour from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3, calibrate camera camera0 on rover rover1 for objective objective1 at waypoint waypoint0, capture an image of the objective objective1 in mode high_res with the camera camera0 on the rover rover1 at waypoint waypoint0, transmit the image data of the objective objective1 in mode high_res from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3, calibrate camera camera0 on rover rover1 for objective objective1 at waypoint waypoint0, capture an image of the objective objective1 in mode low_res with the camera camera0 on the rover rover1 at waypoint waypoint0, transmit the image data of the objective objective1 in mode low_res from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3, guide the rover rover1 from waypoint waypoint0 to waypoint waypoint5, transmit the rock data from the rover rover1 at waypoint waypoint5 to the lander general at waypoint waypoint3 using waypoint waypoint3 as a relay, empty the content from store store0 of the rover rover0, sample rock at waypoint waypoint4 with rover rover0 and store in store store0, transmit the rock data from the rover rover0 at waypoint waypoint4 to the lander general at waypoint waypoint3 using waypoint waypoint4 as a relay\"; which action can be removed from this plan?", "answer": "D"}
{"id": -5961249117379886923, "group": "action_justification_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective0. Camera camera2 supports high_res and colour. Camera camera0 supports colour. Camera camera1 supports low_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint2. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, and Image objective1 was communicated in mode colour.", "question": "Given the plan: \"guide the rover rover0 from waypoint waypoint0 to waypoint waypoint1, guide the rover rover0 from waypoint waypoint1 to waypoint waypoint0, guide the rover rover1 from waypoint waypoint2 to waypoint waypoint0, sample soil at waypoint waypoint0 with rover rover0 and store in the store store0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, guide the rover rover0 from waypoint waypoint0 to waypoint waypoint2, collect a sample from the waypoint waypoint0 using the rover rover1 and store it in the store store1, calibrate the camera camera0 on the rover rover0 for the objective objective1 at waypoint waypoint2, communicate the rock data from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1 via waypoint waypoint0, take a picture of the objective objective1 in mode colour using the camera camera0 mounted on the rover rover0 from the waypoint waypoint2, transmit the image data of the objective objective1 in mode colour from the rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. collect a sample from the waypoint waypoint0 using the rover rover1 and store it in the store store1 and calibrate the camera camera0 on the rover rover0 for the objective objective1 at waypoint waypoint2. B. guide the rover rover0 from waypoint waypoint0 to waypoint waypoint1 and guide the rover rover0 from waypoint waypoint1 to waypoint waypoint0. C. calibrate the camera camera0 on the rover rover0 for the objective objective1 at waypoint waypoint2 and communicate the rock data from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1 via waypoint waypoint0. D. sample soil at waypoint waypoint0 with rover rover0 and store in the store store0 and transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["collect a sample from the waypoint waypoint0 using the rover rover1 and store it in the store store1 and calibrate the camera camera0 on the rover rover0 for the objective objective1 at waypoint waypoint2", "guide the rover rover0 from waypoint waypoint0 to waypoint waypoint1 and guide the rover rover0 from waypoint waypoint1 to waypoint waypoint0", "calibrate the camera camera0 on the rover rover0 for the objective objective1 at waypoint waypoint2 and communicate the rock data from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1 via waypoint waypoint0", "sample soil at waypoint waypoint0 with rover rover0 and store in the store store0 and transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0"]}, "query": "Given the plan: \"guide the rover rover0 from waypoint waypoint0 to waypoint waypoint1, guide the rover rover0 from waypoint waypoint1 to waypoint waypoint0, guide the rover rover1 from waypoint waypoint2 to waypoint waypoint0, sample soil at waypoint waypoint0 with rover rover0 and store in the store store0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, guide the rover rover0 from waypoint waypoint0 to waypoint waypoint2, collect a sample from the waypoint waypoint0 using the rover rover1 and store it in the store store1, calibrate the camera camera0 on the rover rover0 for the objective objective1 at waypoint waypoint2, communicate the rock data from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1 via waypoint waypoint0, take a picture of the objective objective1 in mode colour using the camera camera0 mounted on the rover rover0 from the waypoint waypoint2, transmit the image data of the objective objective1 in mode colour from the rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1\"; which pair of consecutive actions can be removed from this plan?", "answer": "B"}
{"id": 3624680473448194386, "group": "action_justification_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 7 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  No rovers are equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 and camera1 on board. Rover rover0 has camera2 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective1. Camera camera2 supports colour and low_res. Camera camera0 supports high_res and low_res and colour. Camera camera1 supports colour. Rover rover1 can traverse from waypoint3 to waypoint0, waypoint0 to waypoint2, waypoint0 to waypoint3, waypoint2 to waypoint0, waypoint6 to waypoint0, waypoint4 to waypoint2, waypoint1 to waypoint6, waypoint6 to waypoint1, waypoint2 to waypoint4, waypoint0 to waypoint5, waypoint5 to waypoint0, waypoint0 to waypoint6. Rover rover0 can traverse from waypoint0 to waypoint3, waypoint5 to waypoint4, waypoint5 to waypoint1, waypoint1 to waypoint5, waypoint5 to waypoint6, waypoint0 to waypoint5, waypoint6 to waypoint5, waypoint2 to waypoint5, waypoint5 to waypoint2, waypoint4 to waypoint5, waypoint5 to waypoint0, waypoint3 to waypoint0. Waypoint(s) are visible from waypoint4: waypoint6, waypoint2, waypoint5, waypoint1, and waypoint3. Waypoint(s) are visible from waypoint0: waypoint5, waypoint3, waypoint2, and waypoint6. Waypoint(s) are visible from waypoint1: waypoint6, waypoint4, waypoint5, waypoint3, and waypoint2. Waypoint(s) are visible from waypoint3: waypoint5, waypoint1, waypoint0, waypoint6, waypoint2, and waypoint4. Waypoint(s) are visible from waypoint5: waypoint2, waypoint6, waypoint4, waypoint0, waypoint1, and waypoint3. Waypoint(s) are visible from waypoint6: waypoint0, waypoint1, waypoint3, waypoint4, and waypoint5. Waypoint(s) are visible from waypoint2: waypoint0, waypoint1, waypoint3, waypoint4, and waypoint5. Objective objective1 is visible from waypoint2, waypoint5, waypoint0, and waypoint4. Objective objective0 is visible from waypoint0. Lander general is at waypoint waypoint3.  Currently, Rover rover0 is at waypoint5. Rover rover1 is at waypoint0. Rocks can be sampled at the following location(s): waypoint5, waypoint6, waypoint2, waypoint4, and waypoint3. Soil can be sampled at the following location(s): waypoint1, waypoint4, waypoint3, and waypoint6. Rovers rover0 and rover1 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Rock data was communicated from waypoint waypoint5;, Image objective0 was communicated in mode high_res, Image objective1 was communicated in mode high_res, Rock data was communicated from waypoint waypoint3;, Rock data was communicated from waypoint waypoint4;, Image objective1 was communicated in mode colour, and Image objective1 was communicated in mode low_res.", "question": "Given the plan: \"adjust the camera camera0 on the rover rover1 for the objective objective1 at waypoint waypoint0, capture an image of the objective objective0 in mode high_res with the camera camera0 on the rover rover1 at waypoint waypoint0, transmit image data of objective objective0 in mode high_res from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3, navigate with rover rover1 from waypoint waypoint0 to waypoint waypoint3, sample the rock at waypoint waypoint3 with rover rover1 and store it in store store1, sample the rock at waypoint waypoint5 with rover rover0 and store it in store store0, communicate the rock data from rover rover0 at waypoint waypoint5 to lander general at waypoint waypoint3 via waypoint waypoint5, navigate with rover rover0 from waypoint waypoint5 to waypoint waypoint4, navigate with rover rover1 from waypoint waypoint3 to waypoint waypoint0, communicate the rock data from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint3 via waypoint waypoint3, adjust the camera camera1 on the rover rover1 for the objective objective0 at waypoint waypoint0, capture an image of the objective objective1 in mode colour with the camera camera1 on the rover rover1 at waypoint waypoint0, transmit image data of objective objective1 in mode colour from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3, adjust the camera camera0 on the rover rover1 for the objective objective1 at waypoint waypoint0, capture an image of the objective objective1 in mode high_res with the camera camera0 on the rover rover1 at waypoint waypoint0, transmit image data of objective objective1 in mode high_res from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3, adjust the camera camera0 on the rover rover1 for the objective objective1 at waypoint waypoint0, capture an image of the objective objective1 in mode low_res with the camera camera0 on the rover rover1 at waypoint waypoint0, transmit image data of objective objective1 in mode low_res from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3, unload the store store0 from the rover rover0, sample the rock at waypoint waypoint4 with rover rover0 and store it in store store0, communicate the rock data from rover rover0 at waypoint waypoint4 to lander general at waypoint waypoint3 via waypoint waypoint4, navigate with rover rover1 from waypoint waypoint0 to waypoint waypoint2\"; which of the following actions can be removed from this plan and still have a valid plan? A. unload the store store0 from the rover rover0. B. navigate with rover rover1 from waypoint waypoint0 to waypoint waypoint2. C. communicate the rock data from rover rover0 at waypoint waypoint4 to lander general at waypoint waypoint3 via waypoint waypoint4. D. communicate the rock data from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint3 via waypoint waypoint3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["unload the store store0 from the rover rover0", "navigate with rover rover1 from waypoint waypoint0 to waypoint waypoint2", "communicate the rock data from rover rover0 at waypoint waypoint4 to lander general at waypoint waypoint3 via waypoint waypoint4", "communicate the rock data from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint3 via waypoint waypoint3"]}, "query": "Given the plan: \"adjust the camera camera0 on the rover rover1 for the objective objective1 at waypoint waypoint0, capture an image of the objective objective0 in mode high_res with the camera camera0 on the rover rover1 at waypoint waypoint0, transmit image data of objective objective0 in mode high_res from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3, navigate with rover rover1 from waypoint waypoint0 to waypoint waypoint3, sample the rock at waypoint waypoint3 with rover rover1 and store it in store store1, sample the rock at waypoint waypoint5 with rover rover0 and store it in store store0, communicate the rock data from rover rover0 at waypoint waypoint5 to lander general at waypoint waypoint3 via waypoint waypoint5, navigate with rover rover0 from waypoint waypoint5 to waypoint waypoint4, navigate with rover rover1 from waypoint waypoint3 to waypoint waypoint0, communicate the rock data from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint3 via waypoint waypoint3, adjust the camera camera1 on the rover rover1 for the objective objective0 at waypoint waypoint0, capture an image of the objective objective1 in mode colour with the camera camera1 on the rover rover1 at waypoint waypoint0, transmit image data of objective objective1 in mode colour from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3, adjust the camera camera0 on the rover rover1 for the objective objective1 at waypoint waypoint0, capture an image of the objective objective1 in mode high_res with the camera camera0 on the rover rover1 at waypoint waypoint0, transmit image data of objective objective1 in mode high_res from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3, adjust the camera camera0 on the rover rover1 for the objective objective1 at waypoint waypoint0, capture an image of the objective objective1 in mode low_res with the camera camera0 on the rover rover1 at waypoint waypoint0, transmit image data of objective objective1 in mode low_res from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3, unload the store store0 from the rover rover0, sample the rock at waypoint waypoint4 with rover rover0 and store it in store store0, communicate the rock data from rover rover0 at waypoint waypoint4 to lander general at waypoint waypoint3 via waypoint waypoint4, navigate with rover rover1 from waypoint waypoint0 to waypoint waypoint2\"; which action can be removed from this plan?", "answer": "B"}
{"id": -6230322020273258540, "group": "action_justification_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective0. Camera camera2 supports high_res and colour. Camera camera0 supports colour. Camera camera1 supports low_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint2. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, and Image objective1 was communicated in mode colour.", "question": "Given the plan: \"guide the rover rover0 from waypoint waypoint0 to waypoint waypoint2, guide the rover rover0 from waypoint waypoint2 to waypoint waypoint0, guide the rover rover1 from waypoint waypoint2 to waypoint waypoint0, guide the rover rover0 from waypoint waypoint0 to waypoint waypoint2, calibrate camera camera0 on rover rover0 for objective objective1 at waypoint waypoint2, sample rock at waypoint waypoint0 with rover rover1 and store in store store1, transmit the rock data from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint1 through waypoint waypoint0, capture an image of the objective objective1 in mode colour with the camera camera0 on the rover rover0 at waypoint waypoint2, if storey is full, drop store store1 of rover rover1, sample soil at waypoint waypoint0 with rover rover1 and store in store store1, communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1, guide the rover rover0 from waypoint waypoint2 to waypoint waypoint0, communicate the image data of target objective1 in mode colour from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. calibrate camera camera0 on rover rover0 for objective objective1 at waypoint waypoint2 and sample rock at waypoint waypoint0 with rover rover1 and store in store store1. B. guide the rover rover0 from waypoint waypoint2 to waypoint waypoint0 and guide the rover rover1 from waypoint waypoint2 to waypoint waypoint0. C. capture an image of the objective objective1 in mode colour with the camera camera0 on the rover rover0 at waypoint waypoint2 and if storey is full, drop store store1 of rover rover1. D. guide the rover rover0 from waypoint waypoint0 to waypoint waypoint2 and guide the rover rover0 from waypoint waypoint2 to waypoint waypoint0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["calibrate camera camera0 on rover rover0 for objective objective1 at waypoint waypoint2 and sample rock at waypoint waypoint0 with rover rover1 and store in store store1", "guide the rover rover0 from waypoint waypoint2 to waypoint waypoint0 and guide the rover rover1 from waypoint waypoint2 to waypoint waypoint0", "capture an image of the objective objective1 in mode colour with the camera camera0 on the rover rover0 at waypoint waypoint2 and if storey is full, drop store store1 of rover rover1", "guide the rover rover0 from waypoint waypoint0 to waypoint waypoint2 and guide the rover rover0 from waypoint waypoint2 to waypoint waypoint0"]}, "query": "Given the plan: \"guide the rover rover0 from waypoint waypoint0 to waypoint waypoint2, guide the rover rover0 from waypoint waypoint2 to waypoint waypoint0, guide the rover rover1 from waypoint waypoint2 to waypoint waypoint0, guide the rover rover0 from waypoint waypoint0 to waypoint waypoint2, calibrate camera camera0 on rover rover0 for objective objective1 at waypoint waypoint2, sample rock at waypoint waypoint0 with rover rover1 and store in store store1, transmit the rock data from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint1 through waypoint waypoint0, capture an image of the objective objective1 in mode colour with the camera camera0 on the rover rover0 at waypoint waypoint2, if storey is full, drop store store1 of rover rover1, sample soil at waypoint waypoint0 with rover rover1 and store in store store1, communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1, guide the rover rover0 from waypoint waypoint2 to waypoint waypoint0, communicate the image data of target objective1 in mode colour from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
{"id": -3885269214599382873, "group": "action_justification_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective0. Camera camera2 supports high_res and colour. Camera camera0 supports colour. Camera camera1 supports low_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint2. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, and Image objective1 was communicated in mode colour.", "question": "Given the plan: \"move the rover rover1 from waypoint waypoint2 to waypoint waypoint0, calibrate the camera camera2 on the rover rover1 for the objective objective0 at the waypoint waypoint0, use the rover rover0 to collect soil samples at waypoint waypoint0 and store them in the store store0, communicate the soil data from the rover rover0 at waypoint waypoint0 with the soil analysis of waypoint waypoint0 to the lander general at waypoint waypoint1, sample rock at waypoint waypoint0 with rover rover1 and store in store store1, communicate rock data from rover rover1 at waypoint waypoint0 about waypoint waypoint0 to lander general at waypoint waypoint1, move the rover rover1 from waypoint waypoint0 to waypoint waypoint2, take an image of objective objective1 in mode colour using camera camera2 on rover rover1 from waypoint waypoint2, transmit the image data of the objective objective1 in mode colour from the rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint1, if storey is full, drop store store0 of rover rover0, move the rover rover1 from waypoint waypoint2 to waypoint waypoint0\"; which of the following actions can be removed from this plan and still have a valid plan? A. sample rock at waypoint waypoint0 with rover rover1 and store in store store1. B. communicate the soil data from the rover rover0 at waypoint waypoint0 with the soil analysis of waypoint waypoint0 to the lander general at waypoint waypoint1. C. move the rover rover1 from waypoint waypoint2 to waypoint waypoint0. D. take an image of objective objective1 in mode colour using camera camera2 on rover rover1 from waypoint waypoint2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["sample rock at waypoint waypoint0 with rover rover1 and store in store store1", "communicate the soil data from the rover rover0 at waypoint waypoint0 with the soil analysis of waypoint waypoint0 to the lander general at waypoint waypoint1", "move the rover rover1 from waypoint waypoint2 to waypoint waypoint0", "take an image of objective objective1 in mode colour using camera camera2 on rover rover1 from waypoint waypoint2"]}, "query": "Given the plan: \"move the rover rover1 from waypoint waypoint2 to waypoint waypoint0, calibrate the camera camera2 on the rover rover1 for the objective objective0 at the waypoint waypoint0, use the rover rover0 to collect soil samples at waypoint waypoint0 and store them in the store store0, communicate the soil data from the rover rover0 at waypoint waypoint0 with the soil analysis of waypoint waypoint0 to the lander general at waypoint waypoint1, sample rock at waypoint waypoint0 with rover rover1 and store in store store1, communicate rock data from rover rover1 at waypoint waypoint0 about waypoint waypoint0 to lander general at waypoint waypoint1, move the rover rover1 from waypoint waypoint0 to waypoint waypoint2, take an image of objective objective1 in mode colour using camera camera2 on rover rover1 from waypoint waypoint2, transmit the image data of the objective objective1 in mode colour from the rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint1, if storey is full, drop store store0 of rover rover0, move the rover rover1 from waypoint waypoint2 to waypoint waypoint0\"; which action can be removed from this plan?", "answer": "C"}
{"id": 2066499650596870774, "group": "action_justification_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera1 can be calibrated on objective1. Camera camera0 can be calibrated on objective2. Camera camera1 supports high_res and low_res. Camera camera0 supports low_res. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint0. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint1. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rocks can be sampled at the following location(s): waypoint2 and waypoint0. Soil can be sampled at the following location(s): waypoint0 and waypoint2. Rovers rover0 and rover1 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Rock data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode high_res, Soil data was communicated from waypoint waypoint2;, Soil data was communicated from waypoint waypoint0;, Image objective0 was communicated in mode low_res, and Rock data was communicated from waypoint waypoint2;.", "question": "Given the plan: \"collect a rock sample from waypoint waypoint2 using rover rover1 and store it in store store1, transmit the rock data from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint1 through waypoint waypoint2, sample the soil at waypoint waypoint2 using the rover rover0, then store it in the storage unit store0, transmit soil data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint2, navigate rover rover0 from waypoint waypoint2 to waypoint waypoint1, calibrate the camera camera1 on rover rover0 for the objective objective1 at the waypoint waypoint1, take a picture of the objective objective1 in mode high_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint1, calibrate the camera camera1 on rover rover0 for the objective objective1 at the waypoint waypoint1, take a picture of the objective objective1 in mode low_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint1, calibrate the camera camera1 on rover rover0 for the objective objective1 at the waypoint waypoint1, take a picture of the objective objective0 in mode low_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint1, navigate rover rover0 from waypoint waypoint1 to waypoint waypoint2, communicate the image data of objective objective0 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of objective objective1 in mode high_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, navigate rover rover0 from waypoint waypoint2 to waypoint waypoint0, drop store store0 of rover rover0, sample the soil at waypoint waypoint0 using the rover rover0, then store it in the storage unit store0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, drop store store0 of rover rover0, collect a rock sample from waypoint waypoint0 using rover rover0 and store it in store store0, transmit the rock data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1 through waypoint waypoint0\"; which of the following actions can be removed from this plan and still have a valid plan? A. transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0. B. navigate rover rover0 from waypoint waypoint1 to waypoint waypoint2. C. sample the soil at waypoint waypoint0 using the rover rover0, then store it in the storage unit store0. D. take a picture of the objective objective0 in mode low_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0", "navigate rover rover0 from waypoint waypoint1 to waypoint waypoint2", "sample the soil at waypoint waypoint0 using the rover rover0, then store it in the storage unit store0", "take a picture of the objective objective0 in mode low_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint1"]}, "query": "Given the plan: \"collect a rock sample from waypoint waypoint2 using rover rover1 and store it in store store1, transmit the rock data from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint1 through waypoint waypoint2, sample the soil at waypoint waypoint2 using the rover rover0, then store it in the storage unit store0, transmit soil data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint2, navigate rover rover0 from waypoint waypoint2 to waypoint waypoint1, calibrate the camera camera1 on rover rover0 for the objective objective1 at the waypoint waypoint1, take a picture of the objective objective1 in mode high_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint1, calibrate the camera camera1 on rover rover0 for the objective objective1 at the waypoint waypoint1, take a picture of the objective objective1 in mode low_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint1, calibrate the camera camera1 on rover rover0 for the objective objective1 at the waypoint waypoint1, take a picture of the objective objective0 in mode low_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint1, navigate rover rover0 from waypoint waypoint1 to waypoint waypoint2, communicate the image data of objective objective0 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of objective objective1 in mode high_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, navigate rover rover0 from waypoint waypoint2 to waypoint waypoint0, drop store store0 of rover rover0, sample the soil at waypoint waypoint0 using the rover rover0, then store it in the storage unit store0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, drop store store0 of rover rover0, collect a rock sample from waypoint waypoint0 using rover rover0 and store it in store store0, transmit the rock data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1 through waypoint waypoint0\"; which action can be removed from this plan?", "answer": "A"}
{"id": 5696217647687082383, "group": "action_justification_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 and camera1 on board. Rover rover0 has camera2 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective1. Camera camera1 supports high_res. Camera camera2 supports high_res. Camera camera0 supports colour and low_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint0, waypoint0 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint2. Objective objective1 is visible from waypoint0 and waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint1. Rover rover1 is at waypoint1. Rocks can be sampled at the following location(s): waypoint2 and waypoint0. Soil can be sampled at the following location(s): waypoint1 and waypoint0. Rovers rover0 and rover1 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Rock data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode high_res, Image objective0 was communicated in mode high_res, Soil data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint1;, and Rock data was communicated from waypoint waypoint2;.", "question": "Given the plan: \"sample the soil at waypoint waypoint1 using the rover rover1, then store it in the storage unit store1, navigate with rover rover0 to waypoint waypoint0 from waypoint waypoint1, sample the rock at waypoint waypoint0 with rover rover0 and store it in store store0, transmit the rock data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1 through waypoint waypoint0, calibrate camera camera2 on rover rover0 for objective objective1 at waypoint waypoint0, take an image of objective objective1 in mode high_res using camera camera2 on rover rover0 from waypoint waypoint0, communicate the image data of target objective1 in mode high_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1, navigate with rover rover1 to waypoint waypoint0 from waypoint waypoint1, communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint1 to lander general at waypoint waypoint1, navigate with rover rover0 to waypoint waypoint1 from waypoint waypoint0, calibrate camera camera2 on rover rover0 for objective objective1 at waypoint waypoint1, navigate with rover rover0 to waypoint waypoint2 from waypoint waypoint1, take an image of objective objective0 in mode high_res using camera camera2 on rover rover0 from waypoint waypoint2, communicate the image data of target objective0 in mode high_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, if storey is full, drop store store0 of rover rover0, sample the rock at waypoint waypoint2 with rover rover0 and store it in store store0, transmit the rock data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1 through waypoint waypoint2, if storey is full, drop store store1 of rover rover1, sample the soil at waypoint waypoint0 using the rover rover1, then store it in the storage unit store1, communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1, communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint1 to lander general at waypoint waypoint1\"; which of the following actions can be removed from this plan and still have a valid plan? A. communicate the image data of target objective1 in mode high_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1. B. transmit the rock data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1 through waypoint waypoint2. C. communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint1 to lander general at waypoint waypoint1. D. sample the soil at waypoint waypoint0 using the rover rover1, then store it in the storage unit store1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["communicate the image data of target objective1 in mode high_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1", "transmit the rock data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1 through waypoint waypoint2", "communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint1 to lander general at waypoint waypoint1", "sample the soil at waypoint waypoint0 using the rover rover1, then store it in the storage unit store1"]}, "query": "Given the plan: \"sample the soil at waypoint waypoint1 using the rover rover1, then store it in the storage unit store1, navigate with rover rover0 to waypoint waypoint0 from waypoint waypoint1, sample the rock at waypoint waypoint0 with rover rover0 and store it in store store0, transmit the rock data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1 through waypoint waypoint0, calibrate camera camera2 on rover rover0 for objective objective1 at waypoint waypoint0, take an image of objective objective1 in mode high_res using camera camera2 on rover rover0 from waypoint waypoint0, communicate the image data of target objective1 in mode high_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1, navigate with rover rover1 to waypoint waypoint0 from waypoint waypoint1, communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint1 to lander general at waypoint waypoint1, navigate with rover rover0 to waypoint waypoint1 from waypoint waypoint0, calibrate camera camera2 on rover rover0 for objective objective1 at waypoint waypoint1, navigate with rover rover0 to waypoint waypoint2 from waypoint waypoint1, take an image of objective objective0 in mode high_res using camera camera2 on rover rover0 from waypoint waypoint2, communicate the image data of target objective0 in mode high_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, if storey is full, drop store store0 of rover rover0, sample the rock at waypoint waypoint2 with rover rover0 and store it in store store0, transmit the rock data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1 through waypoint waypoint2, if storey is full, drop store store1 of rover rover1, sample the soil at waypoint waypoint0 using the rover rover1, then store it in the storage unit store1, communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1, communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint1 to lander general at waypoint waypoint1\"; which action can be removed from this plan?", "answer": "C"}
{"id": -7050458051006358400, "group": "action_justification_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 and camera1 on board. Rover rover0 has camera2 on board. Camera camera2 can be calibrated on objective2. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera1 supports high_res. Camera camera2 supports high_res. Camera camera0 supports colour and low_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint0, waypoint0 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective1 is visible from waypoint1 and waypoint0. Objective objective0 is visible from waypoint2. Objective objective2 is visible from waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint1. Rover rover1 is at waypoint1. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, and Image objective1 was communicated in mode high_res.", "question": "Given the plan: \"guide the rover rover1 from waypoint waypoint1 to waypoint waypoint2, guide the rover rover1 from waypoint waypoint2 to waypoint waypoint1, guide the rover rover1 from waypoint waypoint1 to waypoint waypoint2, calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint2, calibrate the camera camera0 on rover rover1 for the objective objective0 at the waypoint waypoint2, guide the rover rover1 from waypoint waypoint2 to waypoint waypoint1, capture an image of the objective objective1 in mode low_res with the camera camera0 on the rover rover1 at waypoint waypoint1, guide the rover rover1 from waypoint waypoint1 to waypoint waypoint0, sample soil at waypoint waypoint0 with rover rover1 and store in the store store1, communicate image data of objective objective1 in mode low_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1, guide the rover rover0 from waypoint waypoint1 to waypoint waypoint0, capture an image of the objective objective1 in mode high_res with the camera camera1 on the rover rover1 at waypoint waypoint0, sample the rock at waypoint waypoint0 with rover rover0 and store it in store store0, communicate the rock data from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1 via waypoint waypoint0, communicate image data of objective objective1 in mode high_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1, transmit soil data from rover rover1 located at waypoint waypoint0 to lander general located at waypoint waypoint1 using soil analysis of waypoint waypoint0\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. communicate image data of objective objective1 in mode high_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1 and transmit soil data from rover rover1 located at waypoint waypoint0 to lander general located at waypoint waypoint1 using soil analysis of waypoint waypoint0. B. capture an image of the objective objective1 in mode low_res with the camera camera0 on the rover rover1 at waypoint waypoint1 and guide the rover rover1 from waypoint waypoint1 to waypoint waypoint0. C. guide the rover rover1 from waypoint waypoint1 to waypoint waypoint2 and guide the rover rover1 from waypoint waypoint2 to waypoint waypoint1. D. calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint2 and calibrate the camera camera0 on rover rover1 for the objective objective0 at the waypoint waypoint2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["communicate image data of objective objective1 in mode high_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1 and transmit soil data from rover rover1 located at waypoint waypoint0 to lander general located at waypoint waypoint1 using soil analysis of waypoint waypoint0", "capture an image of the objective objective1 in mode low_res with the camera camera0 on the rover rover1 at waypoint waypoint1 and guide the rover rover1 from waypoint waypoint1 to waypoint waypoint0", "guide the rover rover1 from waypoint waypoint1 to waypoint waypoint2 and guide the rover rover1 from waypoint waypoint2 to waypoint waypoint1", "calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint2 and calibrate the camera camera0 on rover rover1 for the objective objective0 at the waypoint waypoint2"]}, "query": "Given the plan: \"guide the rover rover1 from waypoint waypoint1 to waypoint waypoint2, guide the rover rover1 from waypoint waypoint2 to waypoint waypoint1, guide the rover rover1 from waypoint waypoint1 to waypoint waypoint2, calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint2, calibrate the camera camera0 on rover rover1 for the objective objective0 at the waypoint waypoint2, guide the rover rover1 from waypoint waypoint2 to waypoint waypoint1, capture an image of the objective objective1 in mode low_res with the camera camera0 on the rover rover1 at waypoint waypoint1, guide the rover rover1 from waypoint waypoint1 to waypoint waypoint0, sample soil at waypoint waypoint0 with rover rover1 and store in the store store1, communicate image data of objective objective1 in mode low_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1, guide the rover rover0 from waypoint waypoint1 to waypoint waypoint0, capture an image of the objective objective1 in mode high_res with the camera camera1 on the rover rover1 at waypoint waypoint0, sample the rock at waypoint waypoint0 with rover rover0 and store it in store store0, communicate the rock data from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1 via waypoint waypoint0, communicate image data of objective objective1 in mode high_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1, transmit soil data from rover rover1 located at waypoint waypoint0 to lander general located at waypoint waypoint1 using soil analysis of waypoint waypoint0\"; which pair of consecutive actions can be removed from this plan?", "answer": "C"}
{"id": -1911038771589290469, "group": "action_justification_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera1 can be calibrated on objective1. Camera camera0 can be calibrated on objective2. Camera camera1 supports high_res and low_res. Camera camera0 supports low_res. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint0. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint1. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rocks can be sampled at the following location(s): waypoint2 and waypoint0. Soil can be sampled at the following location(s): waypoint0 and waypoint2. Rovers rover0 and rover1 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Rock data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode high_res, Soil data was communicated from waypoint waypoint2;, Soil data was communicated from waypoint waypoint0;, Image objective0 was communicated in mode low_res, and Rock data was communicated from waypoint waypoint2;.", "question": "Given the plan: \"sample the rock at waypoint waypoint2 with rover rover1 and store it in store store1, communicate the rock data from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint1 via waypoint waypoint2, sample the soil at waypoint waypoint2 using the rover rover0, then store it in the storage unit store0, transmit soil data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint2, navigate with rover rover0 from waypoint waypoint2 to waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective1 at waypoint waypoint1, capture an image of objective objective1 in mode high_res using the camera camera1 on the rover rover0 from waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective1 at waypoint waypoint1, capture an image of objective objective1 in mode low_res using the camera camera1 on the rover rover0 from waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective1 at waypoint waypoint1, capture an image of objective objective0 in mode low_res using the camera camera1 on the rover rover0 from waypoint waypoint1, navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint2, communicate the image data of objective objective0 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of objective objective1 in mode high_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, navigate with rover rover0 from waypoint waypoint2 to waypoint waypoint0, empty the store store0 from rover rover0, sample the soil at waypoint waypoint0 using the rover rover0, then store it in the storage unit store0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, empty the store store0 from rover rover0, sample the rock at waypoint waypoint0 with rover rover0 and store it in store store0, communicate the rock data from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1 via waypoint waypoint0, navigate with rover rover0 from waypoint waypoint0 to waypoint waypoint2\"; which of the following actions can be removed from this plan and still have a valid plan? A. capture an image of objective objective1 in mode high_res using the camera camera1 on the rover rover0 from waypoint waypoint1. B. empty the store store0 from rover rover0. C. navigate with rover rover0 from waypoint waypoint2 to waypoint waypoint0. D. navigate with rover rover0 from waypoint waypoint0 to waypoint waypoint2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["capture an image of objective objective1 in mode high_res using the camera camera1 on the rover rover0 from waypoint waypoint1", "empty the store store0 from rover rover0", "navigate with rover rover0 from waypoint waypoint2 to waypoint waypoint0", "navigate with rover rover0 from waypoint waypoint0 to waypoint waypoint2"]}, "query": "Given the plan: \"sample the rock at waypoint waypoint2 with rover rover1 and store it in store store1, communicate the rock data from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint1 via waypoint waypoint2, sample the soil at waypoint waypoint2 using the rover rover0, then store it in the storage unit store0, transmit soil data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint2, navigate with rover rover0 from waypoint waypoint2 to waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective1 at waypoint waypoint1, capture an image of objective objective1 in mode high_res using the camera camera1 on the rover rover0 from waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective1 at waypoint waypoint1, capture an image of objective objective1 in mode low_res using the camera camera1 on the rover rover0 from waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective1 at waypoint waypoint1, capture an image of objective objective0 in mode low_res using the camera camera1 on the rover rover0 from waypoint waypoint1, navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint2, communicate the image data of objective objective0 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of objective objective1 in mode high_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, navigate with rover rover0 from waypoint waypoint2 to waypoint waypoint0, empty the store store0 from rover rover0, sample the soil at waypoint waypoint0 using the rover rover0, then store it in the storage unit store0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, empty the store store0 from rover rover0, sample the rock at waypoint waypoint0 with rover rover0 and store it in store store0, communicate the rock data from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1 via waypoint waypoint0, navigate with rover rover0 from waypoint waypoint0 to waypoint waypoint2\"; which action can be removed from this plan?", "answer": "D"}
{"id": -480819846947890286, "group": "action_justification_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 and camera1 on board. Rover rover0 has camera2 on board. Camera camera2 can be calibrated on objective2. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera1 supports high_res. Camera camera2 supports high_res. Camera camera0 supports colour and low_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint0, waypoint0 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective1 is visible from waypoint1 and waypoint0. Objective objective0 is visible from waypoint2. Objective objective2 is visible from waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint1. Rover rover1 is at waypoint1. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, and Image objective1 was communicated in mode high_res.", "question": "Given the plan: \"navigate with rover rover1 from waypoint waypoint1 to waypoint waypoint2, calibrate the camera camera0 on rover rover1 for the objective objective0 at the waypoint waypoint2, navigate with rover rover1 from waypoint waypoint2 to waypoint waypoint1, navigate with rover rover1 from waypoint waypoint1 to waypoint waypoint0, capture an image of the objective objective1 in mode low_res with the camera camera0 on the rover rover1 at waypoint waypoint0, sample the soil at waypoint waypoint0 with rover rover1 and store it in store store1, communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1, communicate the image data of target objective1 in mode low_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1, navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint0, calibrate the camera camera2 on rover rover0 for the objective objective2 at the waypoint waypoint0, collect a sample from the waypoint waypoint0 using the rover rover0 and store it in the store store0, communicate the rock data from the rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1 via waypoint waypoint0, capture an image of the objective objective1 in mode high_res with the camera camera2 on the rover rover0 at waypoint waypoint0, communicate the image data of target objective1 in mode high_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1, empty the content from store store1 of the rover rover1\"; which of the following actions can be removed from this plan and still have a valid plan? A. capture an image of the objective objective1 in mode high_res with the camera camera2 on the rover rover0 at waypoint waypoint0. B. empty the content from store store1 of the rover rover1. C. navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint0. D. sample the soil at waypoint waypoint0 with rover rover1 and store it in store store1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["capture an image of the objective objective1 in mode high_res with the camera camera2 on the rover rover0 at waypoint waypoint0", "empty the content from store store1 of the rover rover1", "navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint0", "sample the soil at waypoint waypoint0 with rover rover1 and store it in store store1"]}, "query": "Given the plan: \"navigate with rover rover1 from waypoint waypoint1 to waypoint waypoint2, calibrate the camera camera0 on rover rover1 for the objective objective0 at the waypoint waypoint2, navigate with rover rover1 from waypoint waypoint2 to waypoint waypoint1, navigate with rover rover1 from waypoint waypoint1 to waypoint waypoint0, capture an image of the objective objective1 in mode low_res with the camera camera0 on the rover rover1 at waypoint waypoint0, sample the soil at waypoint waypoint0 with rover rover1 and store it in store store1, communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1, communicate the image data of target objective1 in mode low_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1, navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint0, calibrate the camera camera2 on rover rover0 for the objective objective2 at the waypoint waypoint0, collect a sample from the waypoint waypoint0 using the rover rover0 and store it in the store store0, communicate the rock data from the rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1 via waypoint waypoint0, capture an image of the objective objective1 in mode high_res with the camera camera2 on the rover rover0 at waypoint waypoint0, communicate the image data of target objective1 in mode high_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1, empty the content from store store1 of the rover rover1\"; which action can be removed from this plan?", "answer": "B"}
