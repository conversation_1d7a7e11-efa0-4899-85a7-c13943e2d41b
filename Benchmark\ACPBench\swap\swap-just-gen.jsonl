{"id": 5252367317151271291, "group": "action_justification_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: frank, quentin, xena, liam, vic, and bob. There are 6 items/roles: pliers, sander, knead, wrench, nibbler, and ratchet. Currently, xena is assigned pliers, liam is assigned knead, bob is assigned wrench, vic is assigned nibbler, quentin is assigned ratchet, and frank is assigned sander. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1 with ?a2, ?r1 for ?r2. The goal is to reach a state where the following facts hold: vic is assigned ratchet, xena is assigned wrench, quentin is assigned knead, frank is assigned pliers, bob is assigned nibbler, and liam is assigned sander.", "question": "Simplify the plan \"(swap frank bob sander wrench) (swap frank bob wrench sander) (swap xena bob pliers wrench) (swap quentin vic ratchet nibbler) (swap frank liam sander knead) (swap frank bob knead pliers) (swap bob quentin knead nibbler)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(swap frank bob sander wrench)", "(swap frank bob wrench sander)", "-1"]], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Frank Liam Quentin Vic Xena - agent Knead Nibbler Pliers Ratchet Sander Wrench - role)\n    (:init (assigned Bob Wrench) (assigned Frank Sander) (assigned Liam Knead) (assigned Quentin Ratchet) (assigned Vic Nibbler) (assigned Xena Pliers))\n    (:goal (and (assigned Quentin Knead) (assigned Liam Sander) (assigned Frank Pliers) (assigned Xena Wrench) (assigned Bob Nibbler) (assigned Vic Ratchet)))\n)"}
{"id": -6939648169145133392, "group": "action_justification_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 8 agents: heidi, michelle, dave, carol, xena, alice, zoe, and vic. There are 8 items/roles: guitar, frisbee, quadcopter, iceskates, necklace, slinky, zebra, and whale. Currently, xena is assigned whale, vic is assigned quadcopter, dave is assigned slinky, heidi is assigned necklace, michelle is assigned zebra, alice is assigned iceskates, carol is assigned guitar, and zoe is assigned frisbee. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - exchange ?r1 of ?a1 with ?r2 of ?a2. The goal is to reach a state where the following facts hold: heidi is assigned guitar, alice is assigned zebra, zoe is assigned whale, vic is assigned necklace, michelle is assigned quadcopter, dave is assigned iceskates, xena is assigned slinky, and carol is assigned frisbee.", "question": "Simplify the plan \"(swap zoe xena frisbee whale) (swap zoe xena whale frisbee) (swap michelle vic zebra quadcopter) (swap alice heidi iceskates necklace) (swap vic alice zebra necklace) (swap carol zoe guitar frisbee) (swap dave heidi slinky iceskates) (swap heidi zoe slinky guitar) (swap zoe xena slinky whale)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(swap zoe xena frisbee whale)", "(swap zoe xena whale frisbee)", "-1"]], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-8-36)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Carol Dave Heidi Michelle Vic Xena Zoe - agent Frisbee Guitar IceSkates Necklace Quadcopter Slinky Whale Zebra - role)\n    (:init (assigned Alice IceSkates) (assigned Carol Guitar) (assigned Dave Slinky) (assigned Heidi Necklace) (assigned Michelle Zebra) (assigned Vic Quadcopter) (assigned Xena Whale) (assigned Zoe Frisbee))\n    (:goal (and (assigned Xena Slinky) (assigned Dave IceSkates) (assigned Alice Zebra) (assigned Michelle Quadcopter) (assigned Vic Necklace) (assigned Heidi Guitar) (assigned Carol Frisbee) (assigned Zoe Whale)))\n)"}
{"id": 7000671638405075718, "group": "action_justification_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 4 agents: zoe, vic, steve, and bob. There are 4 items/roles: book01, book03, book02, and book04. Currently, zoe is assigned book04, vic is assigned book03, bob is assigned book01, and steve is assigned book02. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1 with ?a2, ?r1 for ?r2. The goal is to reach a state where the following facts hold: vic is assigned book04, zoe is assigned book01, steve is assigned book03, and bob is assigned book02.", "question": "Simplify the plan \"(swap vic zoe book03 book04) (swap zoe vic book03 book04) (swap steve bob book02 book01) (swap steve zoe book01 book04) (swap vic steve book03 book04)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(swap vic zoe book03 book04)", "(swap zoe vic book03 book04)", "-1"]], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-4-12)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Steve Vic Zoe - agent book01 book02 book03 book04 - role)\n    (:init (assigned Bob book01) (assigned Steve book02) (assigned Vic book03) (assigned Zoe book04))\n    (:goal (and (assigned Bob book02) (assigned Steve book03) (assigned Vic book04) (assigned Zoe book01)))\n)"}
{"id": -4055971158900335072, "group": "action_justification_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 8 agents: heidi, michelle, dave, carol, xena, alice, zoe, and vic. There are 8 items/roles: guitar, frisbee, quadcopter, iceskates, necklace, slinky, zebra, and whale. Currently, xena is assigned whale, vic is assigned quadcopter, dave is assigned slinky, heidi is assigned necklace, michelle is assigned zebra, alice is assigned iceskates, carol is assigned guitar, and zoe is assigned frisbee. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - trade ?r1 of ?a1 for ?r2 of ?a2. The goal is to reach a state where the following facts hold: heidi is assigned guitar, alice is assigned zebra, zoe is assigned whale, vic is assigned necklace, michelle is assigned quadcopter, dave is assigned iceskates, xena is assigned slinky, and carol is assigned frisbee.", "question": "Simplify the plan \"(swap zoe xena frisbee whale) (swap xena zoe frisbee whale) (swap vic michelle quadcopter zebra) (swap vic alice zebra iceskates) (swap heidi vic necklace iceskates) (swap carol zoe guitar frisbee) (swap xena dave whale slinky) (swap heidi zoe iceskates guitar) (swap dave zoe whale iceskates)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(swap zoe xena frisbee whale)", "(swap xena zoe frisbee whale)", "-1"]], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-8-36)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Carol Dave Heidi Michelle Vic Xena Zoe - agent Frisbee Guitar IceSkates Necklace Quadcopter Slinky Whale Zebra - role)\n    (:init (assigned Alice IceSkates) (assigned Carol Guitar) (assigned Dave Slinky) (assigned Heidi Necklace) (assigned Michelle Zebra) (assigned Vic Quadcopter) (assigned Xena Whale) (assigned Zoe Frisbee))\n    (:goal (and (assigned Xena Slinky) (assigned Dave IceSkates) (assigned Alice Zebra) (assigned Michelle Quadcopter) (assigned Vic Necklace) (assigned Heidi Guitar) (assigned Carol Frisbee) (assigned Zoe Whale)))\n)"}
{"id": -9217865470552515012, "group": "action_justification_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: frank, quentin, xena, liam, vic, and bob. There are 6 items/roles: pliers, sander, knead, wrench, nibbler, and ratchet. Currently, xena is assigned pliers, liam is assigned knead, bob is assigned wrench, vic is assigned nibbler, quentin is assigned ratchet, and frank is assigned sander. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1:?r1 with ?a2:?r2. The goal is to reach a state where the following facts hold: vic is assigned ratchet, xena is assigned wrench, quentin is assigned knead, frank is assigned pliers, bob is assigned nibbler, and liam is assigned sander.", "question": "Simplify the plan \"(swap frank bob sander wrench) (swap frank bob wrench sander) (swap quentin vic ratchet nibbler) (swap liam frank knead sander) (swap xena bob pliers wrench) (swap quentin frank nibbler knead) (swap frank bob nibbler pliers)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(swap frank bob sander wrench)", "(swap frank bob wrench sander)", "-1"]], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Frank Liam Quentin Vic Xena - agent Knead Nibbler Pliers Ratchet Sander Wrench - role)\n    (:init (assigned Bob Wrench) (assigned Frank Sander) (assigned Liam Knead) (assigned Quentin Ratchet) (assigned Vic Nibbler) (assigned Xena Pliers))\n    (:goal (and (assigned Quentin Knead) (assigned Liam Sander) (assigned Frank Pliers) (assigned Xena Wrench) (assigned Bob Nibbler) (assigned Vic Ratchet)))\n)"}
{"id": 4181966821932613054, "group": "action_justification_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 3 agents: grace, xena, and judy. There are 3 items/roles: sander, funnel, and xactoknife. Currently, judy is assigned funnel, xena is assigned sander, and grace is assigned xactoknife. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1 with ?a2, ?r1 for ?r2. The goal is to reach a state where the following facts hold: judy is assigned xactoknife, grace is assigned sander, and xena is assigned funnel.", "question": "Simplify the plan \"(swap xena judy sander funnel) (swap xena grace funnel xactoknife) (swap grace xena funnel xactoknife) (swap grace judy xactoknife sander)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(swap xena grace funnel xactoknife)", "(swap grace xena funnel xactoknife)", "-1"]], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-3-4)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Grace Judy Xena - agent Funnel Sander XActoKnife - role)\n    (:init (assigned Grace XActoKnife) (assigned Judy Funnel) (assigned Xena Sander))\n    (:goal (and (assigned Judy XActoKnife) (assigned Grace Sander) (assigned Xena Funnel)))\n)"}
{"id": 1345319746478110882, "group": "action_justification_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 4 agents: zoe, vic, steve, and bob. There are 4 items/roles: book01, book03, book02, and book04. Currently, zoe is assigned book04, vic is assigned book03, bob is assigned book01, and steve is assigned book02. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - trade ?r1 of ?a1 for ?r2 of ?a2. The goal is to reach a state where the following facts hold: vic is assigned book04, zoe is assigned book01, steve is assigned book03, and bob is assigned book02.", "question": "Simplify the plan \"(swap zoe vic book04 book03) (swap zoe vic book03 book04) (swap vic zoe book03 book04) (swap steve bob book02 book01) (swap zoe steve book03 book01)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(swap zoe vic book04 book03)", "(swap zoe vic book03 book04)", "-1"]], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-4-12)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Steve Vic Zoe - agent book01 book02 book03 book04 - role)\n    (:init (assigned Bob book01) (assigned Steve book02) (assigned Vic book03) (assigned Zoe book04))\n    (:goal (and (assigned Bob book02) (assigned Steve book03) (assigned Vic book04) (assigned Zoe book01)))\n)"}
{"id": -2944033087618524080, "group": "action_justification_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 7 agents: heidi, ted, kevin, dave, xena, alice, and bob. There are 7 items/roles: parsnip, mushroom, yam, valerian, quince, ulluco, and leek. Currently, kevin is assigned mushroom, dave is assigned valerian, bob is assigned leek, alice is assigned yam, heidi is assigned quince, xena is assigned parsnip, and ted is assigned ulluco. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1:?r1 with ?a2:?r2. The goal is to reach a state where the following facts hold: heidi is assigned mushroom, bob is assigned parsnip, kevin is assigned yam, xena is assigned quince, alice is assigned valerian, ted is assigned leek, and dave is assigned ulluco.", "question": "Simplify the plan \"(swap alice ted yam ulluco) (swap alice ted ulluco yam) (swap dave ted valerian ulluco) (swap heidi xena quince parsnip) (swap bob ted leek valerian) (swap kevin alice mushroom yam) (swap heidi bob parsnip valerian) (swap alice heidi mushroom valerian)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(swap alice ted yam ulluco)", "(swap alice ted ulluco yam)", "-1"]], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-7-28)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Bob Dave Heidi Kevin Ted Xena - agent Leek Mushroom Parsnip Quince Ulluco Valerian Yam - role)\n    (:init (assigned Alice Yam) (assigned Bob Leek) (assigned Dave Valerian) (assigned Heidi Quince) (assigned Kevin Mushroom) (assigned Ted Ulluco) (assigned Xena Parsnip))\n    (:goal (and (assigned Xena Quince) (assigned Heidi Mushroom) (assigned Kevin Yam) (assigned Alice Valerian) (assigned Dave Ulluco) (assigned Ted Leek) (assigned Bob Parsnip)))\n)"}
{"id": 2268481102222738870, "group": "action_justification_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 7 agents: heidi, ted, kevin, dave, xena, alice, and bob. There are 7 items/roles: parsnip, mushroom, yam, valerian, quince, ulluco, and leek. Currently, kevin is assigned mushroom, dave is assigned valerian, bob is assigned leek, alice is assigned yam, heidi is assigned quince, xena is assigned parsnip, and ted is assigned ulluco. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1 with ?a2, ?r1 for ?r2. The goal is to reach a state where the following facts hold: heidi is assigned mushroom, bob is assigned parsnip, kevin is assigned yam, xena is assigned quince, alice is assigned valerian, ted is assigned leek, and dave is assigned ulluco.", "question": "Simplify the plan \"(swap alice ted yam ulluco) (swap ted alice yam ulluco) (swap dave ted valerian ulluco) (swap bob ted leek valerian) (swap bob xena valerian parsnip) (swap alice heidi yam quince) (swap kevin heidi mushroom yam) (swap xena alice valerian quince)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(swap alice ted yam ulluco)", "(swap ted alice yam ulluco)", "-1"]], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-7-28)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Bob Dave Heidi Kevin Ted Xena - agent Leek Mushroom Parsnip Quince Ulluco Valerian Yam - role)\n    (:init (assigned Alice Yam) (assigned Bob Leek) (assigned Dave Valerian) (assigned Heidi Quince) (assigned Kevin Mushroom) (assigned Ted Ulluco) (assigned Xena Parsnip))\n    (:goal (and (assigned Xena Quince) (assigned Heidi Mushroom) (assigned Kevin Yam) (assigned Alice Valerian) (assigned Dave Ulluco) (assigned Ted Leek) (assigned Bob Parsnip)))\n)"}
{"id": -5815584403909467012, "group": "action_justification_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 8 agents: heidi, michelle, dave, carol, xena, alice, zoe, and vic. There are 8 items/roles: guitar, frisbee, quadcopter, iceskates, necklace, slinky, zebra, and whale. Currently, xena is assigned whale, vic is assigned quadcopter, dave is assigned slinky, heidi is assigned necklace, michelle is assigned zebra, alice is assigned iceskates, carol is assigned guitar, and zoe is assigned frisbee. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - exchange ?r1 of ?a1 with ?r2 of ?a2. The goal is to reach a state where the following facts hold: heidi is assigned guitar, alice is assigned zebra, zoe is assigned whale, vic is assigned necklace, michelle is assigned quadcopter, dave is assigned iceskates, xena is assigned slinky, and carol is assigned frisbee.", "question": "Simplify the plan \"(swap zoe xena frisbee whale) (swap zoe xena whale frisbee) (swap carol zoe guitar frisbee) (swap michelle alice zebra iceskates) (swap heidi zoe necklace guitar) (swap dave michelle slinky iceskates) (swap michelle zoe slinky necklace) (swap xena zoe whale slinky) (swap michelle vic necklace quadcopter)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(swap zoe xena frisbee whale)", "(swap zoe xena whale frisbee)", "-1"]], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-8-36)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Carol Dave Heidi Michelle Vic Xena Zoe - agent Frisbee Guitar IceSkates Necklace Quadcopter Slinky Whale Zebra - role)\n    (:init (assigned Alice IceSkates) (assigned Carol Guitar) (assigned Dave Slinky) (assigned Heidi Necklace) (assigned Michelle Zebra) (assigned Vic Quadcopter) (assigned Xena Whale) (assigned Zoe Frisbee))\n    (:goal (and (assigned Xena Slinky) (assigned Dave IceSkates) (assigned Alice Zebra) (assigned Michelle Quadcopter) (assigned Vic Necklace) (assigned Heidi Guitar) (assigned Carol Frisbee) (assigned Zoe Whale)))\n)"}
