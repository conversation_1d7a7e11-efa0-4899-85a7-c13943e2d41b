{"id": -6293631633042107419, "group": "action_justification_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) is on the table: block_1. The following block(s) are stacked on top of another block: block_2 is on block_1 and block_3 is on block_2. The available actions are: (pick-up ?ob) - remove ?ob from table, (put-down ?ob) - put down the object ?ob, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob. The goal is to reach a state where the following facts hold: The block block_3 is currently situated above the block block_1.", "question": "Simplify the plan \"(unstack block_3 block_2) (put-down block_3) (unstack block_2 block_1) (stack block_2 block_1) (unstack block_2 block_1) (stack block_2 block_1) (unstack block_2 block_1) (put-down block_2) (pick-up block_1) (stack block_1 block_2) (pick-up block_3) (stack block_3 block_1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(unstack block_2 block_1)", "(stack block_2 block_1)", "-1"], ["(stack block_2 block_1)", "(unstack block_2 block_1)", "-1"], ["(unstack block_2 block_1)", "(stack block_2 block_1)", "-1"], ["(stack block_2 block_1)", "(unstack block_2 block_1)", "-1"], ["(pick-up block_1)", "(stack block_1 block_2)", "-1"]], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-3)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3)\n    (:init (clear block_3) (handempty) (on block_2 block_1) (on block_3 block_2) (ontable block_1))\n    (:goal (on block_3 block_1))\n)"}
{"id": -8983495931595202274, "group": "action_justification_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1, block_4, and block_5. The following block(s) are stacked on top of another block: block_2 is on block_1 and block_3 is on block_5. The available actions are: (pick-up ?ob) - pick up the object ?ob from the table, (put-down ?ob) - put down object ?ob, (stack ?ob ?underob) - stack the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack the object ?ob from the object ?underob. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_4, The block block_3 is on top of block block_5, and The block block_4 is currently situated under the block block_5.", "question": "Simplify the plan \"(unstack block_2 block_1) (stack block_2 block_4) (unstack block_2 block_4) (put-down block_2) (pick-up block_4) (stack block_4 block_1) (unstack block_3 block_5) (put-down block_3) (pick-up block_5) (stack block_5 block_4) (pick-up block_3) (stack block_3 block_5)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(stack block_2 block_4)", "(unstack block_2 block_4)", "-1"]], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_3) (clear block_4) (handempty) (on block_2 block_1) (on block_3 block_5) (ontable block_1) (ontable block_4) (ontable block_5))\n    (:goal (and (on block_3 block_5) (on block_4 block_1) (on block_5 block_4)))\n)"}
{"id": 7553875095541972943, "group": "action_justification_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2 and block_1. The following block(s) are stacked on top of another block: block_6 is on block_7, block_3 is on block_4, block_5 is on block_2, block_10 is on block_8, block_7 is on block_3, block_9 is on block_5, block_8 is on block_6, and block_4 is on block_9. The available actions are: (pick-up ?ob) - collect the object ?ob from the table, (put-down ?ob) - put down the object ?ob, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob. The goal is to reach a state where the following facts hold: The block block_9 is currently situated under the block block_5, The block block_9 is on top of block block_1, The block block_1 is currently situated above the block block_8, The block block_3 is currently situated under the block block_6, The block block_4 is on top of block block_2, The block block_10 is currently situated under the block block_7, The block block_5 is currently situated under the block block_2, and The block block_7 is currently situated under the block block_3.", "question": "Simplify the plan \"(pick-up block_1) (put-down block_1) (unstack block_10 block_8) (put-down block_10) (unstack block_8 block_6) (put-down block_8) (pick-up block_1) (stack block_1 block_8) (unstack block_6 block_7) (put-down block_6) (unstack block_7 block_3) (stack block_7 block_10) (unstack block_3 block_4) (stack block_3 block_7) (pick-up block_6) (stack block_6 block_3) (unstack block_4 block_9) (stack block_4 block_6) (unstack block_9 block_5) (stack block_9 block_1) (unstack block_4 block_6) (put-down block_4) (unstack block_5 block_2) (stack block_5 block_9) (pick-up block_2) (stack block_2 block_5) (pick-up block_4) (stack block_4 block_2)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(pick-up block_1)", "(put-down block_1)", "-1"]], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-10)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_2 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_1) (clear block_10) (handempty) (on block_10 block_8) (on block_3 block_4) (on block_4 block_9) (on block_5 block_2) (on block_6 block_7) (on block_7 block_3) (on block_8 block_6) (on block_9 block_5) (ontable block_1) (ontable block_2))\n    (:goal (and (on block_1 block_8) (on block_2 block_5) (on block_3 block_7) (on block_4 block_2) (on block_5 block_9) (on block_6 block_3) (on block_7 block_10) (on block_9 block_1)))\n)"}
{"id": -1896230255185949006, "group": "action_justification_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2 and block_1. The following block(s) are stacked on top of another block: block_6 is on block_7, block_3 is on block_4, block_5 is on block_2, block_10 is on block_8, block_7 is on block_3, block_9 is on block_5, block_8 is on block_6, and block_4 is on block_9. The available actions are: (pick-up ?ob) - collect the object ?ob from the table, (put-down ?ob) - put down the object ?ob, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack the object ?ob from the object ?underob. The goal is to reach a state where the following facts hold: The block block_9 is currently situated under the block block_5, The block block_9 is currently situated above the block block_1, The block block_8 is currently situated under the block block_1, The block block_3 is currently situated under the block block_6, The block block_4 is currently situated above the block block_2, The block block_7 is on top of block block_10, The block block_5 is currently situated under the block block_2, and The block block_3 is currently situated above the block block_7.", "question": "Simplify the plan \"(unstack block_10 block_8) (put-down block_10) (unstack block_8 block_6) (put-down block_8) (unstack block_6 block_7) (stack block_6 block_7) (unstack block_6 block_7) (put-down block_6) (pick-up block_1) (stack block_1 block_8) (unstack block_7 block_3) (stack block_7 block_10) (unstack block_3 block_4) (stack block_3 block_7) (unstack block_4 block_9) (stack block_4 block_3) (unstack block_9 block_5) (stack block_9 block_1) (unstack block_5 block_2) (stack block_5 block_9) (pick-up block_2) (stack block_2 block_5) (unstack block_4 block_3) (stack block_4 block_2) (pick-up block_6) (stack block_6 block_3)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(unstack block_6 block_7)", "(stack block_6 block_7)", "-1"], ["(stack block_6 block_7)", "(unstack block_6 block_7)", "-1"]], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-10)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_2 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_1) (clear block_10) (handempty) (on block_10 block_8) (on block_3 block_4) (on block_4 block_9) (on block_5 block_2) (on block_6 block_7) (on block_7 block_3) (on block_8 block_6) (on block_9 block_5) (ontable block_1) (ontable block_2))\n    (:goal (and (on block_1 block_8) (on block_2 block_5) (on block_3 block_7) (on block_4 block_2) (on block_5 block_9) (on block_6 block_3) (on block_7 block_10) (on block_9 block_1)))\n)"}
{"id": -8606297672752023784, "group": "action_justification_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2 and block_1. The following block(s) are stacked on top of another block: block_6 is on block_7, block_3 is on block_4, block_5 is on block_2, block_10 is on block_8, block_7 is on block_3, block_9 is on block_5, block_8 is on block_6, and block_4 is on block_9. The available actions are: (pick-up ?ob) - remove ?ob from table, (put-down ?ob) - put down object ?ob, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - unstack the object ?ob from the object ?underob. The goal is to reach a state where the following facts hold: The block block_5 is currently situated above the block block_9, The block block_1 is currently situated under the block block_9, The block block_1 is on top of block block_8, The block block_3 is currently situated under the block block_6, The block block_2 is currently situated under the block block_4, The block block_7 is currently situated above the block block_10, The block block_2 is on top of block block_5, and The block block_7 is currently situated under the block block_3.", "question": "Simplify the plan \"(unstack block_10 block_8) (put-down block_10) (unstack block_8 block_6) (put-down block_8) (pick-up block_1) (stack block_1 block_8) (unstack block_6 block_7) (put-down block_6) (unstack block_7 block_3) (stack block_7 block_10) (unstack block_3 block_4) (stack block_3 block_7) (unstack block_4 block_9) (stack block_4 block_6) (unstack block_9 block_5) (stack block_9 block_1) (unstack block_5 block_2) (stack block_5 block_9) (pick-up block_2) (stack block_2 block_5) (unstack block_4 block_6) (stack block_4 block_2) (unstack block_4 block_2) (stack block_4 block_2) (pick-up block_6) (stack block_6 block_3)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(stack block_4 block_2)", "(unstack block_4 block_2)", "-1"], ["(unstack block_4 block_2)", "(stack block_4 block_2)", "-1"]], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-10)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_2 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_1) (clear block_10) (handempty) (on block_10 block_8) (on block_3 block_4) (on block_4 block_9) (on block_5 block_2) (on block_6 block_7) (on block_7 block_3) (on block_8 block_6) (on block_9 block_5) (ontable block_1) (ontable block_2))\n    (:goal (and (on block_1 block_8) (on block_2 block_5) (on block_3 block_7) (on block_4 block_2) (on block_5 block_9) (on block_6 block_3) (on block_7 block_10) (on block_9 block_1)))\n)"}
{"id": 5933069861346884915, "group": "action_justification_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1, block_4, and block_5. The following block(s) are stacked on top of another block: block_2 is on block_1 and block_3 is on block_5. The available actions are: (pick-up ?ob) - remove ?ob from table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob. The goal is to reach a state where the following facts hold: The block block_4 is on top of block block_1, The block block_5 is currently situated under the block block_3, and The block block_5 is on top of block block_4.", "question": "Simplify the plan \"(unstack block_2 block_1) (put-down block_2) (unstack block_3 block_5) (stack block_3 block_2) (pick-up block_5) (stack block_5 block_1) (unstack block_5 block_1) (put-down block_5) (pick-up block_4) (stack block_4 block_1) (pick-up block_5) (stack block_5 block_4) (unstack block_3 block_2) (stack block_3 block_5)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(stack block_5 block_1)", "(unstack block_5 block_1)", "-1"]], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_3) (clear block_4) (handempty) (on block_2 block_1) (on block_3 block_5) (ontable block_1) (ontable block_4) (ontable block_5))\n    (:goal (and (on block_3 block_5) (on block_4 block_1) (on block_5 block_4)))\n)"}
{"id": 2440810982065012781, "group": "action_justification_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_14, block_19, block_1, block_18, block_7, and block_6. The following block(s) are stacked on top of another block: block_12 is on block_17, block_17 is on block_3, block_11 is on block_1, block_8 is on block_7, block_5 is on block_14, block_10 is on block_19, block_3 is on block_13, block_13 is on block_4, block_16 is on block_20, block_20 is on block_8, block_2 is on block_18, block_15 is on block_16, block_4 is on block_11, and block_9 is on block_15. The available actions are: (pick-up ?ob) - pick up object ?ob from the table, (put-down ?ob) - put down object ?ob, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - remove the object ?ob from on top of the object ?underob. The goal is to reach a state where the following facts hold: The block block_14 is currently situated under the block block_2, The block block_6 is currently situated under the block block_4, The block block_7 is currently situated under the block block_12, The block block_8 is currently situated above the block block_4, The block block_14 is currently situated above the block block_20, The block block_15 is on top of block block_9, The block block_17 is on top of block block_12, The block block_8 is currently situated under the block block_11, The block block_18 is on top of block block_5, The block block_1 is currently situated under the block block_5, The block block_1 is on top of block block_17, The block block_7 is on top of block block_10, The block block_11 is currently situated under the block block_16, The block block_19 is currently situated above the block block_16, The block block_2 is currently situated under the block block_9, and The block block_3 is on top of block block_13.", "question": "Simplify the plan \"(unstack block_9 block_15) (stack block_9 block_2) (unstack block_15 block_16) (put-down block_15) (unstack block_12 block_17) (put-down block_12) (unstack block_16 block_20) (put-down block_16) (unstack block_17 block_3) (stack block_17 block_9) (unstack block_17 block_9) (put-down block_17) (unstack block_5 block_14) (put-down block_5) (unstack block_10 block_19) (put-down block_10) (unstack block_20 block_8) (put-down block_20) (pick-up block_15) (stack block_15 block_9) (pick-up block_14) (stack block_14 block_20) (pick-up block_17) (stack block_17 block_12) (pick-up block_19) (stack block_19 block_16) (unstack block_8 block_7) (put-down block_8) (pick-up block_7) (stack block_7 block_10) (unstack block_17 block_12) (put-down block_17) (pick-up block_12) (stack block_12 block_7) (pick-up block_17) (stack block_17 block_12) (unstack block_3 block_13) (put-down block_3) (unstack block_13 block_4) (put-down block_13) (pick-up block_3) (stack block_3 block_13) (unstack block_4 block_11) (stack block_4 block_6) (unstack block_11 block_1) (stack block_11 block_8) (pick-up block_1) (stack block_1 block_17) (pick-up block_5) (stack block_5 block_1) (unstack block_11 block_8) (put-down block_11) (pick-up block_8) (stack block_8 block_4) (pick-up block_11) (stack block_11 block_8) (unstack block_19 block_16) (put-down block_19) (pick-up block_16) (stack block_16 block_11) (pick-up block_19) (stack block_19 block_16) (unstack block_15 block_9) (put-down block_15) (unstack block_9 block_2) (put-down block_9) (pick-up block_15) (stack block_15 block_9) (unstack block_2 block_18) (stack block_2 block_14) (pick-up block_18) (stack block_18 block_5) (unstack block_15 block_9) (put-down block_15) (pick-up block_9) (stack block_9 block_2) (pick-up block_15) (stack block_15 block_9)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(stack block_17 block_9)", "(unstack block_17 block_9)", "-1"]], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-20)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_11 block_12 block_13 block_14 block_15 block_16 block_17 block_18 block_19 block_2 block_20 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_10) (clear block_12) (clear block_2) (clear block_5) (clear block_6) (clear block_9) (handempty) (on block_10 block_19) (on block_11 block_1) (on block_12 block_17) (on block_13 block_4) (on block_15 block_16) (on block_16 block_20) (on block_17 block_3) (on block_2 block_18) (on block_20 block_8) (on block_3 block_13) (on block_4 block_11) (on block_5 block_14) (on block_8 block_7) (on block_9 block_15) (ontable block_1) (ontable block_14) (ontable block_18) (ontable block_19) (ontable block_6) (ontable block_7))\n    (:goal (and (on block_1 block_17) (on block_2 block_14) (on block_3 block_13) (on block_4 block_6) (on block_5 block_1) (on block_7 block_10) (on block_8 block_4) (on block_9 block_2) (on block_11 block_8) (on block_12 block_7) (on block_14 block_20) (on block_15 block_9) (on block_16 block_11) (on block_17 block_12) (on block_18 block_5) (on block_19 block_16)))\n)"}
{"id": 5084615096252134813, "group": "action_justification_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1, block_4, and block_5. The following block(s) are stacked on top of another block: block_2 is on block_1 and block_3 is on block_5. The available actions are: (pick-up ?ob) - pick up the object ?ob from the table, (put-down ?ob) - put down the object ?ob, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob. The goal is to reach a state where the following facts hold: The block block_4 is currently situated above the block block_1, The block block_3 is currently situated above the block block_5, and The block block_5 is on top of block block_4.", "question": "Simplify the plan \"(unstack block_2 block_1) (put-down block_2) (unstack block_3 block_5) (stack block_3 block_4) (pick-up block_5) (put-down block_5) (unstack block_3 block_4) (put-down block_3) (pick-up block_4) (stack block_4 block_1) (pick-up block_5) (stack block_5 block_4) (pick-up block_3) (stack block_3 block_5)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(pick-up block_5)", "(put-down block_5)", "-1"]], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_3) (clear block_4) (handempty) (on block_2 block_1) (on block_3 block_5) (ontable block_1) (ontable block_4) (ontable block_5))\n    (:goal (and (on block_3 block_5) (on block_4 block_1) (on block_5 block_4)))\n)"}
{"id": 1865270629806068826, "group": "action_justification_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1, block_4, and block_5. The following block(s) are stacked on top of another block: block_2 is on block_1 and block_3 is on block_5. The available actions are: (pick-up ?ob) - pick up the object ?ob from the table, (put-down ?ob) - put down the object ?ob, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - remove the object ?ob from on top of the object ?underob. The goal is to reach a state where the following facts hold: The block block_4 is on top of block block_1, The block block_3 is on top of block block_5, and The block block_5 is currently situated above the block block_4.", "question": "Simplify the plan \"(unstack block_2 block_1) (put-down block_2) (pick-up block_4) (stack block_4 block_1) (unstack block_4 block_1) (stack block_4 block_1) (unstack block_3 block_5) (stack block_3 block_2) (pick-up block_5) (stack block_5 block_4) (unstack block_3 block_2) (stack block_3 block_5) (pick-up block_2)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(pick-up block_2)", "1"], ["(stack block_4 block_1)", "(unstack block_4 block_1)", "-1"], ["(unstack block_4 block_1)", "(stack block_4 block_1)", "-1"]], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_3) (clear block_4) (handempty) (on block_2 block_1) (on block_3 block_5) (ontable block_1) (ontable block_4) (ontable block_5))\n    (:goal (and (on block_3 block_5) (on block_4 block_1) (on block_5 block_4)))\n)"}
{"id": 1602653800893810426, "group": "action_justification_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) is on the table: block_1. The following block(s) are stacked on top of another block: block_2 is on block_1 and block_3 is on block_2. The available actions are: (pick-up ?ob) - pick up object ?ob from the table, (put-down ?ob) - put down the object ?ob, (stack ?ob ?underob) - stack the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob. The goal is to reach a state where the following facts hold: The block block_3 is currently situated above the block block_1.", "question": "Simplify the plan \"(unstack block_3 block_2) (put-down block_3) (unstack block_2 block_1) (stack block_2 block_1) (unstack block_2 block_1) (put-down block_2) (pick-up block_3) (stack block_3 block_1) (unstack block_3 block_1) (stack block_3 block_1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(unstack block_2 block_1)", "(stack block_2 block_1)", "-1"], ["(stack block_2 block_1)", "(unstack block_2 block_1)", "-1"], ["(stack block_3 block_1)", "(unstack block_3 block_1)", "-1"], ["(unstack block_3 block_1)", "(stack block_3 block_1)", "-1"]], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem BW-rand-3)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3)\n    (:init (clear block_3) (handempty) (on block_2 block_1) (on block_3 block_2) (ontable block_1))\n    (:goal (on block_3 block_1))\n)"}
{"id": -7520242702388077372, "group": "action_justification_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 3 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_2, block_3, and block_1. The available actions are: (pick-up ?ob) - pick up object ?ob from the table, (put-down ?ob) - put down the object ?ob, (stack ?ob ?underob) - stack the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob. The goal is to reach a state where the following facts hold: The block block_2 is on top of block block_1.", "question": "Simplify the plan \"(pick-up block_2) (stack block_2 block_1) (unstack block_2 block_1) (stack block_2 block_3) (unstack block_2 block_3) (stack block_2 block_1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(stack block_2 block_1)", "(unstack block_2 block_1)", "-1"], ["(stack block_2 block_3)", "(unstack block_2 block_3)", "-1"]], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-3)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3)\n    (:init (clear block_1) (clear block_2) (clear block_3) (handempty) (ontable block_1) (ontable block_2) (ontable block_3))\n    (:goal (on block_2 block_1))\n)"}
{"id": 425282556553314271, "group": "action_justification_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 3 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_3 and block_1. The following block(s) is stacked on top of another block: block_2 is on block_1. The available actions are: (pick-up ?ob) - remove ?ob from table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - remove the object ?ob from on top of the object ?underob. The goal is to reach a state where the following facts hold: The block block_2 is currently situated above the block block_1 and The block block_1 is on top of block block_3.", "question": "Simplify the plan \"(unstack block_2 block_1) (stack block_2 block_3) (unstack block_2 block_3) (put-down block_2) (pick-up block_2) (stack block_2 block_1) (unstack block_2 block_1) (put-down block_2) (pick-up block_1) (stack block_1 block_3) (pick-up block_2) (stack block_2 block_1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(stack block_2 block_3)", "(unstack block_2 block_3)", "-1"], ["(put-down block_2)", "(pick-up block_2)", "-1"], ["(stack block_2 block_1)", "(unstack block_2 block_1)", "-1"]], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-3)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3)\n    (:init (clear block_2) (clear block_3) (handempty) (on block_2 block_1) (ontable block_1) (ontable block_3))\n    (:goal (and (on block_1 block_3) (on block_2 block_1)))\n)"}
{"id": -5259610649173628579, "group": "action_justification_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_2 and block_1. The following block(s) are stacked on top of another block: block_4 is on block_1, block_5 is on block_2, and block_3 is on block_5. The available actions are: (pick-up ?ob) - pick up object ?ob from the table, (put-down ?ob) - put down object ?ob, (stack ?ob ?underob) - stack the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob. The goal is to reach a state where the following facts hold: The block block_2 is currently situated above the block block_4, The block block_5 is currently situated under the block block_4, and The block block_1 is on top of block block_3.", "question": "Simplify the plan \"(unstack block_3 block_5) (put-down block_3) (unstack block_5 block_2) (put-down block_5) (unstack block_4 block_1) (stack block_4 block_5) (pick-up block_2) (stack block_2 block_4) (pick-up block_1) (stack block_1 block_3) (unstack block_2 block_4) (stack block_2 block_1) (unstack block_2 block_1) (stack block_2 block_4)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(stack block_2 block_1)", "(unstack block_2 block_1)", "-1"]], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_3) (clear block_4) (handempty) (on block_3 block_5) (on block_4 block_1) (on block_5 block_2) (ontable block_1) (ontable block_2))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": -3900694270925191760, "group": "action_justification_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 3 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_3 and block_1. The following block(s) is stacked on top of another block: block_2 is on block_3. The available actions are: (pick-up ?ob) - pick up the object ?ob from the table, (put-down ?ob) - put down the object ?ob, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - remove the object ?ob from on top of the object ?underob. The goal is to reach a state where the following facts hold: The block block_2 is currently situated under the block block_1.", "question": "Simplify the plan \"(pick-up block_1) (stack block_1 block_2) (unstack block_1 block_2) (put-down block_1) (unstack block_2 block_3) (stack block_2 block_3) (unstack block_2 block_3) (stack block_2 block_3) (unstack block_2 block_3) (stack block_2 block_3) (unstack block_2 block_3) (stack block_2 block_3) (unstack block_2 block_3) (stack block_2 block_3) (unstack block_2 block_3) (stack block_2 block_3) (pick-up block_1) (stack block_1 block_2)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(stack block_1 block_2)", "(unstack block_1 block_2)", "-1"], ["(unstack block_2 block_3)", "(stack block_2 block_3)", "-1"], ["(stack block_2 block_3)", "(unstack block_2 block_3)", "-1"], ["(unstack block_2 block_3)", "(stack block_2 block_3)", "-1"], ["(stack block_2 block_3)", "(unstack block_2 block_3)", "-1"], ["(unstack block_2 block_3)", "(stack block_2 block_3)", "-1"], ["(stack block_2 block_3)", "(unstack block_2 block_3)", "-1"], ["(unstack block_2 block_3)", "(stack block_2 block_3)", "-1"], ["(stack block_2 block_3)", "(unstack block_2 block_3)", "-1"], ["(unstack block_2 block_3)", "(stack block_2 block_3)", "-1"], ["(stack block_2 block_3)", "(unstack block_2 block_3)", "-1"], ["(unstack block_2 block_3)", "(stack block_2 block_3)", "-1"]], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-3)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3)\n    (:init (clear block_1) (clear block_2) (handempty) (on block_2 block_3) (ontable block_1) (ontable block_3))\n    (:goal (on block_1 block_2))\n)"}
{"id": -1935657372967311035, "group": "action_justification_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 3 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_2, block_3, and block_1. The available actions are: (pick-up ?ob) - pick up object ?ob from the table, (put-down ?ob) - put down the object ?ob, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob. The goal is to reach a state where the following facts hold: The block block_2 is on top of block block_1.", "question": "Simplify the plan \"(pick-up block_1) (put-down block_1) (pick-up block_2) (stack block_2 block_1) (pick-up block_3)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(pick-up block_3)", "1"], ["(pick-up block_1)", "(put-down block_1)", "-1"]], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-3)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3)\n    (:init (clear block_1) (clear block_2) (clear block_3) (handempty) (ontable block_1) (ontable block_2) (ontable block_3))\n    (:goal (on block_2 block_1))\n)"}
