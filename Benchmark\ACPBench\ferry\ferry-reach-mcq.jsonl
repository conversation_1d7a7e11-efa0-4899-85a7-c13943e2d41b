{"id": 3605767053429137153, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c45 on board. The cars are at locations as follows: c14, c9, c38, c0, c41, c29, c28, c30, c46, c47, c8, and c27 are at l4; c18, c17, c1, c10, c36, c24, c44, and c16 are at l0; c31, c13, c43, c20, c7, c4, c42, c5, c12, c37, c25, c6, c49, and c23 are at l3; c19, c33, c15, c34, c48, c32, c2, and c11 are at l1; c21, c26, c40, c35, c39, c22, and c3 are at l2.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Car c29 is on board the ferry and The ferry is empty. B. Car c30 is at location c36. C. The ferry is at l3 location. D. The ferry is at c30 location and Car c2 is at location l1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car c29 is on board the ferry and The ferry is empty", "Car c30 is at location c36", "The ferry is at l3 location", "The ferry is at c30 location and Car c2 is at location l1"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -3764045347364188946, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l4 location and it is empty. The cars are at locations as follows: c1 and c2 are at l3; c0 is at l4.", "question": "Which of the following options can hold in a state that can potentially be reached? A. There are no cars on the ferry and Car c0 is at location c2. B. Car c1 is at location c1. C. Car c2 is at location l1 and Car c2 is at location l2. D. The ferry is at l3 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["There are no cars on the ferry and Car c0 is at location c2", "Car c1 is at location c1", "Car c2 is at location l1 and Car c2 is at location l2", "The ferry is at l3 location"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -2051081572271352155, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c25 on board. The cars are at locations as follows: c18, c22, c2, c23, c17, c11, c20, c29, c35, c21, c0, c8, c19, c14, c13, c3, c6, c33, c44, c9, and c30 are at l0; c4, c28, c41, c12, c15, c40, c45, c42, c24, c39, c49, c7, c26, c16, c47, c27, c34, c5, c48, c10, c38, c43, c36, c46, c32, c31, c37, and c1 are at l1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The ferry is at c31 location. B. Car c25 is at location l1 and The ferry is at l1 location. C. Car c31 is at location l0 and Car c31 is at location l1. D. The ferry is at c5 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The ferry is at c31 location", "Car c25 is at location l1 and The ferry is at l1 location", "Car c31 is at location l0 and Car c31 is at location l1", "The ferry is at c5 location"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -3336074200616058544, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l3 location and it is empty. The cars are at locations as follows: c14, c9, c38, c0, c41, c29, c28, c30, c46, c47, c8, and c27 are at l4; c18, c17, c1, c10, c36, c45, c4, c42, c24, c44, c48, and c16 are at l0; c31, c13, c20, c34, c7, c5, c12, c37, c25, c6, c3, and c23 are at l3; c19, c33, c15, c32, c2, c49, and c11 are at l1; c21, c26, c40, c35, c39, c43, and c22 are at l2.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Car c20 is at location l3 and Car l3 is on the ferry. B. Car c46 is at location l1 and Car c46 is on board the ferry. C. Car c39 is on the ferry and The ferry is at l2 location. D. Ferry has car l1 on board and Car c45 is at location l0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car c20 is at location l3 and Car l3 is on the ferry", "Car c46 is at location l1 and Car c46 is on board the ferry", "Car c39 is on the ferry and The ferry is at l2 location", "Ferry has car l1 on board and Car c45 is at location l0"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -4339646594736032462, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c14, c35, c36, c38, c45, c0, c26, c29, c28, c30, c47, and c8 are at l4; c18, c40, c17, c41, c1, c10, c6, c24, c27, and c9 are at l0; c31, c13, c43, c20, c34, c7, c4, c42, c5, c12, c25, c37, c49, and c23 are at l3; c19, c33, c44, c15, c48, c46, c32, c2, and c11 are at l1; c21, c16, c39, c22, and c3 are at l2.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Car l3 is on the ferry. B. Car c26 is on board the ferry and The ferry is at l4 location. C. Car c24 is at location l0 and The ferry is at c42 location. D. The ferry is at l1 location and The ferry is at l4 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car l3 is on the ferry", "Car c26 is on board the ferry and The ferry is at l4 location", "Car c24 is at location l0 and The ferry is at c42 location", "The ferry is at l1 location and The ferry is at l4 location"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 7834509669496799127, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l1, with the car c2 on board. The cars are at locations as follows: c1 is at l3; c0 is at l4.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Ferry has car l4 on board. B. The ferry is at l1 location and The ferry is at l0 location. C. The ferry is empty and The ferry is at l4 location. D. The ferry is at c2 location and Car c0 is at location l4.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ferry has car l4 on board", "The ferry is at l1 location and The ferry is at l0 location", "The ferry is empty and The ferry is at l4 location", "The ferry is at c2 location and Car c0 is at location l4"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -9026460909770995699, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l4, with the car c26 on board. The cars are at locations as follows: c14, c2, c35, c36, c38, c45, c0, c10, c20, c28, c47, and c27 are at l4; c18, c40, c11, c17, c41, c25, c1, c6, c9, and c16 are at l0; c31, c13, c39, c43, c29, c7, c4, c42, c5, c37, c3, c49, c23, and c15 are at l3; c19, c33, c44, c21, c24, c34, c48, c46, and c32 are at l1; c8, c30, c22, and c12 are at l2.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Car l0 is on board the ferry. B. Car l4 is on board the ferry. C. Car c45 is at location l4 and Car c45 is on the ferry. D. The ferry is at l1 location and Car c26 is at location l1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car l0 is on board the ferry", "Car l4 is on board the ferry", "Car c45 is at location l4 and Car c45 is on the ferry", "The ferry is at l1 location and Car c26 is at location l1"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -698570149508129447, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l1, with the car c5 on board. The cars are at locations as follows: c0, c2, and c1 are at l1; c9, c4, and c7 are at l0; c8, c3, and c6 are at l2.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The ferry is at c9 location and Car c6 is at location l2. B. The ferry is at l0 location and The ferry is at l2 location. C. Car l2 is on the ferry and Car c4 is at location l0. D. There are no cars on the ferry and Car c5 is at location l1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The ferry is at c9 location and Car c6 is at location l2", "The ferry is at l0 location and The ferry is at l2 location", "Car l2 is on the ferry and Car c4 is at location l0", "There are no cars on the ferry and Car c5 is at location l1"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 6393147995374560141, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1, with the car c38 on board. The cars are at locations as follows: c22, c2, c29, c11, c20, c40, c21, c0, c25, c8, c19, c12, c36, c14, c13, c6, c26, c44, c9, c30, and c37 are at l0; c4, c28, c33, c41, c15, c35, c18, c45, c42, c24, c39, c7, c3, c31, c23, c16, c47, c27, c34, c5, c48, c10, c43, c46, c32, c17, c49, and c1 are at l1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Car c41 is at location l1 and The ferry is at c38 location. B. Car c21 is on board the ferry and Car c42 is on the ferry. C. Ferry has car c5 on board and Car c38 is at location l1. D. Car c10 is at location l1 and Car l1 is on the ferry.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car c41 is at location l1 and The ferry is at c38 location", "Car c21 is on board the ferry and Car c42 is on the ferry", "Ferry has car c5 on board and Car c38 is at location l1", "Car c10 is at location l1 and Car l1 is on the ferry"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -5468068658442193148, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c0 is at l0; c1 and c2 are at l3.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Car c0 is at location l0 and Car c1 is at location c1. B. Ferry has car c1 on board and The ferry is at l3 location. C. Car c1 is at location l3 and Car c1 is on the ferry. D. The ferry is at c0 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car c0 is at location l0 and Car c1 is at location c1", "Ferry has car c1 on board and The ferry is at l3 location", "Car c1 is at location l3 and Car c1 is on the ferry", "The ferry is at c0 location"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 7931544803254567708, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 10 cars, numbered consecutively. Currently, the ferry is at l0, with the car c3 on board. The cars are at locations as follows: c0, c1, c2, c6, c8, and c9 are at l0; c4, c7, and c5 are at l1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Ferry has car l1 on board. B. Car c8 is at location l0 and Car c8 is on board the ferry. C. The ferry is at c5 location and Car c5 is at location l1. D. The ferry is at l1 location and Car c3 is at location l1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ferry has car l1 on board", "Car c8 is at location l0 and Car c8 is on board the ferry", "The ferry is at c5 location and Car c5 is at location l1", "The ferry is at l1 location and Car c3 is at location l1"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -6721318970102316394, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 10 cars, numbered consecutively. Currently, the ferry is at l1, with the car c7 on board. The cars are at locations as follows: c0, c9, c1, c8, c6, and c4 are at l0; c3, c2, and c5 are at l1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The ferry is at l0 location and The ferry is at l1 location. B. The ferry is at c3 location. C. The ferry is at c0 location. D. Ferry has car c2 on board and Car c7 is at location l1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The ferry is at l0 location and The ferry is at l1 location", "The ferry is at c3 location", "The ferry is at c0 location", "Ferry has car c2 on board and Car c7 is at location l1"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 4495478564224564821, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 20 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c4, c19, c12, c18, c7, c6, c16, c5, c14, c2, c17, and c11 are at l1; c0, c1, c8, c10, c13, c3, c9, and c15 are at l0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The ferry is at c5 location and Car c5 is at location l1. B. The ferry is at c2 location and Car c19 is at location l1. C. The ferry is at l1 location and The ferry is at l0 location. D. Car c13 is on board the ferry and The ferry is at l0 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The ferry is at c5 location and Car c5 is at location l1", "The ferry is at c2 location and Car c19 is at location l1", "The ferry is at l1 location and The ferry is at l0 location", "Car c13 is on board the ferry and The ferry is at l0 location"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -3265157701752701506, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 10 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c3, c0, c1, c8, c6, c4, and c7 are at l0; c9, c2, and c5 are at l1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Car c9 is on the ferry and The ferry is at l1 location. B. Car c6 is at location l1 and Car c6 is at location l0. C. Car c7 is at location c3 and Car c6 is at location l0. D. Car l0 is on board the ferry and Car c9 is at location l1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car c9 is on the ferry and The ferry is at l1 location", "Car c6 is at location l1 and Car c6 is at location l0", "Car c7 is at location c3 and Car c6 is at location l0", "Car l0 is on board the ferry and Car c9 is at location l1"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 1925404182330370283, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 10 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c0, c9, c1, c8, c6, and c4 are at l0; c7, c3, c2, and c5 are at l1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Ferry has car c8 on board and The ferry is at l1 location. B. Car c7 is at location c5. C. Car c9 is at location l1 and Car c9 is at location l0. D. Car c6 is at location c8 and Car c8 is at location l0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ferry has car c8 on board and The ferry is at l1 location", "Car c7 is at location c5", "Car c9 is at location l1 and Car c9 is at location l0", "Car c6 is at location c8 and Car c8 is at location l0"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
