{"id": 2642877709553038988, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_4. The following block(s) are on the table: block_3 and block_5. The following block(s) are stacked on top of another block: block_1 is on block_3 and block_2 is on block_5.", "question": "Is it possible to transition to a state where the action \"stack object block_1 on top of object block_4\" can be applied?", "answer": "yes"}
{"id": -8843862381011450481, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_3. The following block(s) are on the table: block_2, block_1, and block_4. The following block(s) is stacked on top of another block: block_5 is on block_1.", "question": "Is it possible to transition to a state where the action \"unstack object block_1 from object block_1\" can be applied?", "answer": "no"}
{"id": 8671269818463951114, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_4. The following block(s) are on the table: block_2 and block_1. The following block(s) are stacked on top of another block: block_5 is on block_3 and block_3 is on block_1.", "question": "Is it possible to transition to a state where the action \"place the object block_2 on top of the object block_4\" can be applied?", "answer": "yes"}
{"id": 6147226057332730055, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2, block_1, block_4, and block_5. The following block(s) is stacked on top of another block: block_3 is on block_1.", "question": "Is it possible to transition to a state where the action \"place the object block_4 on top of the object block_3\" can be applied?", "answer": "yes"}
{"id": -1502113976886974126, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is holding block_14. The following block(s) are on the table: block_6, block_10, block_1, block_20, block_16, block_5, block_18, and block_7. The following block(s) are stacked on top of another block: block_8 is on block_7, block_12 is on block_6, block_15 is on block_9, block_3 is on block_13, block_4 is on block_11, block_13 is on block_4, block_9 is on block_2, block_19 is on block_16, block_17 is on block_12, block_2 is on block_18, and block_11 is on block_1.", "question": "Is it possible to transition to a state where the action \"pick up object block_10 from the table\" can be applied?", "answer": "yes"}
{"id": 3771720756564434423, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_6, block_10, block_19, block_1, block_9, block_18, block_14, and block_7. The following block(s) are stacked on top of another block: block_20 is on block_8, block_8 is on block_7, block_15 is on block_9, block_3 is on block_13, block_12 is on block_2, block_4 is on block_11, block_13 is on block_4, block_16 is on block_20, block_5 is on block_14, block_17 is on block_12, block_2 is on block_18, and block_11 is on block_1.", "question": "Is it possible to transition to a state where the action \"stack object block_5 on top of object block_17\" can be applied?", "answer": "yes"}
{"id": 1190867249158433837, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is holding block_3. The following block(s) are on the table: block_2 and block_1.", "question": "Is it possible to transition to a state where the action \"stack object block_1 on top of object block_3\" can be applied?", "answer": "yes"}
{"id": -4026253485069427142, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_12, block_6, block_10, block_19, block_1, block_18, block_14, and block_7. The following block(s) are stacked on top of another block: block_3 is on block_2, block_20 is on block_8, block_8 is on block_7, block_15 is on block_16, block_4 is on block_11, block_13 is on block_4, block_16 is on block_20, block_5 is on block_14, block_9 is on block_15, block_17 is on block_12, block_2 is on block_18, and block_11 is on block_1.", "question": "Is it possible to transition to a state where the action \"stack the object block_3 on top of the object block_3\" can be applied?", "answer": "no"}
{"id": -4609158751711069255, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_12, block_6, block_19, block_1, block_18, block_14, and block_7. The following block(s) are stacked on top of another block: block_20 is on block_8, block_8 is on block_7, block_10 is on block_19, block_15 is on block_16, block_4 is on block_11, block_3 is on block_6, block_13 is on block_4, block_16 is on block_20, block_5 is on block_14, block_9 is on block_15, block_17 is on block_12, block_2 is on block_18, and block_11 is on block_1.", "question": "Is it possible to transition to a state where the action \"place the object block_2 on top of the object block_4\" can be applied?", "answer": "yes"}
{"id": 4710328513831901010, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_4. The following block(s) are on the table: block_1 and block_5. The following block(s) are stacked on top of another block: block_3 is on block_5 and block_2 is on block_1.", "question": "Is it possible to transition to a state where the action \"acquire the key block_4 from the place block_3\" can be applied?", "answer": "no"}
{"id": 88880427143898719, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_5. The following block(s) are on the table: block_1 and block_3. The following block(s) are stacked on top of another block: block_2 is on block_4 and block_4 is on block_1.", "question": "Is it possible to transition to a state where the action \"stack the object block_5 on top of the object block_5\" can be applied?", "answer": "no"}
{"id": -732791368214372788, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_3. The following block(s) are on the table: block_1 and block_5. The following block(s) are stacked on top of another block: block_2 is on block_4 and block_4 is on block_5.", "question": "Is it possible to transition to a state where the action \"stack object block_1 on top of object block_3\" can be applied?", "answer": "yes"}
{"id": -946383691690142322, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2 and block_3. The following block(s) are stacked on top of another block: block_5 is on block_2, block_4 is on block_5, and block_1 is on block_4.", "question": "Is it possible to transition to a state where the action \"stack the object block_3 on top of the object block_1\" can be applied?", "answer": "yes"}
{"id": -100048758651598240, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_3. The following block(s) is on the table: block_5. The following block(s) are stacked on top of another block: block_2 is on block_4, block_4 is on block_5, and block_1 is on block_2.", "question": "Is it possible to transition to a state where the action \"put the key block_2 at the current position place block_4\" can be applied?", "answer": "no"}
{"id": 8239769062387847177, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_5. The following block(s) are on the table: block_2 and block_3. The following block(s) are stacked on top of another block: block_4 is on block_3 and block_1 is on block_2.", "question": "Is it possible to transition to a state where the action \"unstack the object block_1 from the object block_2\" can be applied?", "answer": "yes"}
