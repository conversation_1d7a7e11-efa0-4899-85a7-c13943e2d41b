{"id": -5220604179093862793, "group": "landmarks_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 16 object types: 1 candle, 2 cloths, 3 faucets, 2 handtowels, 1 lightswitch, 1 mirror, 1 plunger, 1 scrubbrush, 1 showerdoor, 1 showerglass, 2 sinks, 2 soapbars, 2 soapbottles, 3 spraybottles, 2 toiletpapers, 1 towel, 8 receptacle types: 4 cabinets, 1 countertop, 1 garbagecan, 2 handtowelholders, 2 sinkbasins, 1 toiletpaperhanger, 1 toilet, 1 towelholder, and 19 locations all numbered consecutively.  The receptacles are at locations as follows. handtowelholder2 is at location17. sinkbasin2 is at location6. toilet1 is at location7. cabinet1 is at location4. countertop1 is at location3. handtowelholder1 is at location18. cabinet2 is at location11. cabinet4 is at location15. towelholder1 is at location5. toiletpaperhanger1 is at location10. sinkbasin1 is at location16. cabinet3 is at location8. garbagecan1 is at location2.  Currently, the objects are at locations as follows. scrubbrush1 and plunger1 are at location10. toiletpaper1 and soapbar1 are at location7. towel1 and showerglass1 are at location5. soapbottle2 and spraybottle1 are at location4. lightswitch1 is at location13. cloth1 and candle1 are at location3. cloth2 is at location11. showerdoor1 is at location1. mirror1 is at location9. handtowel1 is at location18. soapbottle1 and spraybottle2 are at location15. spraybottle3 is at location2. sink1 is at location12. handtowel2 is at location17. sink2 is at location14. toiletpaper2 is at location8. agent agent1 is at location location17. The objects are in/on receptacle as follows. handtowel2 is on handtowelholder2. spraybottle3 is in garbagecan1. toiletpaper2 is in cabinet3. spraybottle1 and soapbottle2 are in cabinet1. soapbar1 and toiletpaper1 are in toilet1. towel1 is on towelholder1. spraybottle2 and soapbottle1 are in cabinet4. cloth1 and candle1 are on countertop1. cloth2 is in cabinet2. handtowel1 is on handtowelholder1. cabinet4, cabinet2, cabinet3, and cabinet1 are closed. Nothing has been validated. agent1 is holding object soapbar2. The goal is to reach a state where the following facts hold: It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. toiletpaper1 is at location11. B. soapbar2 is at location7. C. agent1 is holding object toiletpaper2. D. It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype.", "choices": {"label": ["A", "B", "C", "D"], "text": ["toiletpaper1 is at location11", "soapbar2 is at location7", "agent1 is holding object toiletpaper2", "It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 2463252122767603041, "group": "landmarks_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. countertop2 and coffeemachine1 are at location11. toaster1 is at location9. microwave1 is at location24. cabinet3 is at location16. cabinet2 is at location22. stoveburner4 and stoveburner2 are at location21. sinkbasin1 is at location6. fridge1 is at location10. shelf2 is at location29. cabinet6 is at location4. countertop3 is at location3. shelf1 is at location17. garbagecan1 is at location8. countertop1 is at location2. drawer1 is at location20. drawer3 is at location27. shelf3 is at location26. cabinet5 is at location13. cabinet4 is at location14. drawer2 is at location15. cabinet1 is at location19.  Currently, the objects are at locations as follows. vase1 and dishsponge2 are at location13. spatula3, glassbottle2, spatula2, potato2, and sink1 are at location6. soapbottle1 is at location4. apple1, lettuce2, cellphone2, peppershaker1, knife1, cellphone1, plate1, and lettuce1 are at location2. stoveknob3 and stoveknob2 are at location12. glassbottle1, butterknife1, spoon2, bread1, cellphone3, houseplant1, plate3, creditcard1, knife2, spatula1, and papertowelroll1 are at location3. saltshaker1 is at location15. cup1 is at location24. plate2, mug1, bowl1, tomato1, egg2, egg1, and potato1 are at location10. peppershaker2 is at location20. window2 is at location28. pan1 and spoon1 are at location11. bowl2 is at location29. stoveknob4 is at location7. apple2, potato3, and glassbottle3 are at location8. peppershaker3 is at location16. dishsponge1 and fork1 are at location27. window1 is at location30. chair2 is at location1. chair1 is at location23. soapbottle2 and statue1 are at location26. vase2 is at location17. pot1 is at location5. stoveknob1 is at location18. lightswitch1 is at location25. agent agent1 is at location location29. The objects are in/on receptacle as follows. statue1 and soapbottle2 are on shelf3. vase1 and dishsponge2 are in cabinet5. pot1 is on stoveburner3. spatula2, glassbottle2, spatula3, and potato2 are in sinkbasin1. dishsponge1 and fork1 are in drawer3. cup1 is in microwave1. lettuce2, plate1, knife1, cellphone1, cellphone2, lettuce1, apple1, and peppershaker1 are on countertop1. potato3, apple2, and glassbottle3 are in garbagecan1. saltshaker1 is in drawer2. mug1, potato1, egg1, tomato1, plate2, egg2, and bowl1 are in fridge1. spoon2, spatula1, knife2, glassbottle1, creditcard1, plate3, bread1, houseplant1, papertowelroll1, cellphone3, and butterknife1 are on countertop3. peppershaker2 is in drawer1. bowl2 is on shelf2. soapbottle1 is in cabinet6. pan1 is on stoveburner4. pot1 is on stoveburner1. peppershaker3 is in cabinet3. vase2 is on shelf1. spoon1 and pan1 are on countertop2. pan1 is on stoveburner2. cellphone2 is on plate1. drawer1, cabinet2, cabinet1, cabinet5, fridge1, drawer3, drawer2, and microwave1 are closed. mug1 is cool. mug2 is hot. Nothing has been validated. agent1 is holding object mug2. The goal is to reach a state where the following facts hold: It has been validated that an object of type mugtype is hot and is in a receptacle of type coffeemachinetype.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. potato1 is cool. B. It has been validated that an object of type spatulatype is in a receptacle of type drawertype. C. It has been validated that an object of type mugtype is hot and is in a receptacle of type shelftype. D. agent agent1 is at location location11.", "choices": {"label": ["A", "B", "C", "D"], "text": ["potato1 is cool", "It has been validated that an object of type spatulatype is in a receptacle of type drawertype", "It has been validated that an object of type mugtype is hot and is in a receptacle of type shelftype", "agent agent1 is at location location11"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 8072341704661862746, "group": "landmarks_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. countertop2 and coffeemachine1 are at location11. toaster1 is at location9. microwave1 is at location24. cabinet3 is at location16. cabinet2 is at location22. stoveburner4 and stoveburner2 are at location21. sinkbasin1 is at location6. fridge1 is at location10. shelf2 is at location29. cabinet6 is at location4. countertop3 is at location3. shelf1 is at location17. garbagecan1 is at location8. countertop1 is at location2. drawer1 is at location20. drawer3 is at location27. shelf3 is at location26. cabinet5 is at location13. cabinet4 is at location14. drawer2 is at location15. cabinet1 is at location19.  Currently, the objects are at locations as follows. glassbottle1 is at location19. glassbottle3, butterknife1, soapbottle1, houseplant1, saltshaker2, lettuce3, knife1, spoon3, tomato1, and fork2 are at location3. lettuce1, cup2, cup1, potato2, apple1, and potato1 are at location10. stoveknob3 and stoveknob2 are at location12. spoon2, fork1, spatula2, and sink1 are at location6. cellphone3, bread1, mug1, creditcard2, statue1, lettuce2, spoon1, and creditcard1 are at location2. saltshaker1 is at location16. vase2 and papertowelroll1 are at location29. cellphone1, pan1, and peppershaker1 are at location11. saltshaker3 is at location26. peppershaker2 and cellphone2 are at location20. plate1 is at location4. window2 is at location28. bowl1 and glassbottle2 are at location14. stoveknob4 is at location7. apple2 and egg2 are at location8. window1 is at location30. chair2 is at location1. chair1 is at location23. dishsponge1 is at location15. vase1 is at location17. spatula1 is at location27. pot1 is at location5. stoveknob1 is at location18. lightswitch1 is at location25. agent agent1 is at location location8. The objects are in/on receptacle as follows. soapbottle1, knife1, tomato1, fork2, lettuce3, saltshaker2, spoon3, houseplant1, butterknife1, and glassbottle3 are on countertop3. pot1 is on stoveburner3. spatula2, fork1, and spoon2 are in sinkbasin1. saltshaker1 is in cabinet3. dishsponge1 is in drawer2. apple1, potato1, potato2, lettuce1, cup2, and cup1 are in fridge1. lettuce2, creditcard2, spoon1, creditcard1, cellphone3, statue1, bread1, and mug1 are on countertop1. vase2 and papertowelroll1 are on shelf2. saltshaker3 is on shelf3. plate1 is in cabinet6. glassbottle2 and bowl1 are in cabinet4. apple2 and egg2 are in garbagecan1. spatula1 is in drawer3. peppershaker2 and cellphone2 are in drawer1. vase1 is on shelf1. peppershaker1, cellphone1, and pan1 are on countertop2. pan1 is on stoveburner4. pot1 is on stoveburner1. glassbottle1 is in cabinet1. pan1 is on stoveburner2. drawer1, cabinet2, cabinet1, cabinet5, fridge1, drawer3, drawer2, and microwave1 are closed. egg1 is hot. Nothing has been validated. agent1 is holding object egg1. The goal is to reach a state where the following facts hold: It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. plate1 is on countertop1. B. It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype. C. saltshaker3 is at location15. D. It has been validated that an object of type pottype is clean and is in a receptacle of type cabinettype.", "choices": {"label": ["A", "B", "C", "D"], "text": ["plate1 is on countertop1", "It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype", "saltshaker3 is at location15", "It has been validated that an object of type pottype is clean and is in a receptacle of type cabinettype"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 7474463588091933846, "group": "landmarks_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 16 object types: 1 candle, 2 cloths, 3 faucets, 2 handtowels, 1 lightswitch, 1 mirror, 1 plunger, 1 scrubbrush, 1 showerdoor, 1 showerglass, 2 sinks, 2 soapbars, 2 soapbottles, 3 spraybottles, 2 toiletpapers, 1 towel, 8 receptacle types: 4 cabinets, 1 countertop, 1 garbagecan, 2 handtowelholders, 2 sinkbasins, 1 toiletpaperhanger, 1 toilet, 1 towelholder, and 19 locations all numbered consecutively.  The receptacles are at locations as follows. handtowelholder2 is at location17. sinkbasin2 is at location6. toilet1 is at location7. cabinet1 is at location4. countertop1 is at location3. handtowelholder1 is at location18. cabinet2 is at location11. cabinet4 is at location15. towelholder1 is at location5. toiletpaperhanger1 is at location10. sinkbasin1 is at location16. cabinet3 is at location8. garbagecan1 is at location2.  Currently, the objects are at locations as follows. scrubbrush1 and plunger1 are at location10. toiletpaper1 and soapbar1 are at location7. towel1 and showerglass1 are at location5. soapbottle2 and spraybottle1 are at location4. lightswitch1 is at location13. cloth1, soapbar2, and candle1 are at location3. cloth2 is at location11. showerdoor1 is at location1. mirror1 is at location9. handtowel1 is at location18. soapbottle1 and spraybottle2 are at location15. spraybottle3 is at location2. sink1 is at location12. handtowel2 is at location17. sink2 is at location14. toiletpaper2 is at location8. agent agent1 is at location location10. The objects are in/on receptacle as follows. handtowel2 is on handtowelholder2. spraybottle3 is in garbagecan1. toiletpaper2 is in cabinet3. spraybottle1 and soapbottle2 are in cabinet1. soapbar1 and toiletpaper1 are in toilet1. towel1 is on towelholder1. spraybottle2 and soapbottle1 are in cabinet4. cloth1, candle1, and soapbar2 are on countertop1. cloth2 is in cabinet2. handtowel1 is on handtowelholder1. cabinet4, cabinet2, cabinet3, and cabinet1 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. spraybottle1 is at location2. B. cloth2 is at location15. C. It has been validated that two objects of type toiletpapertype are in a receptacle of type countertoptype. D. It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype.", "choices": {"label": ["A", "B", "C", "D"], "text": ["spraybottle1 is at location2", "cloth2 is at location15", "It has been validated that two objects of type toiletpapertype are in a receptacle of type countertoptype", "It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 3673285689265613903, "group": "landmarks_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. countertop2 and coffeemachine1 are at location11. toaster1 is at location9. microwave1 is at location24. cabinet3 is at location16. cabinet2 is at location22. stoveburner4 and stoveburner2 are at location21. sinkbasin1 is at location6. fridge1 is at location10. shelf2 is at location29. cabinet6 is at location4. countertop3 is at location3. shelf1 is at location17. garbagecan1 is at location8. countertop1 is at location2. drawer1 is at location20. drawer3 is at location27. shelf3 is at location26. cabinet5 is at location13. cabinet4 is at location14. drawer2 is at location15. cabinet1 is at location19.  Currently, the objects are at locations as follows. vase1 and dishsponge2 are at location13. spatula3, glassbottle2, spatula2, potato2, and sink1 are at location6. soapbottle1 is at location4. apple1, lettuce2, cellphone2, peppershaker1, knife1, cellphone1, plate1, and lettuce1 are at location2. stoveknob3 and stoveknob2 are at location12. glassbottle1, butterknife1, spoon2, bread1, cellphone3, houseplant1, plate3, creditcard1, knife2, spatula1, and papertowelroll1 are at location3. saltshaker1 is at location15. cup1 is at location24. plate2, mug1, bowl1, tomato1, egg2, egg1, and potato1 are at location10. peppershaker2 is at location20. window2 is at location28. pan1 and spoon1 are at location11. bowl2 is at location29. stoveknob4 is at location7. apple2, potato3, and glassbottle3 are at location8. peppershaker3 is at location16. dishsponge1 and fork1 are at location27. window1 is at location30. chair2 is at location1. chair1 is at location23. soapbottle2 and statue1 are at location26. vase2 is at location17. pot1 is at location5. stoveknob1 is at location18. lightswitch1 is at location25. agent agent1 is at location location3. The objects are in/on receptacle as follows. statue1 and soapbottle2 are on shelf3. vase1 and dishsponge2 are in cabinet5. pot1 is on stoveburner3. spatula2, glassbottle2, spatula3, and potato2 are in sinkbasin1. dishsponge1 and fork1 are in drawer3. cup1 is in microwave1. lettuce2, plate1, knife1, cellphone1, cellphone2, lettuce1, apple1, and peppershaker1 are on countertop1. potato3, apple2, and glassbottle3 are in garbagecan1. saltshaker1 is in drawer2. mug1, potato1, egg1, tomato1, plate2, egg2, and bowl1 are in fridge1. spoon2, spatula1, knife2, glassbottle1, creditcard1, plate3, bread1, houseplant1, papertowelroll1, cellphone3, and butterknife1 are on countertop3. peppershaker2 is in drawer1. bowl2 is on shelf2. soapbottle1 is in cabinet6. pan1 is on stoveburner4. pot1 is on stoveburner1. peppershaker3 is in cabinet3. vase2 is on shelf1. spoon1 and pan1 are on countertop2. pan1 is on stoveburner2. cellphone2 is on plate1. drawer1, cabinet2, cabinet1, cabinet5, fridge1, drawer3, drawer2, and microwave1 are closed. mug1 is cool. mug2 is hot. Nothing has been validated. agent1 is holding object mug2. The goal is to reach a state where the following facts hold: It has been validated that an object of type mugtype is hot and is in a receptacle of type coffeemachinetype.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. agent agent1 is at location location11. B. saltshaker1 is at location26. C. plate2 is at location2. D. butterknife1 is on countertop1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["agent agent1 is at location location11", "saltshaker1 is at location26", "plate2 is at location2", "butterknife1 is on countertop1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -9114955175993077227, "group": "landmarks_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 16 object types: 1 candle, 2 cloths, 3 faucets, 2 handtowels, 1 lightswitch, 1 mirror, 1 plunger, 1 scrubbrush, 1 showerdoor, 1 showerglass, 2 sinks, 2 soapbars, 2 soapbottles, 3 spraybottles, 2 toiletpapers, 1 towel, 8 receptacle types: 4 cabinets, 1 countertop, 1 garbagecan, 2 handtowelholders, 2 sinkbasins, 1 toiletpaperhanger, 1 toilet, 1 towelholder, and 19 locations all numbered consecutively.  The receptacles are at locations as follows. handtowelholder2 is at location17. sinkbasin2 is at location6. toilet1 is at location7. cabinet1 is at location4. countertop1 is at location3. handtowelholder1 is at location18. cabinet2 is at location11. cabinet4 is at location15. towelholder1 is at location5. toiletpaperhanger1 is at location10. sinkbasin1 is at location16. cabinet3 is at location8. garbagecan1 is at location2.  Currently, the objects are at locations as follows. scrubbrush1 and plunger1 are at location10. toiletpaper1 and soapbar1 are at location7. towel1 and showerglass1 are at location5. soapbottle2 and spraybottle1 are at location4. lightswitch1 is at location13. cloth1 and candle1 are at location3. cloth2 and soapbar2 are at location11. showerdoor1 is at location1. mirror1 is at location9. handtowel1 is at location18. soapbottle1 and spraybottle2 are at location15. spraybottle3 is at location2. sink1 is at location12. handtowel2 is at location17. sink2 is at location14. toiletpaper2 is at location8. agent agent1 is at location location11. The objects are in/on receptacle as follows. handtowel2 is on handtowelholder2. spraybottle3 is in garbagecan1. toiletpaper2 is in cabinet3. spraybottle1 and soapbottle2 are in cabinet1. soapbar1 and toiletpaper1 are in toilet1. towel1 is on towelholder1. spraybottle2 and soapbottle1 are in cabinet4. cloth1 and candle1 are on countertop1. cloth2 and soapbar2 are in cabinet2. handtowel1 is on handtowelholder1. cabinet4, cabinet3, and cabinet1 are closed. soapbar2 is clean. cabinet2 is checked. cabinet2 is open. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. soapbottle2 is in cabinet3. B. toiletpaper1 is in garbagecan1. C. handtowel1 is at location6. D. It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype.", "choices": {"label": ["A", "B", "C", "D"], "text": ["soapbottle2 is in cabinet3", "toiletpaper1 is in garbagecan1", "handtowel1 is at location6", "It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 6347893514412102976, "group": "landmarks_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 3 apples, 1 bowl, 1 bread, 2 butterknives, 3 cellphones, 2 chairs, 3 creditcards, 3 cups, 3 dishsponges, 2 eggs, 1 faucet, 2 forks, 2 glassbottles, 1 houseplant, 1 knife, 1 lettuce, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 2 peppershakers, 2 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 3 soapbottles, 3 spatulas, 2 spoons, 2 statues, 4 stoveknobs, 3 tomatoes, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 32 locations all numbered consecutively.  The receptacles are at locations as follows. drawer1 is at location21. stoveburner4 and stoveburner2 are at location22. stoveburner3 and stoveburner1 are at location6. coffeemachine1 and countertop2 are at location12. countertop3 is at location4. drawer3 is at location28. cabinet3 is at location17. microwave1 is at location25. shelf1 is at location18. toaster1 is at location10. sinkbasin1 is at location7. shelf3 is at location27. cabinet5 is at location14. fridge1 is at location11. cabinet2 is at location23. countertop1 is at location3. cabinet4 is at location15. shelf2 is at location30. garbagecan1 is at location9. cabinet6 is at location5. drawer2 is at location16. cabinet1 is at location20.  Currently, the objects are at locations as follows. stoveknob3 and stoveknob2 are at location13. papertowelroll1, soapbottle2, and glassbottle1 are at location9. statue1, bread1, apple2, apple1, apple3, creditcard1, cellphone1, dishsponge3, and spatula2 are at location3. butterknife1, peppershaker2, fork2, butterknife2, houseplant1, cellphone3, spoon2, statue2, spatula3, soapbottle3, and knife1 are at location4. peppershaker1 is at location20. tomato2, potato2, egg1, cup1, cup2, potato1, tomato1, and lettuce1 are at location11. stoveknob1 is at location19. creditcard3 and saltshaker1 are at location27. cup3, potato3, egg2, sink1, tomato3, and fork1 are at location7. plate1 and plate2 are at location14. window1 is at location31. soapbottle1 and vase1 are at location5. lightswitch1 is at location26. cellphone2 is at location21. mug2, bowl1, glassbottle2, and creditcard2 are at location30. spatula1 is at location28. chair1 is at location24. window2 is at location29. chair2 is at location1. dishsponge1 and spoon1 are at location16. mug1 is at location25. vase2 is at location18. pot1 is at location22. dishsponge2 is at location2. pan1 is at location6. stoveknob4 is at location8. agent agent1 is at location location4. The objects are in/on receptacle as follows. plate2, dishsponge2, and plate1 are in cabinet5. bowl1, mug2, glassbottle2, and creditcard2 are on shelf2. papertowelroll1, soapbottle2, and glassbottle1 are in garbagecan1. knife1, fork2, spoon2, soapbottle3, butterknife2, statue2, houseplant1, cellphone3, peppershaker2, butterknife1, and spatula3 are on countertop3. pan1 is on stoveburner3. dishsponge1 and spoon1 are in drawer2. peppershaker1 is in cabinet1. spatula2, dishsponge3, cellphone1, creditcard1, apple2, statue1, bread1, apple1, and apple3 are on countertop1. mug1 is in microwave1. potato1, egg1, tomato1, potato2, tomato2, lettuce1, cup2, and cup1 are in fridge1. fork1, cup3, tomato3, egg2, and potato3 are in sinkbasin1. pot1 is on stoveburner2. spatula1 is in drawer3. pan1 is on stoveburner1. creditcard3 and saltshaker1 are on shelf3. soapbottle1 and vase1 are in cabinet6. pot1 is on stoveburner4. cellphone2 is in drawer1. dishsponge2 is on plate1. vase2 is on shelf1. drawer1, cabinet2, cabinet1, cabinet5, fridge1, drawer3, drawer2, and microwave1 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type saltshakertype is in a receptacle of type cabinettype.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. It has been validated that an object of type saltshakertype is in a receptacle of type cabinettype. B. creditcard2 is on countertop1. C. peppershaker2 is at location18. D. peppershaker1 is at location17.", "choices": {"label": ["A", "B", "C", "D"], "text": ["It has been validated that an object of type saltshakertype is in a receptacle of type cabinettype", "creditcard2 is on countertop1", "peppershaker2 is at location18", "peppershaker1 is at location17"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -3904675772082885588, "group": "landmarks_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 3 apples, 1 bowl, 1 bread, 2 butterknives, 3 cellphones, 2 chairs, 3 creditcards, 3 cups, 3 dishsponges, 2 eggs, 1 faucet, 2 forks, 2 glassbottles, 1 houseplant, 1 knife, 1 lettuce, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 2 peppershakers, 2 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 3 soapbottles, 3 spatulas, 2 spoons, 2 statues, 4 stoveknobs, 3 tomatoes, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 32 locations all numbered consecutively.  The receptacles are at locations as follows. drawer1 is at location21. stoveburner4 and stoveburner2 are at location22. stoveburner3 and stoveburner1 are at location6. coffeemachine1 and countertop2 are at location12. countertop3 is at location4. drawer3 is at location28. cabinet3 is at location17. microwave1 is at location25. shelf1 is at location18. toaster1 is at location10. sinkbasin1 is at location7. shelf3 is at location27. cabinet5 is at location14. fridge1 is at location11. cabinet2 is at location23. countertop1 is at location3. cabinet4 is at location15. shelf2 is at location30. garbagecan1 is at location9. cabinet6 is at location5. drawer2 is at location16. cabinet1 is at location20.  Currently, the objects are at locations as follows. stoveknob3 and stoveknob2 are at location13. papertowelroll1, soapbottle2, and glassbottle1 are at location9. statue1, bread1, apple2, apple1, apple3, creditcard1, cellphone1, dishsponge3, and spatula2 are at location3. butterknife1, peppershaker2, fork2, butterknife2, houseplant1, cellphone3, spoon2, statue2, spatula3, soapbottle3, and knife1 are at location4. peppershaker1 is at location20. tomato2, potato2, egg1, cup1, cup2, potato1, tomato1, and lettuce1 are at location11. stoveknob1 is at location19. saltshaker1 is at location15. creditcard3 is at location27. cup3, potato3, egg2, sink1, tomato3, and fork1 are at location7. plate1 and plate2 are at location14. window1 is at location31. soapbottle1 and vase1 are at location5. lightswitch1 is at location26. cellphone2 is at location21. mug2, bowl1, glassbottle2, and creditcard2 are at location30. spatula1 is at location28. chair1 is at location24. window2 is at location29. chair2 is at location1. dishsponge1 and spoon1 are at location16. mug1 is at location25. vase2 is at location18. pot1 is at location22. dishsponge2 is at location2. pan1 is at location6. stoveknob4 is at location8. agent agent1 is at location location9. The objects are in/on receptacle as follows. plate2, dishsponge2, and plate1 are in cabinet5. bowl1, mug2, glassbottle2, and creditcard2 are on shelf2. papertowelroll1, soapbottle2, and glassbottle1 are in garbagecan1. knife1, fork2, spoon2, soapbottle3, butterknife2, statue2, houseplant1, cellphone3, peppershaker2, butterknife1, and spatula3 are on countertop3. pan1 is on stoveburner3. dishsponge1 and spoon1 are in drawer2. peppershaker1 is in cabinet1. spatula2, dishsponge3, cellphone1, creditcard1, apple2, statue1, bread1, apple1, and apple3 are on countertop1. mug1 is in microwave1. potato1, egg1, tomato1, potato2, tomato2, lettuce1, cup2, and cup1 are in fridge1. fork1, cup3, tomato3, egg2, and potato3 are in sinkbasin1. pot1 is on stoveburner2. spatula1 is in drawer3. pan1 is on stoveburner1. creditcard3 is on shelf3. soapbottle1 and vase1 are in cabinet6. pot1 is on stoveburner4. cellphone2 is in drawer1. dishsponge2 is on plate1. vase2 is on shelf1. saltshaker1 is in cabinet4. drawer1, cabinet2, cabinet1, cabinet5, fridge1, drawer3, drawer2, and microwave1 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type saltshakertype is in a receptacle of type cabinettype.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. It has been validated that two objects of type tomatotype are in a receptacle of type sinkbasintype. B. It has been validated that an object of type saltshakertype is in a receptacle of type cabinettype. C. saltshaker1 is at location18. D. fork2 is in sinkbasin1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["It has been validated that two objects of type tomatotype are in a receptacle of type sinkbasintype", "It has been validated that an object of type saltshakertype is in a receptacle of type cabinettype", "saltshaker1 is at location18", "fork2 is in sinkbasin1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": *******************, "group": "landmarks_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. countertop2 and coffeemachine1 are at location11. toaster1 is at location9. microwave1 is at location24. cabinet3 is at location16. cabinet2 is at location22. stoveburner4 and stoveburner2 are at location21. sinkbasin1 is at location6. fridge1 is at location10. shelf2 is at location29. cabinet6 is at location4. countertop3 is at location3. shelf1 is at location17. garbagecan1 is at location8. countertop1 is at location2. drawer1 is at location20. drawer3 is at location27. shelf3 is at location26. cabinet5 is at location13. cabinet4 is at location14. drawer2 is at location15. cabinet1 is at location19.  Currently, the objects are at locations as follows. glassbottle1 is at location19. glassbottle3, butterknife1, soapbottle1, houseplant1, saltshaker2, lettuce3, knife1, spoon3, tomato1, and fork2 are at location3. lettuce1, cup2, cup1, potato2, apple1, and potato1 are at location10. stoveknob3 and stoveknob2 are at location12. spoon2, fork1, spatula2, and sink1 are at location6. cellphone3, bread1, mug1, creditcard2, statue1, lettuce2, spoon1, and creditcard1 are at location2. saltshaker1 is at location16. vase2 and papertowelroll1 are at location29. cellphone1, pan1, and peppershaker1 are at location11. saltshaker3 is at location26. peppershaker2 and cellphone2 are at location20. plate1 is at location4. window2 is at location28. bowl1 and glassbottle2 are at location14. stoveknob4 is at location7. apple2 and egg1 are at location8. window1 is at location30. chair2 is at location1. chair1 is at location23. dishsponge1 is at location15. vase1 is at location17. spatula1 is at location27. pot1 is at location5. stoveknob1 is at location18. lightswitch1 is at location25. agent agent1 is at location location8. The objects are in/on receptacle as follows. soapbottle1, knife1, tomato1, fork2, lettuce3, saltshaker2, spoon3, houseplant1, butterknife1, and glassbottle3 are on countertop3. pot1 is on stoveburner3. spatula2, fork1, and spoon2 are in sinkbasin1. saltshaker1 is in cabinet3. dishsponge1 is in drawer2. apple1, potato1, potato2, lettuce1, cup2, and cup1 are in fridge1. lettuce2, creditcard2, spoon1, creditcard1, cellphone3, statue1, bread1, and mug1 are on countertop1. vase2 and papertowelroll1 are on shelf2. saltshaker3 is on shelf3. plate1 is in cabinet6. glassbottle2 and bowl1 are in cabinet4. apple2 and egg1 are in garbagecan1. spatula1 is in drawer3. peppershaker2 and cellphone2 are in drawer1. vase1 is on shelf1. peppershaker1, cellphone1, and pan1 are on countertop2. pan1 is on stoveburner4. pot1 is on stoveburner1. glassbottle1 is in cabinet1. pan1 is on stoveburner2. drawer1, cabinet2, cabinet1, cabinet5, fridge1, drawer3, drawer2, and microwave1 are closed. egg2 is hot. Nothing has been validated. agent1 is holding object egg2. The goal is to reach a state where the following facts hold: It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. It has been validated that an object of type potatotype is in a receptacle of type garbagecantype. B. plate1 is at location22. C. It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype. D. agent1 is holding object spoon1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["It has been validated that an object of type potatotype is in a receptacle of type garbagecantype", "plate1 is at location22", "It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype", "agent1 is holding object spoon1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 7539660820340071809, "group": "landmarks_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. countertop2 and coffeemachine1 are at location11. toaster1 is at location9. microwave1 is at location24. cabinet3 is at location16. cabinet2 is at location22. stoveburner4 and stoveburner2 are at location21. sinkbasin1 is at location6. fridge1 is at location10. shelf2 is at location29. cabinet6 is at location4. countertop3 is at location3. shelf1 is at location17. garbagecan1 is at location8. countertop1 is at location2. drawer1 is at location20. drawer3 is at location27. shelf3 is at location26. cabinet5 is at location13. cabinet4 is at location14. drawer2 is at location15. cabinet1 is at location19.  Currently, the objects are at locations as follows. vase1 and dishsponge2 are at location13. spatula3, glassbottle2, spatula2, potato2, and sink1 are at location6. soapbottle1 is at location4. apple1, lettuce2, cellphone2, peppershaker1, knife1, cellphone1, plate1, and lettuce1 are at location2. stoveknob3 and stoveknob2 are at location12. glassbottle1, butterknife1, spoon2, bread1, cellphone3, houseplant1, plate3, creditcard1, knife2, spatula1, and papertowelroll1 are at location3. saltshaker1 is at location15. cup1 is at location24. plate2, mug1, bowl1, tomato1, egg2, egg1, and potato1 are at location10. peppershaker2 is at location20. window2 is at location28. pan1 and spoon1 are at location11. bowl2 is at location29. stoveknob4 is at location7. apple2, glassbottle3, and potato3 are at location8. peppershaker3 is at location16. dishsponge1 and fork1 are at location27. window1 is at location30. chair2 is at location1. chair1 is at location23. soapbottle2 and statue1 are at location26. vase2 is at location17. pot1 is at location5. stoveknob1 is at location18. lightswitch1 is at location25. agent agent1 is at location location16. The objects are in/on receptacle as follows. statue1 and soapbottle2 are on shelf3. vase1 and dishsponge2 are in cabinet5. pot1 is on stoveburner3. spatula2, glassbottle2, spatula3, and potato2 are in sinkbasin1. dishsponge1 and fork1 are in drawer3. cup1 is in microwave1. lettuce2, plate1, knife1, cellphone1, cellphone2, lettuce1, apple1, and peppershaker1 are on countertop1. potato3, apple2, and glassbottle3 are in garbagecan1. saltshaker1 is in drawer2. mug1, potato1, egg1, tomato1, plate2, egg2, and bowl1 are in fridge1. spoon2, spatula1, knife2, glassbottle1, creditcard1, plate3, bread1, houseplant1, papertowelroll1, cellphone3, and butterknife1 are on countertop3. peppershaker2 is in drawer1. bowl2 is on shelf2. soapbottle1 is in cabinet6. pan1 is on stoveburner4. pot1 is on stoveburner1. peppershaker3 is in cabinet3. vase2 is on shelf1. spoon1 and pan1 are on countertop2. pan1 is on stoveburner2. cellphone2 is on plate1. drawer1, cabinet2, cabinet1, cabinet5, fridge1, drawer3, drawer2, and microwave1 are closed. mug1 is cool. Nothing has been validated. agent1 is holding object mug2. The goal is to reach a state where the following facts hold: It has been validated that an object of type mugtype is hot and is in a receptacle of type coffeemachinetype.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. spatula3 is at location11. B. agent agent1 is at location location11. C. It has been validated that an object of type dishspongetype is in a receptacle of type drawertype. D. It has been validated that an object of type tomatotype is hot and is in a receptacle of type countertoptype.", "choices": {"label": ["A", "B", "C", "D"], "text": ["spatula3 is at location11", "agent agent1 is at location location11", "It has been validated that an object of type dishspongetype is in a receptacle of type drawertype", "It has been validated that an object of type tomatotype is hot and is in a receptacle of type countertoptype"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
