{"id": -7341033865075530299, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1 and both grippers are free. Additionally, ball2 is at room1, ball3 and ball1 are at room4, ball4 is at room5. The goal is to reach a state where the following facts hold: Ball ball4 is at room5 location, Ball ball3 is at room4 location, Ball ball2 is in room room3, and Ball ball1 is at room4 location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is at room3 location", "answer": "yes"}
{"id": -8804551041508343667, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball4 and ball2 are at room2, ball1 is at room4, ball3 is at room10. The goal is to reach a state where the following facts hold: Ball ball1 is at room8 location, Ball ball2 is in room room6, Ball ball4 is at room9 location, and Ball ball3 is in room room8.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is in room room4", "answer": "yes"}
{"id": 797541817915744269, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball15, and right gripper is carrying the ball ball6. Additionally, ball5, ball11, ball2, ball4, ball10, ball9, and ball14 are at room1, ball12, ball1, ball13, and ball3 are at room2, ball8 and ball7 are at room3. The goal is to reach a state where the following facts hold: Ball ball5 is in room room1, Ball ball14 is in room room3, Ball ball8 is in room room1, Ball ball12 is in room room2, Ball ball4 is at room1 location, Ball ball2 is at room1 location, Ball ball6 is at room1 location, Ball ball15 is in room room1, Ball ball1 is at room2 location, Ball ball9 is at room3 location, Ball ball10 is at room3 location, Ball ball11 is at room3 location, Ball ball7 is at room3 location, Ball ball13 is at room2 location, and Ball ball3 is in room room2.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is in room room3", "answer": "yes"}
{"id": 8094631974392802645, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball10, and right gripper is carrying the ball ball15. Additionally, ball5, ball11, ball2, ball4, ball6, ball9, and ball14 are at room1, ball12, ball1, ball13, and ball3 are at room2, ball8 and ball7 are at room3. The goal is to reach a state where the following facts hold: Ball ball5 is at room1 location, Ball ball14 is at room3 location, Ball ball8 is at room1 location, Ball ball12 is in room room2, Ball ball4 is in room room1, Ball ball2 is in room room1, Ball ball6 is at room1 location, Ball ball15 is in room room1, Ball ball1 is in room room2, Ball ball9 is in room room3, Ball ball10 is in room room3, Ball ball11 is at room3 location, Ball ball7 is in room room3, Ball ball13 is in room room2, and Ball ball3 is at room2 location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is in room room3", "answer": "yes"}
{"id": 2248249855581234856, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room6, right gripper is free, and left gripper is carrying the ball ball3. Additionally, ball4 is at room9, ball1 is at room4, ball2 is at room6. The goal is to reach a state where the following facts hold: Ball ball1 is in room room8, Ball ball2 is at room6 location, Ball ball4 is in room room9, and Ball ball3 is in room room8.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is in room room8", "answer": "yes"}
{"id": -2876733251208662020, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, right gripper is free, and left gripper is carrying the ball ball1. Additionally, ball4, ball2, and ball3 are at room2. The goal is to reach a state where the following facts hold: Ball ball2 is at room2 location, Ball ball1 is at room2 location, Ball ball4 is at room2 location, and Ball ball3 is in room room2.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Ball ball1 is in room room1", "answer": "no"}
{"id": -8530413640210327926, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball4, and right gripper is carrying the ball ball2. Additionally, ball1 and ball3 are at room2. The goal is to reach a state where the following facts hold: Ball ball2 is in room room2, Ball ball1 is at room2 location, Ball ball4 is in room room2, and Ball ball3 is at room2 location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Ball ball2 is in room room2", "answer": "yes"}
{"id": -4501150682243790741, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball3, and right gripper is carrying the ball ball4. Additionally, ball1 is at room1, ball2 is at room2. The goal is to reach a state where the following facts hold: Ball ball2 is at room2 location, Ball ball1 is at room2 location, Ball ball4 is at room2 location, and Ball ball3 is at room2 location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is in room room2", "answer": "yes"}
{"id": -462891615075826088, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room1, right gripper is free, and left gripper is carrying the ball ball10. Additionally, ball11, ball8, ball2, ball15, ball4, ball6, ball9, and ball14 are at room1, ball12, ball1, ball13, and ball3 are at room2, ball7 and ball5 are at room3. The goal is to reach a state where the following facts hold: Ball ball5 is at room1 location, Ball ball14 is at room3 location, Ball ball8 is at room1 location, Ball ball12 is at room2 location, Ball ball4 is in room room1, Ball ball2 is in room room1, Ball ball6 is at room1 location, Ball ball15 is in room room1, Ball ball1 is at room2 location, Ball ball9 is at room3 location, Ball ball10 is at room3 location, Ball ball11 is at room3 location, Ball ball7 is in room room3, Ball ball13 is in room room2, and Ball ball3 is in room room2.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Ball ball14 is at room2 location", "answer": "no"}
{"id": -793582170106558144, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room5, left gripper is free, and right gripper is carrying the ball ball4. Additionally, ball1 is at room4, ball2 is at room6, ball3 is at room10. The goal is to reach a state where the following facts hold: Ball ball1 is at room8 location, Ball ball2 is at room6 location, Ball ball4 is at room9 location, and Ball ball3 is at room8 location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Ball ball4 is in room room7", "answer": "no"}
{"id": 3113431555141572725, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 7 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball3, ball2, and ball1 are at room3, ball4 is at room6. The goal is to reach a state where the following facts hold: Ball ball3 is in room room3, Ball ball2 is in room room2, Ball ball1 is in room room1, and Ball ball4 is at room1 location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Ball ball2 is in room room4", "answer": "no"}
{"id": -7771405907353553441, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2 and both grippers are free. Additionally, ball4 is at room1, ball3 and ball1 are at room3, ball2 is at room2. The goal is to reach a state where the following facts hold: Ball ball3 is in room room3, Ball ball1 is in room room3, Ball ball4 is at room3 location, and Ball ball2 is at room2 location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Ball ball2 is at room3 location", "answer": "no"}
{"id": 1399289264857206271, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room5, right gripper is free, and left gripper is carrying the ball ball1. Additionally, ball4 and ball2 are at room2, ball3 is at room3. The goal is to reach a state where the following facts hold: Ball ball3 is at room3 location, Ball ball2 is in room room2, Ball ball1 is in room room1, and Ball ball4 is at room1 location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is in room room1", "answer": "yes"}
{"id": -299998383572851420, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball1 is at room1, ball4 and ball2 are at room2, ball3 is at room3. The goal is to reach a state where the following facts hold: Ball ball3 is at room3 location, Ball ball1 is at room3 location, Ball ball4 is at room3 location, and Ball ball2 is in room room2.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is at room1 location", "answer": "yes"}
{"id": -7822544754909932731, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, right gripper is free, and left gripper is carrying the ball ball1. Additionally, ball4 and ball2 are at room1, ball3 is at room3. The goal is to reach a state where the following facts hold: Ball ball3 is in room room3, Ball ball1 is at room3 location, Ball ball4 is at room3 location, and Ball ball2 is in room room2.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is carrying the ball ball2 in the right gripper", "answer": "no"}
