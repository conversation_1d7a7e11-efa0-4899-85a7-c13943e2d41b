{"id": 8373848456019174669, "group": "action_justification_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. drawer3 is at location17. desk2 is at location10. shelf2 is at location25. shelf3 is at location11. desk1 is at location3. bed1 is at location13. shelf5 is at location22. drawer6 is at location1. garbagecan1 is at location2. safe1 is at location6. drawer5 and drawer4 are at location12. shelf1 is at location20. shelf4 is at location23. laundryhamper1 is at location8. drawer1 is at location21. drawer2 is at location18. shelf6 is at location24.  Currently, the objects are at locations as follows. pillow1, laptop2, cellphone1, book1, pillow2, and laptop1 are at location13. laundryhamperlid1 is at location8. blinds2 is at location15. desklamp1, alarmclock3, and bowl2 are at location23. pencil2 and creditcard1 are at location22. pen1, cellphone2, mug2, pencil3, and cd3 are at location10. baseballbat1 is at location9. window1 is at location5. window2 is at location4. lightswitch1 is at location14. chair2 is at location26. chair1 is at location21. cellphone3 is at location12. keychain1 and keychain2 are at location6. blinds1 is at location16. mirror1 is at location19. cd1, mug1, pencil1, bowl1, and alarmclock1 are at location3. basketball1 is at location7. bowl3 is at location24. cd2 is at location2. alarmclock2 is at location11. agent agent1 is at location location27. The objects are in/on receptacle as follows. pencil3, pen1, desklamp1, mug2, cellphone2, bowl2, alarmclock3, and cd3 are on desk2. bowl3 is on shelf6. bowl1, mug1, pencil1, cd1, and alarmclock1 are on desk1. cellphone3 is in drawer5. laptop2, book1, cellphone1, laptop1, pillow1, and pillow2 are in bed1. alarmclock2 is on shelf3. pencil2 and creditcard1 are on shelf5. keychain1 and keychain2 are in safe1. bowl2, alarmclock3, and desklamp1 are on shelf4. cd2 is in garbagecan1. drawer6, drawer3, safe1, and drawer1 are closed. desklamp1 is off. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type booktype is examined under an object of type desklamptype.", "question": "Given the plan: \"agent agent1 navigates to the receptacle bed1 from the current location location27 to the next location location13, agent agent1 navigates to the receptacle laundryhamper1 from the current location location13 to the next location location8, agent agent1 navigates to the receptacle bed1 from the current location location8 to the next location location13, agent agent1 picks up object book1 from receptacle bed1 that is at location location13, agent agent1 navigates to the receptacle drawer1 from the current location location13 to the next location location21, agent agent1 navigates to the receptacle drawer6 from the current location location21 to the next location location1, agent agent1 navigates to the receptacle desk2 from the current location location1 to the next location location10, agent agent1 turns on object desklamp1 that is on the receptacle desk2 at location location10, agent agent1 navigates to the receptacle shelf4 from the current location location10 to the next location location23, check that the togglable object desklamp1 of type desklamptype is toggled and in receptacle shelf4 at location location23 while agent agent1 is holding object book1 of type booktype\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. agent agent1 navigates to the receptacle bed1 from the current location location8 to the next location location13 and agent agent1 picks up object book1 from receptacle bed1 that is at location location13. B. agent agent1 navigates to the receptacle laundryhamper1 from the current location location13 to the next location location8 and agent agent1 navigates to the receptacle bed1 from the current location location8 to the next location location13. C. agent agent1 navigates to the receptacle drawer1 from the current location location13 to the next location location21 and agent agent1 navigates to the receptacle drawer6 from the current location location21 to the next location location1. D. agent agent1 navigates to the receptacle drawer6 from the current location location21 to the next location location1 and agent agent1 navigates to the receptacle desk2 from the current location location1 to the next location location10.", "choices": {"label": ["A", "B", "C", "D"], "text": ["agent agent1 navigates to the receptacle bed1 from the current location location8 to the next location location13 and agent agent1 picks up object book1 from receptacle bed1 that is at location location13", "agent agent1 navigates to the receptacle laundryhamper1 from the current location location13 to the next location location8 and agent agent1 navigates to the receptacle bed1 from the current location location8 to the next location location13", "agent agent1 navigates to the receptacle drawer1 from the current location location13 to the next location location21 and agent agent1 navigates to the receptacle drawer6 from the current location location21 to the next location location1", "agent agent1 navigates to the receptacle drawer6 from the current location location21 to the next location location1 and agent agent1 navigates to the receptacle desk2 from the current location location1 to the next location location10"]}, "query": "Given the plan: \"agent agent1 navigates to the receptacle bed1 from the current location location27 to the next location location13, agent agent1 navigates to the receptacle laundryhamper1 from the current location location13 to the next location location8, agent agent1 navigates to the receptacle bed1 from the current location location8 to the next location location13, agent agent1 picks up object book1 from receptacle bed1 that is at location location13, agent agent1 navigates to the receptacle drawer1 from the current location location13 to the next location location21, agent agent1 navigates to the receptacle drawer6 from the current location location21 to the next location location1, agent agent1 navigates to the receptacle desk2 from the current location location1 to the next location location10, agent agent1 turns on object desklamp1 that is on the receptacle desk2 at location location10, agent agent1 navigates to the receptacle shelf4 from the current location location10 to the next location location23, check that the togglable object desklamp1 of type desklamptype is toggled and in receptacle shelf4 at location location23 while agent agent1 is holding object book1 of type booktype\"; which pair of consecutive actions can be removed from this plan?", "answer": "B"}
{"id": -8523288516399011528, "group": "action_justification_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet5 is at location13. stoveburner2 and stoveburner4 are at location21. shelf3 is at location26. drawer2 is at location15. cabinet3 is at location16. sinkbasin1 is at location6. drawer3 is at location27. microwave1 is at location24. coffeemachine1 and countertop2 are at location11. cabinet2 is at location22. fridge1 is at location10. garbagecan1 is at location8. countertop1 is at location2. cabinet1 is at location19. shelf2 is at location29. drawer1 is at location20. stoveburner1 and stoveburner3 are at location5. shelf1 is at location17. cabinet4 is at location14. countertop3 is at location3. toaster1 is at location9. cabinet6 is at location4.  Currently, the objects are at locations as follows. cellphone1, lettuce1, plate1, apple1, knife1, lettuce2, peppershaker1, and cellphone2 are at location2. spoon2, knife2, houseplant1, glassbottle1, cellphone3, plate3, papertowelroll1, mug2, butterknife1, bread1, spatula1, and creditcard1 are at location3. window1 is at location30. apple2, glassbottle3, and potato3 are at location8. saltshaker1 is at location15. stoveknob1 is at location18. soapbottle2 and statue1 are at location26. fork1 and dishsponge1 are at location27. pan1 and spoon1 are at location11. dishsponge2 and vase1 are at location13. bowl1, egg2, egg1, potato1, tomato1, plate2, and mug1 are at location10. stoveknob2 and stoveknob3 are at location12. spatula2, potato2, glassbottle2, sink1, and spatula3 are at location6. chair1 is at location23. lightswitch1 is at location25. cup1 is at location24. vase2 is at location17. peppershaker3 is at location16. stoveknob4 is at location7. bowl2 is at location29. peppershaker2 is at location20. soapbottle1 is at location4. window2 is at location28. chair2 is at location1. pot1 is at location5. agent agent1 is at location location31. The objects are in/on receptacle as follows. lettuce1, knife1, cellphone2, peppershaker1, apple1, lettuce2, plate1, and cellphone1 are on countertop1. saltshaker1 is in drawer2. mug1, potato1, plate2, tomato1, bowl1, egg1, and egg2 are in fridge1. mug2, spatula1, glassbottle1, creditcard1, plate3, cellphone3, butterknife1, houseplant1, knife2, bread1, spoon2, and papertowelroll1 are on countertop3. pan1 is on stoveburner4. pot1 is on stoveburner1. vase2 is on shelf1. pan1 is on stoveburner2. glassbottle2, spatula3, potato2, and spatula2 are in sinkbasin1. statue1 and soapbottle2 are on shelf3. potato3, glassbottle3, and apple2 are in garbagecan1. peppershaker3 is in cabinet3. dishsponge2 and vase1 are in cabinet5. dishsponge1 and fork1 are in drawer3. soapbottle1 is in cabinet6. cellphone2 is on plate1. cup1 is in microwave1. pot1 is on stoveburner3. bowl2 is on shelf2. spoon1 and pan1 are on countertop2. peppershaker2 is in drawer1. cabinet5, fridge1, drawer2, cabinet2, drawer1, drawer3, cabinet1, and microwave1 are closed. mug1 is cool. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type mugtype is hot and is in a receptacle of type coffeemachinetype.", "question": "Given the plan: \"agent agent1 navigates to the receptacle countertop2 from the current location location31 to the next location location11, agent agent1 navigates to the receptacle countertop3 from the current location location11 to the next location location3, agent agent1 navigates to the receptacle cabinet2 from the current location location3 to the next location location22, agent agent1 navigates to the receptacle countertop3 from the current location location22 to the next location location3, agent agent1 picks up object mug2 from receptacle countertop3 that is at location location3, agent agent1 navigates to the receptacle countertop2 from the current location location3 to the next location location11, agent agent1 navigates to the receptacle microwave1 from the current location location11 to the next location location24, agent agent1 heats up object mug2 with a microwave microwave1 at location location24, agent agent1 navigates to the receptacle countertop2 from the current location location24 to the next location location11, agent agent1 puts down an object mug2 of type mugtype in a receptacle coffeemachine1 of type coffeemachinetype that is at location location11, ensure that object mug2 of type mugtype is hot and in receptacle coffeemachine1 of type coffeemachinetype\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. agent agent1 picks up object mug2 from receptacle countertop3 that is at location location3 and agent agent1 navigates to the receptacle countertop2 from the current location location3 to the next location location11. B. agent agent1 navigates to the receptacle countertop3 from the current location location11 to the next location location3 and agent agent1 navigates to the receptacle cabinet2 from the current location location3 to the next location location22. C. agent agent1 navigates to the receptacle cabinet2 from the current location location3 to the next location location22 and agent agent1 navigates to the receptacle countertop3 from the current location location22 to the next location location3. D. agent agent1 navigates to the receptacle countertop3 from the current location location22 to the next location location3 and agent agent1 picks up object mug2 from receptacle countertop3 that is at location location3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["agent agent1 picks up object mug2 from receptacle countertop3 that is at location location3 and agent agent1 navigates to the receptacle countertop2 from the current location location3 to the next location location11", "agent agent1 navigates to the receptacle countertop3 from the current location location11 to the next location location3 and agent agent1 navigates to the receptacle cabinet2 from the current location location3 to the next location location22", "agent agent1 navigates to the receptacle cabinet2 from the current location location3 to the next location location22 and agent agent1 navigates to the receptacle countertop3 from the current location location22 to the next location location3", "agent agent1 navigates to the receptacle countertop3 from the current location location22 to the next location location3 and agent agent1 picks up object mug2 from receptacle countertop3 that is at location location3"]}, "query": "Given the plan: \"agent agent1 navigates to the receptacle countertop2 from the current location location31 to the next location location11, agent agent1 navigates to the receptacle countertop3 from the current location location11 to the next location location3, agent agent1 navigates to the receptacle cabinet2 from the current location location3 to the next location location22, agent agent1 navigates to the receptacle countertop3 from the current location location22 to the next location location3, agent agent1 picks up object mug2 from receptacle countertop3 that is at location location3, agent agent1 navigates to the receptacle countertop2 from the current location location3 to the next location location11, agent agent1 navigates to the receptacle microwave1 from the current location location11 to the next location location24, agent agent1 heats up object mug2 with a microwave microwave1 at location location24, agent agent1 navigates to the receptacle countertop2 from the current location location24 to the next location location11, agent agent1 puts down an object mug2 of type mugtype in a receptacle coffeemachine1 of type coffeemachinetype that is at location location11, ensure that object mug2 of type mugtype is hot and in receptacle coffeemachine1 of type coffeemachinetype\"; which pair of consecutive actions can be removed from this plan?", "answer": "C"}
{"id": 6589150745980453782, "group": "action_justification_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet5 is at location13. stoveburner2 and stoveburner4 are at location21. shelf3 is at location26. drawer2 is at location15. cabinet3 is at location16. sinkbasin1 is at location6. drawer3 is at location27. microwave1 is at location24. coffeemachine1 and countertop2 are at location11. cabinet2 is at location22. fridge1 is at location10. garbagecan1 is at location8. countertop1 is at location2. cabinet1 is at location19. shelf2 is at location29. drawer1 is at location20. stoveburner1 and stoveburner3 are at location5. shelf1 is at location17. cabinet4 is at location14. countertop3 is at location3. toaster1 is at location9. cabinet6 is at location4.  Currently, the objects are at locations as follows. saltshaker2, houseplant1, tomato1, fork2, spoon3, butterknife1, soapbottle1, knife1, lettuce3, and glassbottle3 are at location3. spoon1, lettuce2, creditcard2, cellphone3, creditcard1, mug1, statue1, and bread1 are at location2. dishsponge1 is at location15. window1 is at location30. apple2, egg2, and egg1 are at location8. vase2 and papertowelroll1 are at location29. stoveknob1 is at location18. glassbottle1 is at location19. pan1, peppershaker1, and cellphone1 are at location11. stoveknob2 and stoveknob3 are at location12. plate1 is at location4. spatula1 is at location27. spatula2, fork1, sink1, and spoon2 are at location6. cup2, lettuce1, potato1, cup1, potato2, and apple1 are at location10. glassbottle2 and bowl1 are at location14. lightswitch1 is at location25. chair1 is at location23. stoveknob4 is at location7. saltshaker3 is at location26. cellphone2 and peppershaker2 are at location20. saltshaker1 is at location16. window2 is at location28. vase1 is at location17. chair2 is at location1. pot1 is at location5. agent agent1 is at location location31. The objects are in/on receptacle as follows. papertowelroll1 and vase2 are on shelf2. bread1, cellphone3, mug1, statue1, creditcard1, lettuce2, spoon1, and creditcard2 are on countertop1. dishsponge1 is in drawer2. spoon2, fork1, and spatula2 are in sinkbasin1. cellphone1, peppershaker1, and pan1 are on countertop2. pan1 is on stoveburner4. soapbottle1, spoon3, fork2, glassbottle3, knife1, lettuce3, butterknife1, houseplant1, tomato1, and saltshaker2 are on countertop3. vase1 is on shelf1. pot1 is on stoveburner1. potato1, cup1, lettuce1, apple1, potato2, and cup2 are in fridge1. pan1 is on stoveburner2. glassbottle2 and bowl1 are in cabinet4. egg2, apple2, and egg1 are in garbagecan1. plate1 is in cabinet6. pot1 is on stoveburner3. saltshaker3 is on shelf3. glassbottle1 is in cabinet1. peppershaker2 and cellphone2 are in drawer1. saltshaker1 is in cabinet3. spatula1 is in drawer3. cabinet5, fridge1, drawer2, cabinet2, drawer1, drawer3, cabinet1, and microwave1 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype.", "question": "Given the plan: \"agent agent1 goes to receptacle garbagecan1 from the current location location31 to the next location location8, agent agent1 picks up object egg2 from receptacle garbagecan1 that is at location location8, agent agent1 goes to receptacle stoveburner3 from the current location location8 to the next location location5, agent agent1 goes to receptacle garbagecan1 from the current location location5 to the next location location8, agent agent1 goes to receptacle stoveburner3 from the current location location8 to the next location location5, agent agent1 goes to receptacle garbagecan1 from the current location location5 to the next location location8, agent agent1 goes to receptacle microwave1 from the current location location8 to the next location location24, agent agent1 heats up object egg2 with a microwave microwave1 at location location24, agent agent1 goes to receptacle garbagecan1 from the current location location24 to the next location location8, agent agent1 puts down an object egg2 of type eggtype in a receptacle garbagecan1 of type garbagecantype that is at location location8, validate that object egg2 of type eggtype is hot and in receptacle garbagecan1 of type garbagecantype\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. agent agent1 goes to receptacle garbagecan1 from the current location location31 to the next location location8 and agent agent1 picks up object egg2 from receptacle garbagecan1 that is at location location8. B. agent agent1 goes to receptacle microwave1 from the current location location8 to the next location location24 and agent agent1 heats up object egg2 with a microwave microwave1 at location location24. C. agent agent1 goes to receptacle stoveburner3 from the current location location8 to the next location location5 and agent agent1 goes to receptacle garbagecan1 from the current location location5 to the next location location8. D. agent agent1 goes to receptacle garbagecan1 from the current location location5 to the next location location8 and agent agent1 goes to receptacle microwave1 from the current location location8 to the next location location24.", "choices": {"label": ["A", "B", "C", "D"], "text": ["agent agent1 goes to receptacle garbagecan1 from the current location location31 to the next location location8 and agent agent1 picks up object egg2 from receptacle garbagecan1 that is at location location8", "agent agent1 goes to receptacle microwave1 from the current location location8 to the next location location24 and agent agent1 heats up object egg2 with a microwave microwave1 at location location24", "agent agent1 goes to receptacle stoveburner3 from the current location location8 to the next location location5 and agent agent1 goes to receptacle garbagecan1 from the current location location5 to the next location location8", "agent agent1 goes to receptacle garbagecan1 from the current location location5 to the next location location8 and agent agent1 goes to receptacle microwave1 from the current location location8 to the next location location24"]}, "query": "Given the plan: \"agent agent1 goes to receptacle garbagecan1 from the current location location31 to the next location location8, agent agent1 picks up object egg2 from receptacle garbagecan1 that is at location location8, agent agent1 goes to receptacle stoveburner3 from the current location location8 to the next location location5, agent agent1 goes to receptacle garbagecan1 from the current location location5 to the next location location8, agent agent1 goes to receptacle stoveburner3 from the current location location8 to the next location location5, agent agent1 goes to receptacle garbagecan1 from the current location location5 to the next location location8, agent agent1 goes to receptacle microwave1 from the current location location8 to the next location location24, agent agent1 heats up object egg2 with a microwave microwave1 at location location24, agent agent1 goes to receptacle garbagecan1 from the current location location24 to the next location location8, agent agent1 puts down an object egg2 of type eggtype in a receptacle garbagecan1 of type garbagecantype that is at location location8, validate that object egg2 of type eggtype is hot and in receptacle garbagecan1 of type garbagecantype\"; which pair of consecutive actions can be removed from this plan?", "answer": "C"}
{"id": -8873810010045966448, "group": "action_justification_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet5 is at location13. stoveburner2 and stoveburner4 are at location21. shelf3 is at location26. drawer2 is at location15. cabinet3 is at location16. sinkbasin1 is at location6. drawer3 is at location27. microwave1 is at location24. coffeemachine1 and countertop2 are at location11. cabinet2 is at location22. fridge1 is at location10. garbagecan1 is at location8. countertop1 is at location2. cabinet1 is at location19. shelf2 is at location29. drawer1 is at location20. stoveburner1 and stoveburner3 are at location5. shelf1 is at location17. cabinet4 is at location14. countertop3 is at location3. toaster1 is at location9. cabinet6 is at location4.  Currently, the objects are at locations as follows. cellphone1, lettuce1, plate1, apple1, knife1, lettuce2, peppershaker1, and cellphone2 are at location2. spoon2, knife2, houseplant1, glassbottle1, cellphone3, plate3, papertowelroll1, mug2, butterknife1, bread1, spatula1, and creditcard1 are at location3. window1 is at location30. apple2, glassbottle3, and potato3 are at location8. saltshaker1 is at location15. stoveknob1 is at location18. soapbottle2 and statue1 are at location26. fork1 and dishsponge1 are at location27. pan1 and spoon1 are at location11. dishsponge2 and vase1 are at location13. bowl1, egg2, egg1, potato1, tomato1, plate2, and mug1 are at location10. stoveknob2 and stoveknob3 are at location12. spatula2, potato2, glassbottle2, sink1, and spatula3 are at location6. chair1 is at location23. lightswitch1 is at location25. cup1 is at location24. vase2 is at location17. peppershaker3 is at location16. stoveknob4 is at location7. bowl2 is at location29. peppershaker2 is at location20. soapbottle1 is at location4. window2 is at location28. chair2 is at location1. pot1 is at location5. agent agent1 is at location location31. The objects are in/on receptacle as follows. lettuce1, knife1, cellphone2, peppershaker1, apple1, lettuce2, plate1, and cellphone1 are on countertop1. saltshaker1 is in drawer2. mug1, potato1, plate2, tomato1, bowl1, egg1, and egg2 are in fridge1. mug2, spatula1, glassbottle1, creditcard1, plate3, cellphone3, butterknife1, houseplant1, knife2, bread1, spoon2, and papertowelroll1 are on countertop3. pan1 is on stoveburner4. pot1 is on stoveburner1. vase2 is on shelf1. pan1 is on stoveburner2. glassbottle2, spatula3, potato2, and spatula2 are in sinkbasin1. statue1 and soapbottle2 are on shelf3. potato3, glassbottle3, and apple2 are in garbagecan1. peppershaker3 is in cabinet3. dishsponge2 and vase1 are in cabinet5. dishsponge1 and fork1 are in drawer3. soapbottle1 is in cabinet6. cellphone2 is on plate1. cup1 is in microwave1. pot1 is on stoveburner3. bowl2 is on shelf2. spoon1 and pan1 are on countertop2. peppershaker2 is in drawer1. cabinet5, fridge1, drawer2, cabinet2, drawer1, drawer3, cabinet1, and microwave1 are closed. mug1 is cool. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type mugtype is hot and is in a receptacle of type coffeemachinetype.", "question": "Given the plan: \"agent agent1 navigates from the current position location31 to the next position location11 that has a receptacle countertop2, agent agent1 navigates from the current position location11 to the next position location3 that has a receptacle countertop3, agent agent1 navigates from the current position location3 to the next position location22 that has a receptacle cabinet2, agent agent1 navigates from the current position location22 to the next position location3 that has a receptacle countertop3, agent agent1 picks up object mug2 from receptacle countertop3 that is at location location3, agent agent1 navigates from the current position location3 to the next position location24 that has a receptacle microwave1, agent agent1 heats up object mug2 with a microwave microwave1 that is in location location24, agent agent1 navigates from the current position location24 to the next position location11 that has a receptacle countertop2, agent agent1 puts down an object mug2 with type mugtype in a receptacle coffeemachine1 with type coffeemachinetype at location location11, validate that object mug2 of type mugtype is hot and in receptacle coffeemachine1 of type coffeemachinetype\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. agent agent1 navigates from the current position location24 to the next position location11 that has a receptacle countertop2 and agent agent1 puts down an object mug2 with type mugtype in a receptacle coffeemachine1 with type coffeemachinetype at location location11. B. agent agent1 navigates from the current position location22 to the next position location3 that has a receptacle countertop3 and agent agent1 picks up object mug2 from receptacle countertop3 that is at location location3. C. agent agent1 heats up object mug2 with a microwave microwave1 that is in location location24 and agent agent1 navigates from the current position location24 to the next position location11 that has a receptacle countertop2. D. agent agent1 navigates from the current position location3 to the next position location22 that has a receptacle cabinet2 and agent agent1 navigates from the current position location22 to the next position location3 that has a receptacle countertop3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["agent agent1 navigates from the current position location24 to the next position location11 that has a receptacle countertop2 and agent agent1 puts down an object mug2 with type mugtype in a receptacle coffeemachine1 with type coffeemachinetype at location location11", "agent agent1 navigates from the current position location22 to the next position location3 that has a receptacle countertop3 and agent agent1 picks up object mug2 from receptacle countertop3 that is at location location3", "agent agent1 heats up object mug2 with a microwave microwave1 that is in location location24 and agent agent1 navigates from the current position location24 to the next position location11 that has a receptacle countertop2", "agent agent1 navigates from the current position location3 to the next position location22 that has a receptacle cabinet2 and agent agent1 navigates from the current position location22 to the next position location3 that has a receptacle countertop3"]}, "query": "Given the plan: \"agent agent1 navigates from the current position location31 to the next position location11 that has a receptacle countertop2, agent agent1 navigates from the current position location11 to the next position location3 that has a receptacle countertop3, agent agent1 navigates from the current position location3 to the next position location22 that has a receptacle cabinet2, agent agent1 navigates from the current position location22 to the next position location3 that has a receptacle countertop3, agent agent1 picks up object mug2 from receptacle countertop3 that is at location location3, agent agent1 navigates from the current position location3 to the next position location24 that has a receptacle microwave1, agent agent1 heats up object mug2 with a microwave microwave1 that is in location location24, agent agent1 navigates from the current position location24 to the next position location11 that has a receptacle countertop2, agent agent1 puts down an object mug2 with type mugtype in a receptacle coffeemachine1 with type coffeemachinetype at location location11, validate that object mug2 of type mugtype is hot and in receptacle coffeemachine1 of type coffeemachinetype\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
{"id": -5385658100598768250, "group": "action_justification_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet5 is at location13. stoveburner2 and stoveburner4 are at location21. shelf3 is at location26. drawer2 is at location15. cabinet3 is at location16. sinkbasin1 is at location6. drawer3 is at location27. microwave1 is at location24. coffeemachine1 and countertop2 are at location11. cabinet2 is at location22. fridge1 is at location10. garbagecan1 is at location8. countertop1 is at location2. cabinet1 is at location19. shelf2 is at location29. drawer1 is at location20. stoveburner1 and stoveburner3 are at location5. shelf1 is at location17. cabinet4 is at location14. countertop3 is at location3. toaster1 is at location9. cabinet6 is at location4.  Currently, the objects are at locations as follows. cellphone1, lettuce1, plate1, apple1, knife1, lettuce2, peppershaker1, and cellphone2 are at location2. spoon2, knife2, houseplant1, glassbottle1, cellphone3, plate3, papertowelroll1, mug2, butterknife1, bread1, spatula1, and creditcard1 are at location3. window1 is at location30. apple2, glassbottle3, and potato3 are at location8. saltshaker1 is at location15. stoveknob1 is at location18. soapbottle2 and statue1 are at location26. fork1 and dishsponge1 are at location27. pan1 and spoon1 are at location11. dishsponge2 and vase1 are at location13. bowl1, egg2, egg1, potato1, tomato1, plate2, and mug1 are at location10. stoveknob2 and stoveknob3 are at location12. spatula2, potato2, glassbottle2, sink1, and spatula3 are at location6. chair1 is at location23. lightswitch1 is at location25. cup1 is at location24. vase2 is at location17. peppershaker3 is at location16. stoveknob4 is at location7. bowl2 is at location29. peppershaker2 is at location20. soapbottle1 is at location4. window2 is at location28. chair2 is at location1. pot1 is at location5. agent agent1 is at location location31. The objects are in/on receptacle as follows. lettuce1, knife1, cellphone2, peppershaker1, apple1, lettuce2, plate1, and cellphone1 are on countertop1. saltshaker1 is in drawer2. mug1, potato1, plate2, tomato1, bowl1, egg1, and egg2 are in fridge1. mug2, spatula1, glassbottle1, creditcard1, plate3, cellphone3, butterknife1, houseplant1, knife2, bread1, spoon2, and papertowelroll1 are on countertop3. pan1 is on stoveburner4. pot1 is on stoveburner1. vase2 is on shelf1. pan1 is on stoveburner2. glassbottle2, spatula3, potato2, and spatula2 are in sinkbasin1. statue1 and soapbottle2 are on shelf3. potato3, glassbottle3, and apple2 are in garbagecan1. peppershaker3 is in cabinet3. dishsponge2 and vase1 are in cabinet5. dishsponge1 and fork1 are in drawer3. soapbottle1 is in cabinet6. cellphone2 is on plate1. cup1 is in microwave1. pot1 is on stoveburner3. bowl2 is on shelf2. spoon1 and pan1 are on countertop2. peppershaker2 is in drawer1. cabinet5, fridge1, drawer2, cabinet2, drawer1, drawer3, cabinet1, and microwave1 are closed. mug1 is cool. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type mugtype is hot and is in a receptacle of type coffeemachinetype.", "question": "Given the plan: \"agent agent1 moves from the current position location31 to the next position location11 that has the receptacle countertop2, agent agent1 moves from the current position location11 to the next position location3 that has the receptacle countertop3, agent agent1 moves from the current position location3 to the next position location22 that has the receptacle cabinet2, agent agent1 moves from the current position location22 to the next position location3 that has the receptacle countertop3, agent agent1 grasps object mug2 from receptacle countertop3 while at location location3, agent agent1 moves from the current position location3 to the next position location24 that has the receptacle microwave1, agent agent1 warms up object mug2 with a microwave microwave1 at location location24, agent agent1 moves from the current position location24 to the next position location11 that has the receptacle coffeemachine1, agent agent1 puts down an object mug2 of type mugtype in a receptacle coffeemachine1 of type coffeemachinetype that is at location location11, ensure that object mug2 of type mugtype is hot and in receptacle coffeemachine1 of type coffeemachinetype\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. agent agent1 moves from the current position location11 to the next position location3 that has the receptacle countertop3 and agent agent1 moves from the current position location3 to the next position location22 that has the receptacle cabinet2. B. agent agent1 moves from the current position location3 to the next position location22 that has the receptacle cabinet2 and agent agent1 moves from the current position location22 to the next position location3 that has the receptacle countertop3. C. agent agent1 puts down an object mug2 of type mugtype in a receptacle coffeemachine1 of type coffeemachinetype that is at location location11 and ensure that object mug2 of type mugtype is hot and in receptacle coffeemachine1 of type coffeemachinetype. D. agent agent1 moves from the current position location3 to the next position location24 that has the receptacle microwave1 and agent agent1 warms up object mug2 with a microwave microwave1 at location location24.", "choices": {"label": ["A", "B", "C", "D"], "text": ["agent agent1 moves from the current position location11 to the next position location3 that has the receptacle countertop3 and agent agent1 moves from the current position location3 to the next position location22 that has the receptacle cabinet2", "agent agent1 moves from the current position location3 to the next position location22 that has the receptacle cabinet2 and agent agent1 moves from the current position location22 to the next position location3 that has the receptacle countertop3", "agent agent1 puts down an object mug2 of type mugtype in a receptacle coffeemachine1 of type coffeemachinetype that is at location location11 and ensure that object mug2 of type mugtype is hot and in receptacle coffeemachine1 of type coffeemachinetype", "agent agent1 moves from the current position location3 to the next position location24 that has the receptacle microwave1 and agent agent1 warms up object mug2 with a microwave microwave1 at location location24"]}, "query": "Given the plan: \"agent agent1 moves from the current position location31 to the next position location11 that has the receptacle countertop2, agent agent1 moves from the current position location11 to the next position location3 that has the receptacle countertop3, agent agent1 moves from the current position location3 to the next position location22 that has the receptacle cabinet2, agent agent1 moves from the current position location22 to the next position location3 that has the receptacle countertop3, agent agent1 grasps object mug2 from receptacle countertop3 while at location location3, agent agent1 moves from the current position location3 to the next position location24 that has the receptacle microwave1, agent agent1 warms up object mug2 with a microwave microwave1 at location location24, agent agent1 moves from the current position location24 to the next position location11 that has the receptacle coffeemachine1, agent agent1 puts down an object mug2 of type mugtype in a receptacle coffeemachine1 of type coffeemachinetype that is at location location11, ensure that object mug2 of type mugtype is hot and in receptacle coffeemachine1 of type coffeemachinetype\"; which pair of consecutive actions can be removed from this plan?", "answer": "B"}
{"id": 5958583551022429696, "group": "action_justification_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 16 object types: 1 candle, 2 cloths, 3 faucets, 2 handtowels, 1 lightswitch, 1 mirror, 1 plunger, 1 scrubbrush, 1 showerdoor, 1 showerglass, 2 sinks, 2 soapbars, 2 soapbottles, 3 spraybottles, 2 toiletpapers, 1 towel, 8 receptacle types: 4 cabinets, 1 countertop, 1 garbagecan, 2 handtowelholders, 2 sinkbasins, 1 toiletpaperhanger, 1 toilet, 1 towelholder, and 19 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet1 is at location4. toilet1 is at location7. cabinet2 is at location11. countertop1 is at location3. cabinet3 is at location8. sinkbasin1 is at location16. towelholder1 is at location5. toiletpaperhanger1 is at location10. sinkbasin2 is at location6. handtowelholder2 is at location17. handtowelholder1 is at location18. garbagecan1 is at location2. cabinet4 is at location15.  Currently, the objects are at locations as follows. candle1, cloth1, and soapbar2 are at location3. soapbar1 and toiletpaper1 are at location7. handtowel1 is at location18. soapbottle2 and spraybottle1 are at location4. handtowel2 is at location17. mirror1 is at location9. showerdoor1 is at location1. towel1 and showerglass1 are at location5. sink1 is at location12. lightswitch1 is at location13. soapbottle1 and spraybottle2 are at location15. scrubbrush1 and plunger1 are at location10. spraybottle3 is at location2. cloth2 is at location11. sink2 is at location14. toiletpaper2 is at location8. agent agent1 is at location location19. The objects are in/on receptacle as follows. soapbar1 and toiletpaper1 are in toilet1. handtowel1 is on handtowelholder1. soapbottle2 and spraybottle1 are in cabinet1. cloth1, candle1, and soapbar2 are on countertop1. handtowel2 is on handtowelholder2. spraybottle3 is in garbagecan1. spraybottle2 and soapbottle1 are in cabinet4. towel1 is on towelholder1. cloth2 is in cabinet2. toiletpaper2 is in cabinet3. cabinet2, cabinet4, cabinet3, and cabinet1 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype.", "question": "Given the plan: \"agent agent1 navigates from the current position location19 to the next position location3 that has a receptacle countertop1, agent agent1 navigates from the current position location3 to the next position location17 that has a receptacle handtowelholder2, agent agent1 navigates from the current position location17 to the next position location3 that has a receptacle countertop1, agent agent1 picks up object soapbar2 from receptacle countertop1 that is at location location3, agent agent1 navigates from the current position location3 to the next position location6 that has a receptacle sinkbasin2, agent agent1 cleans an object soapbar2 in a sink sinkbasin2 that is in location location6, agent agent1 navigates from the current position location6 to the next position location8 that has a receptacle cabinet3, agent agent1 navigates from the current position location8 to the next position location15 that has a receptacle cabinet4, agent agent1 opens receptacle cabinet4 while at location location15, agent agent1 places object soapbar2 with type soapbartype in a receptacle cabinet4 with type cabinettype at location location15, ensure that object soapbar2 of type soapbartype is clean and in receptacle cabinet4 of type cabinettype\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. agent agent1 opens receptacle cabinet4 while at location location15 and agent agent1 places object soapbar2 with type soapbartype in a receptacle cabinet4 with type cabinettype at location location15. B. agent agent1 navigates from the current position location3 to the next position location17 that has a receptacle handtowelholder2 and agent agent1 navigates from the current position location17 to the next position location3 that has a receptacle countertop1. C. agent agent1 places object soapbar2 with type soapbartype in a receptacle cabinet4 with type cabinettype at location location15 and ensure that object soapbar2 of type soapbartype is clean and in receptacle cabinet4 of type cabinettype. D. agent agent1 picks up object soapbar2 from receptacle countertop1 that is at location location3 and agent agent1 navigates from the current position location3 to the next position location6 that has a receptacle sinkbasin2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["agent agent1 opens receptacle cabinet4 while at location location15 and agent agent1 places object soapbar2 with type soapbartype in a receptacle cabinet4 with type cabinettype at location location15", "agent agent1 navigates from the current position location3 to the next position location17 that has a receptacle handtowelholder2 and agent agent1 navigates from the current position location17 to the next position location3 that has a receptacle countertop1", "agent agent1 places object soapbar2 with type soapbartype in a receptacle cabinet4 with type cabinettype at location location15 and ensure that object soapbar2 of type soapbartype is clean and in receptacle cabinet4 of type cabinettype", "agent agent1 picks up object soapbar2 from receptacle countertop1 that is at location location3 and agent agent1 navigates from the current position location3 to the next position location6 that has a receptacle sinkbasin2"]}, "query": "Given the plan: \"agent agent1 navigates from the current position location19 to the next position location3 that has a receptacle countertop1, agent agent1 navigates from the current position location3 to the next position location17 that has a receptacle handtowelholder2, agent agent1 navigates from the current position location17 to the next position location3 that has a receptacle countertop1, agent agent1 picks up object soapbar2 from receptacle countertop1 that is at location location3, agent agent1 navigates from the current position location3 to the next position location6 that has a receptacle sinkbasin2, agent agent1 cleans an object soapbar2 in a sink sinkbasin2 that is in location location6, agent agent1 navigates from the current position location6 to the next position location8 that has a receptacle cabinet3, agent agent1 navigates from the current position location8 to the next position location15 that has a receptacle cabinet4, agent agent1 opens receptacle cabinet4 while at location location15, agent agent1 places object soapbar2 with type soapbartype in a receptacle cabinet4 with type cabinettype at location location15, ensure that object soapbar2 of type soapbartype is clean and in receptacle cabinet4 of type cabinettype\"; which pair of consecutive actions can be removed from this plan?", "answer": "B"}
{"id": 2459156398927500782, "group": "action_justification_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 3 apples, 1 bowl, 1 bread, 2 butterknives, 3 cellphones, 2 chairs, 3 creditcards, 3 cups, 3 dishsponges, 2 eggs, 1 faucet, 2 forks, 2 glassbottles, 1 houseplant, 1 knife, 1 lettuce, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 2 peppershakers, 2 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 3 soapbottles, 3 spatulas, 2 spoons, 2 statues, 4 stoveknobs, 3 tomatoes, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 32 locations all numbered consecutively.  The receptacles are at locations as follows. fridge1 is at location11. cabinet2 is at location23. drawer3 is at location28. cabinet5 is at location14. stoveburner4 and stoveburner2 are at location22. cabinet6 is at location5. sinkbasin1 is at location7. shelf2 is at location30. stoveburner1 and stoveburner3 are at location6. drawer2 is at location16. cabinet3 is at location17. countertop1 is at location3. microwave1 is at location25. countertop3 is at location4. garbagecan1 is at location9. coffeemachine1 and countertop2 are at location12. shelf1 is at location18. shelf3 is at location27. cabinet1 is at location20. drawer1 is at location21. cabinet4 is at location15. toaster1 is at location10.  Currently, the objects are at locations as follows. apple1, statue1, cellphone1, spatula2, bread1, apple2, apple3, dishsponge3, and creditcard1 are at location3. pot1 is at location22. spatula1 is at location28. fork1, cup3, tomato3, sink1, potato3, and egg2 are at location7. stoveknob4 is at location8. potato2, egg1, cup2, potato1, tomato2, tomato1, cup1, and lettuce1 are at location11. window1 is at location31. vase1 and soapbottle1 are at location5. peppershaker2, butterknife1, cellphone3, butterknife2, statue2, spatula3, knife1, spoon2, fork2, houseplant1, and soapbottle3 are at location4. stoveknob2 and stoveknob3 are at location13. glassbottle2, bowl1, creditcard2, and mug2 are at location30. plate2 and plate1 are at location14. chair1 is at location24. soapbottle2, papertowelroll1, and glassbottle1 are at location9. pan1 is at location6. spoon1 and dishsponge1 are at location16. vase2 is at location18. window2 is at location29. saltshaker1 and creditcard3 are at location27. peppershaker1 is at location20. chair2 is at location1. mug1 is at location25. dishsponge2 is at location2. lightswitch1 is at location26. stoveknob1 is at location19. cellphone2 is at location21. agent agent1 is at location location32. The objects are in/on receptacle as follows. spoon1 and dishsponge1 are in drawer2. creditcard3 and saltshaker1 are on shelf3. pot1 is on stoveburner4. peppershaker1 is in cabinet1. bread1, apple2, statue1, spatula2, dishsponge3, apple1, creditcard1, apple3, and cellphone1 are on countertop1. bowl1, mug2, creditcard2, and glassbottle2 are on shelf2. peppershaker2, spatula3, fork2, knife1, cellphone3, butterknife2, butterknife1, houseplant1, statue2, soapbottle3, and spoon2 are on countertop3. potato1, cup1, tomato2, tomato1, lettuce1, egg1, potato2, and cup2 are in fridge1. vase2 is on shelf1. dishsponge2 is on plate1. cup3, egg2, fork1, tomato3, and potato3 are in sinkbasin1. soapbottle2, glassbottle1, and papertowelroll1 are in garbagecan1. vase1 and soapbottle1 are in cabinet6. dishsponge2, plate2, and plate1 are in cabinet5. pot1 is on stoveburner2. pan1 is on stoveburner3. pan1 is on stoveburner1. mug1 is in microwave1. spatula1 is in drawer3. cellphone2 is in drawer1. cabinet5, fridge1, drawer2, cabinet2, drawer1, drawer3, cabinet1, and microwave1 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type saltshakertype is in a receptacle of type cabinettype.", "question": "Given the plan: \"agent agent1 goes to receptacle stoveburner3 from the current location location32 to the next location location6, agent agent1 goes to receptacle shelf3 from the current location location6 to the next location location27, agent agent1 goes to receptacle shelf1 from the current location location27 to the next location location18, agent agent1 goes to receptacle shelf3 from the current location location18 to the next location location27, agent agent1 collects object saltshaker1 from receptacle shelf3 while at location location27, agent agent1 goes to receptacle cabinet3 from the current location location27 to the next location location17, agent agent1 places object saltshaker1 of type saltshakertype in a receptacle cabinet3 of type cabinettype while at location location17, check that object saltshaker1 of type saltshakertype is in a receptacle cabinet3 of type cabinettype\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. agent agent1 goes to receptacle cabinet3 from the current location location27 to the next location location17 and agent agent1 places object saltshaker1 of type saltshakertype in a receptacle cabinet3 of type cabinettype while at location location17. B. agent agent1 goes to receptacle stoveburner3 from the current location location32 to the next location location6 and agent agent1 goes to receptacle shelf3 from the current location location6 to the next location location27. C. agent agent1 goes to receptacle shelf3 from the current location location18 to the next location location27 and agent agent1 collects object saltshaker1 from receptacle shelf3 while at location location27. D. agent agent1 goes to receptacle shelf1 from the current location location27 to the next location location18 and agent agent1 goes to receptacle shelf3 from the current location location18 to the next location location27.", "choices": {"label": ["A", "B", "C", "D"], "text": ["agent agent1 goes to receptacle cabinet3 from the current location location27 to the next location location17 and agent agent1 places object saltshaker1 of type saltshakertype in a receptacle cabinet3 of type cabinettype while at location location17", "agent agent1 goes to receptacle stoveburner3 from the current location location32 to the next location location6 and agent agent1 goes to receptacle shelf3 from the current location location6 to the next location location27", "agent agent1 goes to receptacle shelf3 from the current location location18 to the next location location27 and agent agent1 collects object saltshaker1 from receptacle shelf3 while at location location27", "agent agent1 goes to receptacle shelf1 from the current location location27 to the next location location18 and agent agent1 goes to receptacle shelf3 from the current location location18 to the next location location27"]}, "query": "Given the plan: \"agent agent1 goes to receptacle stoveburner3 from the current location location32 to the next location location6, agent agent1 goes to receptacle shelf3 from the current location location6 to the next location location27, agent agent1 goes to receptacle shelf1 from the current location location27 to the next location location18, agent agent1 goes to receptacle shelf3 from the current location location18 to the next location location27, agent agent1 collects object saltshaker1 from receptacle shelf3 while at location location27, agent agent1 goes to receptacle cabinet3 from the current location location27 to the next location location17, agent agent1 places object saltshaker1 of type saltshakertype in a receptacle cabinet3 of type cabinettype while at location location17, check that object saltshaker1 of type saltshakertype is in a receptacle cabinet3 of type cabinettype\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
{"id": -*******************, "group": "action_justification_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 3 apples, 1 bowl, 1 bread, 2 butterknives, 3 cellphones, 2 chairs, 3 creditcards, 3 cups, 3 dishsponges, 2 eggs, 1 faucet, 2 forks, 2 glassbottles, 1 houseplant, 1 knife, 1 lettuce, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 2 peppershakers, 2 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 3 soapbottles, 3 spatulas, 2 spoons, 2 statues, 4 stoveknobs, 3 tomatoes, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 32 locations all numbered consecutively.  The receptacles are at locations as follows. fridge1 is at location11. cabinet2 is at location23. drawer3 is at location28. cabinet5 is at location14. stoveburner4 and stoveburner2 are at location22. cabinet6 is at location5. sinkbasin1 is at location7. shelf2 is at location30. stoveburner1 and stoveburner3 are at location6. drawer2 is at location16. cabinet3 is at location17. countertop1 is at location3. microwave1 is at location25. countertop3 is at location4. garbagecan1 is at location9. coffeemachine1 and countertop2 are at location12. shelf1 is at location18. shelf3 is at location27. cabinet1 is at location20. drawer1 is at location21. cabinet4 is at location15. toaster1 is at location10.  Currently, the objects are at locations as follows. apple1, statue1, cellphone1, spatula2, bread1, apple2, apple3, dishsponge3, and creditcard1 are at location3. pot1 is at location22. spatula1 is at location28. fork1, cup3, tomato3, sink1, potato3, and egg2 are at location7. stoveknob4 is at location8. potato2, egg1, cup2, potato1, tomato2, tomato1, cup1, and lettuce1 are at location11. window1 is at location31. vase1 and soapbottle1 are at location5. peppershaker2, butterknife1, cellphone3, butterknife2, statue2, spatula3, knife1, spoon2, fork2, houseplant1, and soapbottle3 are at location4. stoveknob2 and stoveknob3 are at location13. glassbottle2, bowl1, creditcard2, and mug2 are at location30. plate2 and plate1 are at location14. chair1 is at location24. soapbottle2, papertowelroll1, and glassbottle1 are at location9. pan1 is at location6. spoon1 and dishsponge1 are at location16. vase2 is at location18. window2 is at location29. saltshaker1 and creditcard3 are at location27. peppershaker1 is at location20. chair2 is at location1. mug1 is at location25. dishsponge2 is at location2. lightswitch1 is at location26. stoveknob1 is at location19. cellphone2 is at location21. agent agent1 is at location location32. The objects are in/on receptacle as follows. spoon1 and dishsponge1 are in drawer2. creditcard3 and saltshaker1 are on shelf3. pot1 is on stoveburner4. peppershaker1 is in cabinet1. bread1, apple2, statue1, spatula2, dishsponge3, apple1, creditcard1, apple3, and cellphone1 are on countertop1. bowl1, mug2, creditcard2, and glassbottle2 are on shelf2. peppershaker2, spatula3, fork2, knife1, cellphone3, butterknife2, butterknife1, houseplant1, statue2, soapbottle3, and spoon2 are on countertop3. potato1, cup1, tomato2, tomato1, lettuce1, egg1, potato2, and cup2 are in fridge1. vase2 is on shelf1. dishsponge2 is on plate1. cup3, egg2, fork1, tomato3, and potato3 are in sinkbasin1. soapbottle2, glassbottle1, and papertowelroll1 are in garbagecan1. vase1 and soapbottle1 are in cabinet6. dishsponge2, plate2, and plate1 are in cabinet5. pot1 is on stoveburner2. pan1 is on stoveburner3. pan1 is on stoveburner1. mug1 is in microwave1. spatula1 is in drawer3. cellphone2 is in drawer1. cabinet5, fridge1, drawer2, cabinet2, drawer1, drawer3, cabinet1, and microwave1 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type saltshakertype is in a receptacle of type cabinettype.", "question": "Given the plan: \"agent agent1 travels from the current position location32 to the next position location22 with receptacle stoveburner2, agent agent1 travels from the current position location22 to the next position location9 with receptacle garbagecan1, agent agent1 travels from the current position location9 to the next position location27 with receptacle shelf3, agent agent1 travels from the current position location27 to the next position location18 with receptacle shelf1, agent agent1 travels from the current position location18 to the next position location27 with receptacle shelf3, agent agent1 collects object saltshaker1 from receptacle shelf3 while at location location27, agent agent1 travels from the current position location27 to the next position location17 with receptacle cabinet3, agent agent1 puts down an object saltshaker1 with type saltshakertype in a receptacle cabinet3 with type cabinettype at location location17, validate that object saltshaker1 of type saltshakertype is in a receptacle cabinet3 of type cabinettype\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. agent agent1 puts down an object saltshaker1 with type saltshakertype in a receptacle cabinet3 with type cabinettype at location location17 and validate that object saltshaker1 of type saltshakertype is in a receptacle cabinet3 of type cabinettype. B. agent agent1 travels from the current position location22 to the next position location9 with receptacle garbagecan1 and agent agent1 travels from the current position location9 to the next position location27 with receptacle shelf3. C. agent agent1 collects object saltshaker1 from receptacle shelf3 while at location location27 and agent agent1 travels from the current position location27 to the next position location17 with receptacle cabinet3. D. agent agent1 travels from the current position location27 to the next position location18 with receptacle shelf1 and agent agent1 travels from the current position location18 to the next position location27 with receptacle shelf3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["agent agent1 puts down an object saltshaker1 with type saltshakertype in a receptacle cabinet3 with type cabinettype at location location17 and validate that object saltshaker1 of type saltshakertype is in a receptacle cabinet3 of type cabinettype", "agent agent1 travels from the current position location22 to the next position location9 with receptacle garbagecan1 and agent agent1 travels from the current position location9 to the next position location27 with receptacle shelf3", "agent agent1 collects object saltshaker1 from receptacle shelf3 while at location location27 and agent agent1 travels from the current position location27 to the next position location17 with receptacle cabinet3", "agent agent1 travels from the current position location27 to the next position location18 with receptacle shelf1 and agent agent1 travels from the current position location18 to the next position location27 with receptacle shelf3"]}, "query": "Given the plan: \"agent agent1 travels from the current position location32 to the next position location22 with receptacle stoveburner2, agent agent1 travels from the current position location22 to the next position location9 with receptacle garbagecan1, agent agent1 travels from the current position location9 to the next position location27 with receptacle shelf3, agent agent1 travels from the current position location27 to the next position location18 with receptacle shelf1, agent agent1 travels from the current position location18 to the next position location27 with receptacle shelf3, agent agent1 collects object saltshaker1 from receptacle shelf3 while at location location27, agent agent1 travels from the current position location27 to the next position location17 with receptacle cabinet3, agent agent1 puts down an object saltshaker1 with type saltshakertype in a receptacle cabinet3 with type cabinettype at location location17, validate that object saltshaker1 of type saltshakertype is in a receptacle cabinet3 of type cabinettype\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
{"id": -7244621928313375497, "group": "action_justification_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 3 apples, 1 bowl, 1 bread, 2 butterknives, 3 cellphones, 2 chairs, 3 creditcards, 3 cups, 3 dishsponges, 2 eggs, 1 faucet, 2 forks, 2 glassbottles, 1 houseplant, 1 knife, 1 lettuce, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 2 peppershakers, 2 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 3 soapbottles, 3 spatulas, 2 spoons, 2 statues, 4 stoveknobs, 3 tomatoes, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 32 locations all numbered consecutively.  The receptacles are at locations as follows. fridge1 is at location11. cabinet2 is at location23. drawer3 is at location28. cabinet5 is at location14. stoveburner4 and stoveburner2 are at location22. cabinet6 is at location5. sinkbasin1 is at location7. shelf2 is at location30. stoveburner1 and stoveburner3 are at location6. drawer2 is at location16. cabinet3 is at location17. countertop1 is at location3. microwave1 is at location25. countertop3 is at location4. garbagecan1 is at location9. coffeemachine1 and countertop2 are at location12. shelf1 is at location18. shelf3 is at location27. cabinet1 is at location20. drawer1 is at location21. cabinet4 is at location15. toaster1 is at location10.  Currently, the objects are at locations as follows. apple1, statue1, cellphone1, spatula2, bread1, apple2, apple3, dishsponge3, and creditcard1 are at location3. pot1 is at location22. spatula1 is at location28. fork1, cup3, tomato3, sink1, potato3, and egg2 are at location7. stoveknob4 is at location8. potato2, egg1, cup2, potato1, tomato2, tomato1, cup1, and lettuce1 are at location11. window1 is at location31. vase1 and soapbottle1 are at location5. peppershaker2, butterknife1, cellphone3, butterknife2, statue2, spatula3, knife1, spoon2, fork2, houseplant1, and soapbottle3 are at location4. stoveknob2 and stoveknob3 are at location13. glassbottle2, bowl1, creditcard2, and mug2 are at location30. plate2 and plate1 are at location14. chair1 is at location24. soapbottle2, papertowelroll1, and glassbottle1 are at location9. pan1 is at location6. spoon1 and dishsponge1 are at location16. vase2 is at location18. window2 is at location29. saltshaker1 and creditcard3 are at location27. peppershaker1 is at location20. chair2 is at location1. mug1 is at location25. dishsponge2 is at location2. lightswitch1 is at location26. stoveknob1 is at location19. cellphone2 is at location21. agent agent1 is at location location32. The objects are in/on receptacle as follows. spoon1 and dishsponge1 are in drawer2. creditcard3 and saltshaker1 are on shelf3. pot1 is on stoveburner4. peppershaker1 is in cabinet1. bread1, apple2, statue1, spatula2, dishsponge3, apple1, creditcard1, apple3, and cellphone1 are on countertop1. bowl1, mug2, creditcard2, and glassbottle2 are on shelf2. peppershaker2, spatula3, fork2, knife1, cellphone3, butterknife2, butterknife1, houseplant1, statue2, soapbottle3, and spoon2 are on countertop3. potato1, cup1, tomato2, tomato1, lettuce1, egg1, potato2, and cup2 are in fridge1. vase2 is on shelf1. dishsponge2 is on plate1. cup3, egg2, fork1, tomato3, and potato3 are in sinkbasin1. soapbottle2, glassbottle1, and papertowelroll1 are in garbagecan1. vase1 and soapbottle1 are in cabinet6. dishsponge2, plate2, and plate1 are in cabinet5. pot1 is on stoveburner2. pan1 is on stoveburner3. pan1 is on stoveburner1. mug1 is in microwave1. spatula1 is in drawer3. cellphone2 is in drawer1. cabinet5, fridge1, drawer2, cabinet2, drawer1, drawer3, cabinet1, and microwave1 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type saltshakertype is in a receptacle of type cabinettype.", "question": "Given the plan: \"agent agent1 moves from the current position location32 to the next position location11 that has the receptacle fridge1, agent agent1 moves from the current position location11 to the next position location27 that has the receptacle shelf3, agent agent1 moves from the current position location27 to the next position location18 that has the receptacle shelf1, agent agent1 moves from the current position location18 to the next position location27 that has the receptacle shelf3, agent agent1 picks up object saltshaker1 from receptacle shelf3 while at location location27, agent agent1 moves from the current position location27 to the next position location5 that has the receptacle cabinet6, agent agent1 puts down an object saltshaker1 with type saltshakertype in a receptacle cabinet6 with type cabinettype at location location5, check that object saltshaker1 of type saltshakertype is in a receptacle cabinet6 of type cabinettype\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. agent agent1 moves from the current position location27 to the next position location5 that has the receptacle cabinet6 and agent agent1 puts down an object saltshaker1 with type saltshakertype in a receptacle cabinet6 with type cabinettype at location location5. B. agent agent1 moves from the current position location11 to the next position location27 that has the receptacle shelf3 and agent agent1 moves from the current position location27 to the next position location18 that has the receptacle shelf1. C. agent agent1 moves from the current position location27 to the next position location18 that has the receptacle shelf1 and agent agent1 moves from the current position location18 to the next position location27 that has the receptacle shelf3. D. agent agent1 moves from the current position location18 to the next position location27 that has the receptacle shelf3 and agent agent1 picks up object saltshaker1 from receptacle shelf3 while at location location27.", "choices": {"label": ["A", "B", "C", "D"], "text": ["agent agent1 moves from the current position location27 to the next position location5 that has the receptacle cabinet6 and agent agent1 puts down an object saltshaker1 with type saltshakertype in a receptacle cabinet6 with type cabinettype at location location5", "agent agent1 moves from the current position location11 to the next position location27 that has the receptacle shelf3 and agent agent1 moves from the current position location27 to the next position location18 that has the receptacle shelf1", "agent agent1 moves from the current position location27 to the next position location18 that has the receptacle shelf1 and agent agent1 moves from the current position location18 to the next position location27 that has the receptacle shelf3", "agent agent1 moves from the current position location18 to the next position location27 that has the receptacle shelf3 and agent agent1 picks up object saltshaker1 from receptacle shelf3 while at location location27"]}, "query": "Given the plan: \"agent agent1 moves from the current position location32 to the next position location11 that has the receptacle fridge1, agent agent1 moves from the current position location11 to the next position location27 that has the receptacle shelf3, agent agent1 moves from the current position location27 to the next position location18 that has the receptacle shelf1, agent agent1 moves from the current position location18 to the next position location27 that has the receptacle shelf3, agent agent1 picks up object saltshaker1 from receptacle shelf3 while at location location27, agent agent1 moves from the current position location27 to the next position location5 that has the receptacle cabinet6, agent agent1 puts down an object saltshaker1 with type saltshakertype in a receptacle cabinet6 with type cabinettype at location location5, check that object saltshaker1 of type saltshakertype is in a receptacle cabinet6 of type cabinettype\"; which pair of consecutive actions can be removed from this plan?", "answer": "C"}
{"id": -3237350622045242080, "group": "action_justification_mcq", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. drawer3 is at location17. desk2 is at location10. shelf2 is at location25. shelf3 is at location11. desk1 is at location3. bed1 is at location13. shelf5 is at location22. drawer6 is at location1. garbagecan1 is at location2. safe1 is at location6. drawer5 and drawer4 are at location12. shelf1 is at location20. shelf4 is at location23. laundryhamper1 is at location8. drawer1 is at location21. drawer2 is at location18. shelf6 is at location24.  Currently, the objects are at locations as follows. pillow1, laptop2, cellphone1, book1, pillow2, and laptop1 are at location13. laundryhamperlid1 is at location8. blinds2 is at location15. desklamp1, alarmclock3, and bowl2 are at location23. pencil2 and creditcard1 are at location22. pen1, cellphone2, mug2, pencil3, and cd3 are at location10. baseballbat1 is at location9. window1 is at location5. window2 is at location4. lightswitch1 is at location14. chair2 is at location26. chair1 is at location21. cellphone3 is at location12. keychain1 and keychain2 are at location6. blinds1 is at location16. mirror1 is at location19. cd1, mug1, pencil1, bowl1, and alarmclock1 are at location3. basketball1 is at location7. bowl3 is at location24. cd2 is at location2. alarmclock2 is at location11. agent agent1 is at location location27. The objects are in/on receptacle as follows. pencil3, pen1, desklamp1, mug2, cellphone2, bowl2, alarmclock3, and cd3 are on desk2. bowl3 is on shelf6. bowl1, mug1, pencil1, cd1, and alarmclock1 are on desk1. cellphone3 is in drawer5. laptop2, book1, cellphone1, laptop1, pillow1, and pillow2 are in bed1. alarmclock2 is on shelf3. pencil2 and creditcard1 are on shelf5. keychain1 and keychain2 are in safe1. bowl2, alarmclock3, and desklamp1 are on shelf4. cd2 is in garbagecan1. drawer6, drawer3, safe1, and drawer1 are closed. desklamp1 is off. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type booktype is examined under an object of type desklamptype.", "question": "Given the plan: \"agent agent1 moves from the current position location27 to the next position location13 that has the receptacle bed1, agent agent1 picks up object book1 from receptacle bed1 while at location location13, agent agent1 moves from the current position location13 to the next position location17 that has the receptacle drawer3, agent agent1 moves from the current position location17 to the next position location10 that has the receptacle desk2, agent agent1 moves from the current position location10 to the next position location1 that has the receptacle drawer6, agent agent1 moves from the current position location1 to the next position location10 that has the receptacle desk2, agent agent1 toggles a togglable object desklamp1 that is on the receptacle desk2 at location location10, check that the togglable object desklamp1 of type desklamptype is toggled and in receptacle desk2 at location location10 while agent agent1 is holding object book1 of type booktype\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. agent agent1 moves from the current position location27 to the next position location13 that has the receptacle bed1 and agent agent1 picks up object book1 from receptacle bed1 while at location location13. B. agent agent1 moves from the current position location13 to the next position location17 that has the receptacle drawer3 and agent agent1 moves from the current position location17 to the next position location10 that has the receptacle desk2. C. agent agent1 moves from the current position location17 to the next position location10 that has the receptacle desk2 and agent agent1 moves from the current position location10 to the next position location1 that has the receptacle drawer6. D. agent agent1 moves from the current position location10 to the next position location1 that has the receptacle drawer6 and agent agent1 moves from the current position location1 to the next position location10 that has the receptacle desk2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["agent agent1 moves from the current position location27 to the next position location13 that has the receptacle bed1 and agent agent1 picks up object book1 from receptacle bed1 while at location location13", "agent agent1 moves from the current position location13 to the next position location17 that has the receptacle drawer3 and agent agent1 moves from the current position location17 to the next position location10 that has the receptacle desk2", "agent agent1 moves from the current position location17 to the next position location10 that has the receptacle desk2 and agent agent1 moves from the current position location10 to the next position location1 that has the receptacle drawer6", "agent agent1 moves from the current position location10 to the next position location1 that has the receptacle drawer6 and agent agent1 moves from the current position location1 to the next position location10 that has the receptacle desk2"]}, "query": "Given the plan: \"agent agent1 moves from the current position location27 to the next position location13 that has the receptacle bed1, agent agent1 picks up object book1 from receptacle bed1 while at location location13, agent agent1 moves from the current position location13 to the next position location17 that has the receptacle drawer3, agent agent1 moves from the current position location17 to the next position location10 that has the receptacle desk2, agent agent1 moves from the current position location10 to the next position location1 that has the receptacle drawer6, agent agent1 moves from the current position location1 to the next position location10 that has the receptacle desk2, agent agent1 toggles a togglable object desklamp1 that is on the receptacle desk2 at location location10, check that the togglable object desklamp1 of type desklamptype is toggled and in receptacle desk2 at location location10 while agent agent1 is holding object book1 of type booktype\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
