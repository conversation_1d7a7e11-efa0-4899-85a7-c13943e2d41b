{"id": 1194727978679316860, "group": "action_justification_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x2-y3, loc-x0-y4, and loc-x1-y2. Currently, the robot is in place loc-x0-y2.Place loc-x0-y2 has been visited. The available actions are: (move ?curpos ?nextpos) - navigate from ?curpos to ?nextpos. The goal is to reach a state where the following facts hold: Place loc-x1-y3 is visited, Place loc-x2-y0 is visited, Place loc-x1-y0 is visited, Place loc-x0-y2 is visited, Place loc-x0-y3 is visited, Place loc-x2-y2 is visited, Place loc-x0-y0 is visited, Place loc-x1-y1 is visited, Place loc-x1-y4 is visited, Place loc-x3-y0 is visited, Place loc-x0-y1 is visited, Place loc-x3-y1 is visited, Place loc-x3-y3 is visited, Place loc-x3-y4 is visited, Place loc-x3-y2 is visited, Place loc-x2-y4 is visited, and Place loc-x2-y1 is visited.", "question": "Simplify the plan \"(move loc-x0-y2 loc-x0-y3) (move loc-x0-y3 loc-x1-y3) (move loc-x1-y3 loc-x1-y4) (move loc-x1-y4 loc-x2-y4) (move loc-x2-y4 loc-x3-y4) (move loc-x3-y4 loc-x3-y3) (move loc-x3-y3 loc-x3-y4) (move loc-x3-y4 loc-x3-y3) (move loc-x3-y3 loc-x3-y2) (move loc-x3-y2 loc-x3-y1) (move loc-x3-y1 loc-x3-y0) (move loc-x3-y0 loc-x2-y0) (move loc-x2-y0 loc-x1-y0) (move loc-x1-y0 loc-x0-y0) (move loc-x0-y0 loc-x0-y1) (move loc-x0-y1 loc-x1-y1) (move loc-x1-y1 loc-x2-y1) (move loc-x2-y1 loc-x2-y2)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move loc-x3-y4 loc-x3-y3)", "(move loc-x3-y3 loc-x3-y4)", "-1"], ["(move loc-x3-y3 loc-x3-y4)", "(move loc-x3-y4 loc-x3-y3)", "-1"]], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-5-3-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y3 loc-x1-y4 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y4 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 loc-x3-y4 - place)\n    (:init (at-robot loc-x0-y2) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y4) (connected loc-x1-y4 loc-x1-y3) (connected loc-x1-y4 loc-x2-y4) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y4 loc-x1-y4) (connected loc-x2-y4 loc-x3-y4) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x3-y2) (connected loc-x3-y3 loc-x3-y4) (connected loc-x3-y4 loc-x2-y4) (connected loc-x3-y4 loc-x3-y3) (visited loc-x0-y2))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y3) (visited loc-x1-y4) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y4) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3) (visited loc-x3-y4)))\n)"}
{"id": 7518443155818252034, "group": "action_justification_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y3, loc-x0-y2, loc-x3-y1, and loc-x1-y0. Currently, the robot is in place loc-x2-y1.Place loc-x2-y1 has been visited. The available actions are: (move ?curpos ?nextpos) - go to ?nextpos from ?curpos. The goal is to reach a state where the following facts hold: Place loc-x1-y3 is visited, Place loc-x2-y0 is visited, Place loc-x0-y3 is visited, Place loc-x2-y2 is visited, Place loc-x0-y0 is visited, Place loc-x1-y1 is visited, Place loc-x1-y2 is visited, Place loc-x3-y0 is visited, Place loc-x0-y1 is visited, Place loc-x2-y3 is visited, Place loc-x3-y2 is visited, and Place loc-x2-y1 is visited.", "question": "Simplify the plan \"(move loc-x2-y1 loc-x2-y2) (move loc-x2-y2 loc-x3-y2) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y3) (move loc-x2-y3 loc-x1-y3) (move loc-x1-y3 loc-x0-y3) (move loc-x0-y3 loc-x1-y3) (move loc-x1-y3 loc-x1-y2) (move loc-x1-y2 loc-x1-y1) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y0) (move loc-x0-y0 loc-x0-y1) (move loc-x0-y1 loc-x1-y1) (move loc-x1-y1 loc-x2-y1) (move loc-x2-y1 loc-x2-y0) (move loc-x2-y0 loc-x3-y0) (move loc-x3-y0 loc-x2-y0) (move loc-x2-y0 loc-x3-y0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move loc-x2-y0 loc-x3-y0)", "2"], ["(move loc-x2-y0 loc-x3-y0)", "(move loc-x3-y0 loc-x2-y0)", "-1"], ["(move loc-x3-y0 loc-x2-y0)", "(move loc-x2-y0 loc-x3-y0)", "-1"]], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-4-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y3 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 - place)\n    (:init (at-robot loc-x2-y1) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (visited loc-x2-y1))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y3) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2)))\n)"}
{"id": 5407582758147423149, "group": "action_justification_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x2-y3, loc-x0-y4, and loc-x1-y2. Currently, the robot is in place loc-x0-y2.Place loc-x0-y2 has been visited. The available actions are: (move ?curpos ?nextpos) - go to ?nextpos from ?curpos. The goal is to reach a state where the following facts hold: Place loc-x1-y3 is visited, Place loc-x2-y0 is visited, Place loc-x1-y0 is visited, Place loc-x0-y2 is visited, Place loc-x0-y3 is visited, Place loc-x2-y2 is visited, Place loc-x0-y0 is visited, Place loc-x1-y1 is visited, Place loc-x1-y4 is visited, Place loc-x3-y0 is visited, Place loc-x0-y1 is visited, Place loc-x3-y1 is visited, Place loc-x3-y3 is visited, Place loc-x3-y4 is visited, Place loc-x3-y2 is visited, Place loc-x2-y4 is visited, and Place loc-x2-y1 is visited.", "question": "Simplify the plan \"(move loc-x0-y2 loc-x0-y3) (move loc-x0-y3 loc-x1-y3) (move loc-x1-y3 loc-x1-y4) (move loc-x1-y4 loc-x2-y4) (move loc-x2-y4 loc-x3-y4) (move loc-x3-y4 loc-x3-y3) (move loc-x3-y3 loc-x3-y2) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x3-y2) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y1) (move loc-x2-y1 loc-x3-y1) (move loc-x3-y1 loc-x3-y0) (move loc-x3-y0 loc-x2-y0) (move loc-x2-y0 loc-x1-y0) (move loc-x1-y0 loc-x1-y1) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move loc-x3-y2 loc-x2-y2)", "(move loc-x2-y2 loc-x3-y2)", "-1"], ["(move loc-x2-y2 loc-x3-y2)", "(move loc-x3-y2 loc-x2-y2)", "-1"]], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-5-3-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y3 loc-x1-y4 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y4 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 loc-x3-y4 - place)\n    (:init (at-robot loc-x0-y2) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y4) (connected loc-x1-y4 loc-x1-y3) (connected loc-x1-y4 loc-x2-y4) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y4 loc-x1-y4) (connected loc-x2-y4 loc-x3-y4) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x3-y2) (connected loc-x3-y3 loc-x3-y4) (connected loc-x3-y4 loc-x2-y4) (connected loc-x3-y4 loc-x3-y3) (visited loc-x0-y2))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y3) (visited loc-x1-y4) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y4) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3) (visited loc-x3-y4)))\n)"}
{"id": -7912364969241848946, "group": "action_justification_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x3-y0.Place loc-x3-y0 has been visited. The available actions are: (move ?curpos ?nextpos) - navigate from ?curpos to ?nextpos. The goal is to reach a state where the following facts hold: Place loc-x1-y3 is visited, Place loc-x2-y0 is visited, Place loc-x0-y2 is visited, Place loc-x0-y3 is visited, Place loc-x2-y2 is visited, Place loc-x0-y0 is visited, Place loc-x1-y1 is visited, Place loc-x1-y2 is visited, Place loc-x3-y0 is visited, Place loc-x0-y1 is visited, Place loc-x2-y3 is visited, Place loc-x3-y3 is visited, Place loc-x3-y2 is visited, and Place loc-x2-y1 is visited.", "question": "Simplify the plan \"(move loc-x3-y0 loc-x2-y0) (move loc-x2-y0 loc-x2-y1) (move loc-x2-y1 loc-x1-y1) (move loc-x1-y1 loc-x1-y2) (move loc-x1-y2 loc-x2-y2) (move loc-x2-y2 loc-x1-y2) (move loc-x1-y2 loc-x2-y2) (move loc-x2-y2 loc-x3-y2) (move loc-x3-y2 loc-x3-y3) (move loc-x3-y3 loc-x2-y3) (move loc-x2-y3 loc-x1-y3) (move loc-x1-y3 loc-x0-y3) (move loc-x0-y3 loc-x0-y2) (move loc-x0-y2 loc-x0-y1) (move loc-x0-y1 loc-x0-y0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move loc-x1-y2 loc-x2-y2)", "(move loc-x2-y2 loc-x1-y2)", "-1"], ["(move loc-x2-y2 loc-x1-y2)", "(move loc-x1-y2 loc-x2-y2)", "-1"]], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-2-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x3-y0) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x3-y0))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": 674855600240698383, "group": "action_justification_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y3, loc-x0-y2, loc-x3-y1, and loc-x1-y0. Currently, the robot is in place loc-x2-y1.Place loc-x2-y1 has been visited. The available actions are: (move ?curpos ?nextpos) - travel from the current position ?curpos to the next position ?nextpos. The goal is to reach a state where the following facts hold: Place loc-x1-y3 is visited, Place loc-x2-y0 is visited, Place loc-x0-y3 is visited, Place loc-x2-y2 is visited, Place loc-x0-y0 is visited, Place loc-x1-y1 is visited, Place loc-x1-y2 is visited, Place loc-x3-y0 is visited, Place loc-x0-y1 is visited, Place loc-x2-y3 is visited, Place loc-x3-y2 is visited, and Place loc-x2-y1 is visited.", "question": "Simplify the plan \"(move loc-x2-y1 loc-x2-y0) (move loc-x2-y0 loc-x3-y0) (move loc-x3-y0 loc-x2-y0) (move loc-x2-y0 loc-x2-y1) (move loc-x2-y1 loc-x2-y2) (move loc-x2-y2 loc-x3-y2) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y3) (move loc-x2-y3 loc-x1-y3) (move loc-x1-y3 loc-x2-y3) (move loc-x2-y3 loc-x1-y3) (move loc-x1-y3 loc-x0-y3) (move loc-x0-y3 loc-x1-y3) (move loc-x1-y3 loc-x1-y2) (move loc-x1-y2 loc-x1-y1) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move loc-x2-y3 loc-x1-y3)", "(move loc-x1-y3 loc-x2-y3)", "-1"], ["(move loc-x1-y3 loc-x2-y3)", "(move loc-x2-y3 loc-x1-y3)", "-1"]], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-4-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y3 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 - place)\n    (:init (at-robot loc-x2-y1) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (visited loc-x2-y1))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y3) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2)))\n)"}
{"id": 5312360379507350483, "group": "action_justification_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y3, loc-x0-y2, loc-x3-y1, and loc-x1-y0. Currently, the robot is in place loc-x2-y1.Place loc-x2-y1 has been visited. The available actions are: (move ?curpos ?nextpos) - move to place ?nextpos from place ?curpos. The goal is to reach a state where the following facts hold: Place loc-x1-y3 is visited, Place loc-x2-y0 is visited, Place loc-x0-y3 is visited, Place loc-x2-y2 is visited, Place loc-x0-y0 is visited, Place loc-x1-y1 is visited, Place loc-x1-y2 is visited, Place loc-x3-y0 is visited, Place loc-x0-y1 is visited, Place loc-x2-y3 is visited, Place loc-x3-y2 is visited, and Place loc-x2-y1 is visited.", "question": "Simplify the plan \"(move loc-x2-y1 loc-x2-y0) (move loc-x2-y0 loc-x3-y0) (move loc-x3-y0 loc-x2-y0) (move loc-x2-y0 loc-x2-y1) (move loc-x2-y1 loc-x2-y2) (move loc-x2-y2 loc-x3-y2) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y3) (move loc-x2-y3 loc-x1-y3) (move loc-x1-y3 loc-x0-y3) (move loc-x0-y3 loc-x1-y3) (move loc-x1-y3 loc-x1-y2) (move loc-x1-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y1) (move loc-x2-y1 loc-x1-y1) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y0) (move loc-x0-y0 loc-x0-y1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move loc-x0-y0 loc-x0-y1)", "1"]], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-4-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y3 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 - place)\n    (:init (at-robot loc-x2-y1) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (visited loc-x2-y1))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y3) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2)))\n)"}
{"id": 6118387912695573909, "group": "action_justification_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x2-y3, loc-x0-y4, and loc-x1-y2. Currently, the robot is in place loc-x0-y2.Place loc-x0-y2 has been visited. The available actions are: (move ?curpos ?nextpos) - navigate from ?curpos to ?nextpos. The goal is to reach a state where the following facts hold: Place loc-x1-y3 is visited, Place loc-x2-y0 is visited, Place loc-x1-y0 is visited, Place loc-x0-y2 is visited, Place loc-x0-y3 is visited, Place loc-x2-y2 is visited, Place loc-x0-y0 is visited, Place loc-x1-y1 is visited, Place loc-x1-y4 is visited, Place loc-x3-y0 is visited, Place loc-x0-y1 is visited, Place loc-x3-y1 is visited, Place loc-x3-y3 is visited, Place loc-x3-y4 is visited, Place loc-x3-y2 is visited, Place loc-x2-y4 is visited, and Place loc-x2-y1 is visited.", "question": "Simplify the plan \"(move loc-x0-y2 loc-x0-y3) (move loc-x0-y3 loc-x1-y3) (move loc-x1-y3 loc-x1-y4) (move loc-x1-y4 loc-x2-y4) (move loc-x2-y4 loc-x3-y4) (move loc-x3-y4 loc-x3-y3) (move loc-x3-y3 loc-x3-y2) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y1) (move loc-x2-y1 loc-x2-y2) (move loc-x2-y2 loc-x2-y1) (move loc-x2-y1 loc-x1-y1) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y0) (move loc-x0-y0 loc-x1-y0) (move loc-x1-y0 loc-x2-y0) (move loc-x2-y0 loc-x3-y0) (move loc-x3-y0 loc-x3-y1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move loc-x2-y2 loc-x2-y1)", "(move loc-x2-y1 loc-x2-y2)", "-1"], ["(move loc-x2-y1 loc-x2-y2)", "(move loc-x2-y2 loc-x2-y1)", "-1"]], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-5-3-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y3 loc-x1-y4 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y4 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 loc-x3-y4 - place)\n    (:init (at-robot loc-x0-y2) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y4) (connected loc-x1-y4 loc-x1-y3) (connected loc-x1-y4 loc-x2-y4) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y4 loc-x1-y4) (connected loc-x2-y4 loc-x3-y4) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x3-y2) (connected loc-x3-y3 loc-x3-y4) (connected loc-x3-y4 loc-x2-y4) (connected loc-x3-y4 loc-x3-y3) (visited loc-x0-y2))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y3) (visited loc-x1-y4) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y4) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3) (visited loc-x3-y4)))\n)"}
{"id": 6852479958956747817, "group": "action_justification_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x3-y0.Place loc-x3-y0 has been visited. The available actions are: (move ?curpos ?nextpos) - navigate from ?curpos to ?nextpos. The goal is to reach a state where the following facts hold: Place loc-x1-y3 is visited, Place loc-x2-y0 is visited, Place loc-x0-y2 is visited, Place loc-x0-y3 is visited, Place loc-x2-y2 is visited, Place loc-x0-y0 is visited, Place loc-x1-y1 is visited, Place loc-x1-y2 is visited, Place loc-x3-y0 is visited, Place loc-x0-y1 is visited, Place loc-x2-y3 is visited, Place loc-x3-y3 is visited, Place loc-x3-y2 is visited, and Place loc-x2-y1 is visited.", "question": "Simplify the plan \"(move loc-x3-y0 loc-x2-y0) (move loc-x2-y0 loc-x2-y1) (move loc-x2-y1 loc-x1-y1) (move loc-x1-y1 loc-x1-y2) (move loc-x1-y2 loc-x2-y2) (move loc-x2-y2 loc-x3-y2) (move loc-x3-y2 loc-x3-y3) (move loc-x3-y3 loc-x2-y3) (move loc-x2-y3 loc-x1-y3) (move loc-x1-y3 loc-x0-y3) (move loc-x0-y3 loc-x1-y3) (move loc-x1-y3 loc-x0-y3) (move loc-x0-y3 loc-x0-y2) (move loc-x0-y2 loc-x0-y1) (move loc-x0-y1 loc-x0-y0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move loc-x1-y3 loc-x0-y3)", "(move loc-x0-y3 loc-x1-y3)", "-1"], ["(move loc-x0-y3 loc-x1-y3)", "(move loc-x1-y3 loc-x0-y3)", "-1"]], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-2-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x3-y0) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x3-y0))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": 3189739645656031685, "group": "action_justification_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x2-y3, loc-x0-y4, and loc-x1-y2. Currently, the robot is in place loc-x0-y2.Place loc-x0-y2 has been visited. The available actions are: (move ?curpos ?nextpos) - travel from ?curpos to ?nextpos. The goal is to reach a state where the following facts hold: Place loc-x1-y3 is visited, Place loc-x2-y0 is visited, Place loc-x1-y0 is visited, Place loc-x0-y2 is visited, Place loc-x0-y3 is visited, Place loc-x2-y2 is visited, Place loc-x0-y0 is visited, Place loc-x1-y1 is visited, Place loc-x1-y4 is visited, Place loc-x3-y0 is visited, Place loc-x0-y1 is visited, Place loc-x3-y1 is visited, Place loc-x3-y3 is visited, Place loc-x3-y4 is visited, Place loc-x3-y2 is visited, Place loc-x2-y4 is visited, and Place loc-x2-y1 is visited.", "question": "Simplify the plan \"(move loc-x0-y2 loc-x0-y3) (move loc-x0-y3 loc-x1-y3) (move loc-x1-y3 loc-x1-y4) (move loc-x1-y4 loc-x2-y4) (move loc-x2-y4 loc-x3-y4) (move loc-x3-y4 loc-x3-y3) (move loc-x3-y3 loc-x3-y2) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y1) (move loc-x2-y1 loc-x1-y1) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y0) (move loc-x0-y0 loc-x1-y0) (move loc-x1-y0 loc-x2-y0) (move loc-x2-y0 loc-x3-y0) (move loc-x3-y0 loc-x3-y1) (move loc-x3-y1 loc-x2-y1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move loc-x3-y1 loc-x2-y1)", "1"]], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-5-3-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y3 loc-x1-y4 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y4 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 loc-x3-y4 - place)\n    (:init (at-robot loc-x0-y2) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y4) (connected loc-x1-y4 loc-x1-y3) (connected loc-x1-y4 loc-x2-y4) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y4 loc-x1-y4) (connected loc-x2-y4 loc-x3-y4) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x3-y2) (connected loc-x3-y3 loc-x3-y4) (connected loc-x3-y4 loc-x2-y4) (connected loc-x3-y4 loc-x3-y3) (visited loc-x0-y2))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y3) (visited loc-x1-y4) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y4) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3) (visited loc-x3-y4)))\n)"}
{"id": -2111146498867458514, "group": "action_justification_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y3, loc-x0-y2, loc-x3-y1, and loc-x1-y0. Currently, the robot is in place loc-x2-y1.Place loc-x2-y1 has been visited. The available actions are: (move ?curpos ?nextpos) - transition from the current position ?curpos to the next position ?nextpos. The goal is to reach a state where the following facts hold: Place loc-x1-y3 is visited, Place loc-x2-y0 is visited, Place loc-x0-y3 is visited, Place loc-x2-y2 is visited, Place loc-x0-y0 is visited, Place loc-x1-y1 is visited, Place loc-x1-y2 is visited, Place loc-x3-y0 is visited, Place loc-x0-y1 is visited, Place loc-x2-y3 is visited, Place loc-x3-y2 is visited, and Place loc-x2-y1 is visited.", "question": "Simplify the plan \"(move loc-x2-y1 loc-x2-y0) (move loc-x2-y0 loc-x3-y0) (move loc-x3-y0 loc-x2-y0) (move loc-x2-y0 loc-x2-y1) (move loc-x2-y1 loc-x2-y2) (move loc-x2-y2 loc-x3-y2) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y3) (move loc-x2-y3 loc-x1-y3) (move loc-x1-y3 loc-x0-y3) (move loc-x0-y3 loc-x1-y3) (move loc-x1-y3 loc-x1-y2) (move loc-x1-y2 loc-x1-y1) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y0) (move loc-x0-y0 loc-x0-y1) (move loc-x0-y1 loc-x0-y0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move loc-x0-y1 loc-x0-y0)", "2"], ["(move loc-x0-y1 loc-x0-y0)", "(move loc-x0-y0 loc-x0-y1)", "-1"], ["(move loc-x0-y0 loc-x0-y1)", "(move loc-x0-y1 loc-x0-y0)", "-1"]], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-4-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y3 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 - place)\n    (:init (at-robot loc-x2-y1) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (visited loc-x2-y1))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y3) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2)))\n)"}
{"id": -4321627342990418552, "group": "action_justification_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y3 and loc-x0-y0. \nCurrently, the robot is in place loc-x3-y3.Place loc-x3-y3 has been visited. The available actions are: (move ?curpos ?nextpos) - Please move to ?nextpos position from ?curpos position. The goal is to reach a state where the following facts hold: Place loc-x1-y3 is visited, Place loc-x2-y0 is visited, Place loc-x1-y0 is visited, Place loc-x0-y2 is visited, Place loc-x3-y3 is visited, Place loc-x2-y2 is visited, Place loc-x1-y1 is visited, Place loc-x1-y2 is visited, Place loc-x3-y0 is visited, Place loc-x0-y1 is visited, Place loc-x2-y3 is visited, Place loc-x3-y1 is visited, Place loc-x3-y2 is visited, and Place loc-x2-y1 is visited.", "question": "Simplify the plan \"(move loc-x3-y3 loc-x2-y3) (move loc-x2-y3 loc-x1-y3) (move loc-x1-y3 loc-x1-y2) (move loc-x1-y2 loc-x0-y2) (move loc-x0-y2 loc-x0-y1) (move loc-x0-y1 loc-x1-y1) (move loc-x1-y1 loc-x2-y1) (move loc-x2-y1 loc-x1-y1) (move loc-x1-y1 loc-x1-y0) (move loc-x1-y0 loc-x2-y0) (move loc-x2-y0 loc-x3-y0) (move loc-x3-y0 loc-x3-y1) (move loc-x3-y1 loc-x3-y2) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x3-y2)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move loc-x2-y2 loc-x3-y2)", "1"]], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-2-2020)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y1 loc-x0-y2 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x3-y3) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x1-y2) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x3-y3))\n    (:goal (and (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": 2355349441423409023, "group": "action_justification_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The only unavailable cell is loc-x3-y1. \nCurrently, the robot is in place loc-x0-y3.Place loc-x0-y3 has been visited. The available actions are: (move ?curpos ?nextpos) - go to ?nextpos from ?curpos. The goal is to reach a state where the following facts hold: Place loc-x1-y3 is visited, Place loc-x2-y0 is visited, Place loc-x1-y0 is visited, Place loc-x0-y2 is visited, Place loc-x0-y3 is visited, Place loc-x2-y2 is visited, Place loc-x0-y0 is visited, Place loc-x1-y1 is visited, Place loc-x1-y2 is visited, Place loc-x3-y0 is visited, Place loc-x0-y1 is visited, Place loc-x2-y3 is visited, Place loc-x3-y3 is visited, Place loc-x3-y2 is visited, and Place loc-x2-y1 is visited.", "question": "Simplify the plan \"(move loc-x0-y3 loc-x0-y2) (move loc-x0-y2 loc-x0-y1) (move loc-x0-y1 loc-x0-y0) (move loc-x0-y0 loc-x1-y0) (move loc-x1-y0 loc-x1-y1) (move loc-x1-y1 loc-x1-y2) (move loc-x1-y2 loc-x1-y3) (move loc-x1-y3 loc-x1-y2) (move loc-x1-y2 loc-x1-y3) (move loc-x1-y3 loc-x2-y3) (move loc-x2-y3 loc-x3-y3) (move loc-x3-y3 loc-x3-y2) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y1) (move loc-x2-y1 loc-x2-y0) (move loc-x2-y0 loc-x3-y0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move loc-x1-y2 loc-x1-y3)", "(move loc-x1-y3 loc-x1-y2)", "-1"], ["(move loc-x1-y3 loc-x1-y2)", "(move loc-x1-y2 loc-x1-y3)", "-1"]], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-1-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x0-y3) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x0-y3))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": 7435001057603114336, "group": "action_justification_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The only unavailable cell is loc-x3-y1. \nCurrently, the robot is in place loc-x0-y3.Place loc-x0-y3 has been visited. The available actions are: (move ?curpos ?nextpos) - move to place ?nextpos from place ?curpos. The goal is to reach a state where the following facts hold: Place loc-x1-y3 is visited, Place loc-x2-y0 is visited, Place loc-x1-y0 is visited, Place loc-x0-y2 is visited, Place loc-x0-y3 is visited, Place loc-x2-y2 is visited, Place loc-x0-y0 is visited, Place loc-x1-y1 is visited, Place loc-x1-y2 is visited, Place loc-x3-y0 is visited, Place loc-x0-y1 is visited, Place loc-x2-y3 is visited, Place loc-x3-y3 is visited, Place loc-x3-y2 is visited, and Place loc-x2-y1 is visited.", "question": "Simplify the plan \"(move loc-x0-y3 loc-x1-y3) (move loc-x1-y3 loc-x2-y3) (move loc-x2-y3 loc-x3-y3) (move loc-x3-y3 loc-x3-y2) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x1-y2) (move loc-x1-y2 loc-x2-y2) (move loc-x2-y2 loc-x1-y2) (move loc-x1-y2 loc-x0-y2) (move loc-x0-y2 loc-x0-y1) (move loc-x0-y1 loc-x0-y0) (move loc-x0-y0 loc-x1-y0) (move loc-x1-y0 loc-x1-y1) (move loc-x1-y1 loc-x2-y1) (move loc-x2-y1 loc-x2-y0) (move loc-x2-y0 loc-x3-y0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move loc-x2-y2 loc-x1-y2)", "(move loc-x1-y2 loc-x2-y2)", "-1"], ["(move loc-x1-y2 loc-x2-y2)", "(move loc-x2-y2 loc-x1-y2)", "-1"]], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-1-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x0-y3) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x0-y3))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": -1272133617518972459, "group": "action_justification_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y3, loc-x3-y3, and loc-x0-y0. \nCurrently, the robot is in place loc-x3-y2.Place loc-x3-y2 has been visited. The available actions are: (move ?curpos ?nextpos) - Please move to ?nextpos position from ?curpos position. The goal is to reach a state where the following facts hold: Place loc-x1-y3 is visited, Place loc-x2-y0 is visited, Place loc-x1-y0 is visited, Place loc-x0-y2 is visited, Place loc-x2-y2 is visited, Place loc-x1-y1 is visited, Place loc-x1-y2 is visited, Place loc-x3-y0 is visited, Place loc-x0-y1 is visited, Place loc-x2-y3 is visited, Place loc-x3-y1 is visited, Place loc-x3-y2 is visited, and Place loc-x2-y1 is visited.", "question": "Simplify the plan \"(move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y3) (move loc-x2-y3 loc-x1-y3) (move loc-x1-y3 loc-x1-y2) (move loc-x1-y2 loc-x0-y2) (move loc-x0-y2 loc-x0-y1) (move loc-x0-y1 loc-x1-y1) (move loc-x1-y1 loc-x2-y1) (move loc-x2-y1 loc-x1-y1) (move loc-x1-y1 loc-x2-y1) (move loc-x2-y1 loc-x3-y1) (move loc-x3-y1 loc-x3-y0) (move loc-x3-y0 loc-x2-y0) (move loc-x2-y0 loc-x1-y0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move loc-x1-y1 loc-x2-y1)", "(move loc-x2-y1 loc-x1-y1)", "-1"], ["(move loc-x2-y1 loc-x1-y1)", "(move loc-x1-y1 loc-x2-y1)", "-1"]], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-3-2020)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y1 loc-x0-y2 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 - place)\n    (:init (at-robot loc-x3-y2) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x1-y2) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (visited loc-x3-y2))\n    (:goal (and (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2)))\n)"}
{"id": 5357640852055954325, "group": "action_justification_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y3 and loc-x0-y0. \nCurrently, the robot is in place loc-x3-y3.Place loc-x3-y3 has been visited. The available actions are: (move ?curpos ?nextpos) - travel from ?curpos to ?nextpos. The goal is to reach a state where the following facts hold: Place loc-x1-y3 is visited, Place loc-x2-y0 is visited, Place loc-x1-y0 is visited, Place loc-x0-y2 is visited, Place loc-x3-y3 is visited, Place loc-x2-y2 is visited, Place loc-x1-y1 is visited, Place loc-x1-y2 is visited, Place loc-x3-y0 is visited, Place loc-x0-y1 is visited, Place loc-x2-y3 is visited, Place loc-x3-y1 is visited, Place loc-x3-y2 is visited, and Place loc-x2-y1 is visited.", "question": "Simplify the plan \"(move loc-x3-y3 loc-x3-y2) (move loc-x3-y2 loc-x3-y1) (move loc-x3-y1 loc-x3-y0) (move loc-x3-y0 loc-x3-y1) (move loc-x3-y1 loc-x2-y1) (move loc-x2-y1 loc-x2-y0) (move loc-x2-y0 loc-x1-y0) (move loc-x1-y0 loc-x1-y1) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y2) (move loc-x0-y2 loc-x1-y2) (move loc-x1-y2 loc-x1-y3) (move loc-x1-y3 loc-x2-y3) (move loc-x2-y3 loc-x2-y2) (move loc-x2-y2 loc-x1-y2)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(move loc-x2-y2 loc-x1-y2)", "1"]], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-2-2020)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y1 loc-x0-y2 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x3-y3) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x1-y2) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x3-y3))\n    (:goal (and (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
