{"id": 1991549721694001435, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l0-2, l0-1, and l0-0 are in c0; l2-1, l2-0, and l2-2 are in c2; l1-2, l1-0, and l1-1 are in c1. Currently, t0 is at l0-1, a0 and t2 are at l2-0, p4 and t1 are at l1-0, p1, p3, and p0 are in a0, p2 is in t0.", "question": "Is it possible to transition to a state where the action \"place the object t2 into the truck l0-1 at location c2\" can be applied?", "answer": "no"}
{"id": -2724827960285167407, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-2, l0-1, and l0-0 are in c0; l1-2, l1-0, and l1-1 are in c1. Currently, p0 is at l0-1, p3, t0, and p1 are at l0-2, t1 is at l1-2, a0 is at l0-0, p2 is in t0.", "question": "Is it possible to transition to a state where the action \"unload the object l0-0 from the truck t0 at location c0\" can be applied?", "answer": "no"}
{"id": 5540851196970732681, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, t0 is at l0-1, p1 and a0 are at l1-0, p2 is at l0-0, t1 is at l1-1, p0 is in t1, p3 is in a0.", "question": "Is it possible to transition to a state where the action \"load object p2 into truck t0 at location l0-0\" can be applied?", "answer": "yes"}
{"id": 2566074982906870688, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-2, l0-1, and l0-0 are in c0; l1-2, l1-0, and l1-1 are in c1. Currently, p3 is at l0-2, t0 and p2 are at l0-1, a0 is at l1-0, t1 is at l1-2, p0 and p1 are in t0.", "question": "Is it possible to transition to a state where the action \"fly the airplane l1-1 from the airport p3 to the airport l1-2\" can be applied?", "answer": "no"}
{"id": 3565936110601348422, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, a0 and t1 are at l1-0, t0 is at l0-0, p2, p0, and p3 are in t1, p1 is in a0.", "question": "Is it possible to transition to a state where the action \"load the object c0 from location t1 into the airplane p0\" can be applied?", "answer": "no"}
{"id": -9182711933972997217, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l0-2, l0-1, and l0-0 are in c0; l2-1, l2-0, and l2-2 are in c2; l1-2, l1-0, and l1-1 are in c1. Currently, p1, a0, and p3 are at l2-0, t0 is at l0-1, p0 and t1 are at l1-2, t2 is at l2-1, p4 is in a0, p2 is in t0.", "question": "Is it possible to transition to a state where the action \"drive the truck t2 in city c2 from location l2-2 to location l2-0\" can be applied?", "answer": "yes"}
{"id": -5074069474542963394, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-2, l0-1, and l0-0 are in c0; l1-2, l1-0, and l1-1 are in c1. Currently, p0 and t0 are at l0-1, p3 and p1 are at l0-2, a0 and t1 are at l1-0, p2 is in t1.", "question": "Is it possible to transition to a state where the action \"unstack p2 from crate l1-0 into the truck t1\" can be applied?", "answer": "no"}
{"id": 2497787696614096034, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l3-4, l3-7, l3-3, l3-2, l3-0, l3-8, l3-6, l3-1, l3-9, and l3-5 are in c3; l0-2, l0-9, l0-1, l0-6, l0-8, l0-0, l0-4, l0-5, l0-7, and l0-3 are in c0; l2-2, l2-7, l2-0, l2-3, l2-9, l2-4, l2-1, l2-6, l2-8, and l2-5 are in c2; l4-5, l4-2, l4-7, l4-9, l4-8, l4-0, l4-6, l4-4, l4-3, and l4-1 are in c4; l1-8, l1-7, l1-3, l1-2, l1-0, l1-6, l1-4, l1-5, l1-1, and l1-9 are in c1. Currently, t2 is at l2-9, t0 is at l0-2, t4 is at l4-2, a0 is at l2-0, t1 is at l1-0, t3 is at l3-0, p2 is at l4-8, p0 and p3 are in t2, p1 is in a0.", "question": "Is it possible to transition to a state where the action \"navigate the truck t1 from location l1-5 in city c1 to location l1-2 in the same city\" can be applied?", "answer": "yes"}
{"id": -4980077240752893964, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l4-0, l4-2, and l4-1 are in c4; l3-1, l3-0, and l3-2 are in c3; l0-2, l0-1, and l0-0 are in c0; l2-1, l2-0, and l2-2 are in c2; l1-2, l1-0, and l1-1 are in c1. Currently, p3 is at l4-1, t0 is at l0-2, a0 and t3 are at l3-0, t4 is at l4-0, t2 is at l2-0, t1 is at l1-2, p2 is in t2, p1 is in t3, p0 is in a0.", "question": "Is it possible to transition to a state where the action \"remove the object c1 from the truck t3 and place it on the location l1-2\" can be applied?", "answer": "no"}
{"id": -928897422565075377, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l4-0, l4-2, and l4-1 are in c4; l3-1, l3-0, and l3-2 are in c3; l0-2, l0-1, and l0-0 are in c0; l2-1, l2-0, and l2-2 are in c2; l1-2, l1-0, and l1-1 are in c1. Currently, p3 is at l4-1, p2 is at l2-2, t0 is at l0-2, a0 and t2 are at l2-0, t3 is at l3-0, t4 is at l4-0, t1 is at l1-2, p1 is in t3, p0 is in a0.", "question": "Is it possible to transition to a state where the action \"sail the ship t1 into city c1 from location l1-2 in city l1-1\" can be applied?", "answer": "no"}
{"id": 2099802504790024603, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, p0 and a0 are at l0-0, t0 is at l0-1, p1, p2, and t1 are at l1-0, p3 is in t0.", "question": "Is it possible to transition to a state where the action \"remove the object p1 from the truck t1 and place it on the location l1-0\" can be applied?", "answer": "yes"}
{"id": 2875780928092188787, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 4 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l0-2, l0-1, and l0-0 are in c0; l2-1, l2-0, and l2-2 are in c2; l1-2, l1-0, and l1-1 are in c1. Currently, p2, p1, a0, and t2 are at l2-0, p3 and t1 are at l1-2, t0 is at l0-0, p0 is in t2.", "question": "Is it possible to transition to a state where the action \"navigate the truck t0 from location l0-2 in city c0 to location l0-1 in the next city\" can be applied?", "answer": "no"}
{"id": -1011594381339674512, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 4 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l0-2, l0-1, and l0-0 are in c0; l2-1, l2-0, and l2-2 are in c2; l1-2, l1-0, and l1-1 are in c1. Currently, p0 and t2 are at l2-1, t0 is at l0-1, a0 is at l2-0, t1 is at l1-0, p2 is at l0-0, p1 is in t1, p3 is in t2.", "question": "Is it possible to transition to a state where the action \"navigate the truck l2-1 from its current location t1 in city c1 to the new location l1-0 within the same city\" can be applied?", "answer": "no"}
{"id": 2902832396618372313, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 4 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l0-2, l0-1, and l0-0 are in c0; l2-1, l2-0, and l2-2 are in c2; l1-2, l1-0, and l1-1 are in c1. Currently, p0 is at l2-1, t1 and p3 are at l1-0, a0 and t0 are at l0-0, t2 is at l2-0, p2 is in t0, p1 is in a0.", "question": "Is it possible to transition to a state where the action \"navigate the truck t1 which is in location l1-1 in city c1 to another location l1-2 in the same city\" can be applied?", "answer": "yes"}
{"id": -5412331473698620602, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-2, l0-1, and l0-0 are in c0; l1-2, l1-0, and l1-1 are in c1. Currently, a0, p3, and t0 are at l0-0, t1 is at l1-2, p1 and p2 are in t1, p0 is in t0.", "question": "Is it possible to transition to a state where the action \"drive the truck t0 in city c0 from location l0-1 to location l0-2\" can be applied?", "answer": "yes"}
