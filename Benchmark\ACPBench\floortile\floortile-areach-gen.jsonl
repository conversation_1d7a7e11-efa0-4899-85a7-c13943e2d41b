{"id": -6827376788996833467, "group": "reachable_action_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 2 robots. The tiles locations are: tile_10 is to the right of tile_9, tile_12 is to the right of tile_11, tile_13 is to the right of tile_12, tile_18 is to the right of tile_17, tile_5 is to the right of tile_4, tile_9 is to the right of tile_8, tile_8 is to the right of tile_7, tile_4 is to the right of tile_3, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, tile_20 is to the right of tile_19, tile_19 is to the right of tile_18, tile_14 is to the right of tile_13, tile_7 is to the right of tile_6, tile_17 is to the right of tile_16, and tile_15 is to the right of tile_14. Further, tile_12 is down from tile_17, tile_6 is down from tile_11, tile_2 is down from tile_7, tile_4 is down from tile_9, tile_9 is down from tile_14, tile_13 is down from tile_18, tile_5 is down from tile_10, tile_7 is down from tile_12, tile_15 is down from tile_20, tile_10 is down from tile_15, tile_11 is down from tile_16, tile_3 is down from tile_8, tile_14 is down from tile_19, tile_8 is down from tile_13, and tile_1 is down from tile_6 Currently, robot robot1 is at tile_3 and holding color white and robot robot2 is at tile_10 and holding color black; tile_7, tile_14, tile_1, tile_19, tile_5, tile_2, tile_6, tile_9, tile_8, tile_4, tile_11, and tile_12 are clear; tile_20 is painted white, tile_13 is painted black, tile_18 is painted white, tile_17 is painted black, tile_15 is painted black, and tile_16 is painted white. The available actions are: (change-color ?r ?c ?c2) - modify the color of the robot ?r from ?c to ?c2, (paint-up ?r ?y ?x ?c) - apply color ?c to tile ?y above tile ?x using robot ?r, (paint-down ?r ?y ?x ?c) - paint tile ?y down from tile ?x with color ?c using robot ?r, (up ?r ?x ?y) - move the robot ?r from tile ?x to tile ?y going upwards, (down ?r ?x ?y) - move the robot ?r from the tile ?x to the tile ?y going downwards, (right ?r ?x ?y) - move robot ?r from tile ?x to the right tile tile ?y, and (left ?r ?x ?y) - move the robot ?r from the tile ?x to the tile on its left ?y.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(paint-up robot2 tile_6 tile_20 black)"], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-5-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_11) (clear tile_12) (clear tile_14) (clear tile_19) (clear tile_2) (clear tile_4) (clear tile_5) (clear tile_6) (clear tile_7) (clear tile_8) (clear tile_9) (down tile_1 tile_6) (down tile_10 tile_15) (down tile_11 tile_16) (down tile_12 tile_17) (down tile_13 tile_18) (down tile_14 tile_19) (down tile_15 tile_20) (down tile_2 tile_7) (down tile_3 tile_8) (down tile_4 tile_9) (down tile_5 tile_10) (down tile_6 tile_11) (down tile_7 tile_12) (down tile_8 tile_13) (down tile_9 tile_14) (left tile_1 tile_2) (left tile_11 tile_12) (left tile_12 tile_13) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_18 tile_19) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_13 black) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_20 white) (right tile_10 tile_9) (right tile_12 tile_11) (right tile_13 tile_12) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_19 tile_18) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_7 tile_6) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_3) (robot-at robot2 tile_10) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_5) (up tile_11 tile_6) (up tile_12 tile_7) (up tile_13 tile_8) (up tile_14 tile_9) (up tile_15 tile_10) (up tile_16 tile_11) (up tile_17 tile_12) (up tile_18 tile_13) (up tile_19 tile_14) (up tile_20 tile_15) (up tile_6 tile_1) (up tile_7 tile_2) (up tile_8 tile_3) (up tile_9 tile_4))\n    (:goal (and (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white)))\n)"}
{"id": -2584845745225226514, "group": "reachable_action_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 3 robots. The tiles locations are: tile_10 is to the right of tile_9, tile_12 is to the right of tile_11, tile_13 is to the right of tile_12, tile_18 is to the right of tile_17, tile_5 is to the right of tile_4, tile_9 is to the right of tile_8, tile_8 is to the right of tile_7, tile_4 is to the right of tile_3, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, tile_20 is to the right of tile_19, tile_19 is to the right of tile_18, tile_14 is to the right of tile_13, tile_7 is to the right of tile_6, tile_17 is to the right of tile_16, and tile_15 is to the right of tile_14. Further, tile_12 is down from tile_17, tile_6 is down from tile_11, tile_2 is down from tile_7, tile_4 is down from tile_9, tile_9 is down from tile_14, tile_13 is down from tile_18, tile_5 is down from tile_10, tile_7 is down from tile_12, tile_15 is down from tile_20, tile_10 is down from tile_15, tile_11 is down from tile_16, tile_3 is down from tile_8, tile_14 is down from tile_19, tile_8 is down from tile_13, and tile_1 is down from tile_6 Currently, robot robot1 is at tile_1 and holding color white, robot robot3 is at tile_5 and holding color white, and robot robot2 is at tile_8 and holding color black; tile_17, tile_7, tile_13, tile_2, tile_12, tile_6, tile_18, tile_4, tile_11, and tile_3 are clear; tile_20 is painted white, tile_19 is painted black, tile_10 is painted white, tile_15 is painted black, tile_9 is painted black, tile_16 is painted white, and tile_14 is painted white. The available actions are: (change-color ?r ?c ?c2) - modify the color of the robot ?r from ?c to ?c2, (paint-up ?r ?y ?x ?c) - paint tile ?y above tile ?x with color ?c using robot ?r, (paint-down ?r ?y ?x ?c) - paint the tile ?y down from tile ?x with color ?c using the robot ?r, (up ?r ?x ?y) - move the robot ?r from tile ?x to tile ?y upwards, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - move the robot ?r from the tile ?x to the tile ?y which is to the right of the tile ?x, and (left ?r ?x ?y) - move the robot ?r from the tile ?x to the tile on its left ?y.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(up robot1 tile_5 tile_10)"], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-5-3)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 robot3 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_11) (clear tile_12) (clear tile_13) (clear tile_17) (clear tile_18) (clear tile_2) (clear tile_3) (clear tile_4) (clear tile_6) (clear tile_7) (down tile_1 tile_6) (down tile_10 tile_15) (down tile_11 tile_16) (down tile_12 tile_17) (down tile_13 tile_18) (down tile_14 tile_19) (down tile_15 tile_20) (down tile_2 tile_7) (down tile_3 tile_8) (down tile_4 tile_9) (down tile_5 tile_10) (down tile_6 tile_11) (down tile_7 tile_12) (down tile_8 tile_13) (down tile_9 tile_14) (left tile_1 tile_2) (left tile_11 tile_12) (left tile_12 tile_13) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_18 tile_19) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_10 white) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_19 black) (painted tile_20 white) (painted tile_9 black) (right tile_10 tile_9) (right tile_12 tile_11) (right tile_13 tile_12) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_19 tile_18) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_7 tile_6) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_1) (robot-at robot2 tile_8) (robot-at robot3 tile_5) (robot-has robot1 white) (robot-has robot2 black) (robot-has robot3 white) (up tile_10 tile_5) (up tile_11 tile_6) (up tile_12 tile_7) (up tile_13 tile_8) (up tile_14 tile_9) (up tile_15 tile_10) (up tile_16 tile_11) (up tile_17 tile_12) (up tile_18 tile_13) (up tile_19 tile_14) (up tile_20 tile_15) (up tile_6 tile_1) (up tile_7 tile_2) (up tile_8 tile_3) (up tile_9 tile_4))\n    (:goal (and (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white)))\n)"}
{"id": 6671991906670320594, "group": "reachable_action_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_5 is to the right of tile_4, tile_8 is to the right of tile_7, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, and tile_6 is to the right of tile_5. Further, tile_5 is down from tile_8, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_9 is down from tile_12, tile_8 is down from tile_11, tile_7 is down from tile_10, tile_2 is down from tile_5, tile_1 is down from tile_4, and tile_4 is down from tile_7 Currently, robot robot1 is at tile_1 and holding color black and robot robot2 is at tile_2 and holding color white; tile_5, tile_4, tile_7, tile_3, and tile_8 are clear; tile_12 is painted white, tile_9 is painted black, tile_6 is painted white, tile_11 is painted black, and tile_10 is painted white. The available actions are: (change-color ?r ?c ?c2) - modify the color of the robot ?r from ?c to ?c2, (paint-up ?r ?y ?x ?c) - use robot ?r to paint the tile ?y above the tile ?x with the color ?c, (paint-down ?r ?y ?x ?c) - paint tile ?y down from tile ?x with color ?c using robot ?r, (up ?r ?x ?y) - move the robot ?r from tile ?x to tile ?y going upwards, (down ?r ?x ?y) - move the robot ?r from the tile ?x to the tile ?y going downwards, (right ?r ?x ?y) - move robot ?r from tile ?x to the right tile tile ?y, and (left ?r ?x ?y) - move robot ?r from tile ?x to the left tile tile ?y.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(down robot1 tile_10 tile_5)"], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_7) (clear tile_8) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_6 white) (painted tile_9 black) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_1) (robot-at robot2 tile_2) (robot-has robot1 black) (robot-has robot2 white) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": -2742452347030958986, "group": "reachable_action_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_5 is to the right of tile_4, tile_8 is to the right of tile_7, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, and tile_6 is to the right of tile_5. Further, tile_5 is down from tile_8, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_9 is down from tile_12, tile_8 is down from tile_11, tile_7 is down from tile_10, tile_2 is down from tile_5, tile_1 is down from tile_4, and tile_4 is down from tile_7 Currently, robot robot1 is at tile_3 and holding color white and robot robot2 is at tile_5 and holding color white; tile_4, tile_2, tile_7, tile_1, tile_6, tile_9, and tile_8 are clear; tile_12 is painted white, tile_11 is painted black, and tile_10 is painted white. The available actions are: (change-color ?r ?c ?c2) - change the color of robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - use robot ?r to paint the tile ?y above the tile ?x with the color ?c, (paint-down ?r ?y ?x ?c) - use robot ?r to paint the tile ?y downwards from the tile ?x with the color ?c, (up ?r ?x ?y) - move robot ?r up from tile ?x to tile ?y, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - move robot ?r from tile ?x to the right tile tile ?y, and (left ?r ?x ?y) - move the robot ?r from tile ?x to the left tile ?y.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(down robot2 tile_8 tile_2)"], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_2) (clear tile_4) (clear tile_6) (clear tile_7) (clear tile_8) (clear tile_9) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_3) (robot-at robot2 tile_5) (robot-has robot1 white) (robot-has robot2 white) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": -8538755358130500455, "group": "reachable_action_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 9 tiles and 2 robots. The tiles locations are: tile_5 is to the right of tile_4, tile_8 is to the right of tile_7, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, and tile_6 is to the right of tile_5. Further, tile_5 is down from tile_8, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_2 is down from tile_5, tile_1 is down from tile_4, and tile_4 is down from tile_7 Currently, robot robot1 is at tile_6 and holding color white and robot robot2 is at tile_2 and holding color black; tile_4, tile_5, tile_1, tile_9, tile_3, and tile_8 are clear; tile_7 is painted black. The available actions are: (change-color ?r ?c ?c2) - alter the color of the robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - apply color ?c to tile ?y above tile ?x using robot ?r, (paint-down ?r ?y ?x ?c) - paint tile ?y down from tile ?x with color ?c using robot ?r, (up ?r ?x ?y) - move the robot ?r from tile ?x to tile ?y going upwards, (down ?r ?x ?y) - move the robot ?r from the tile ?x to the tile ?y going downwards, (right ?r ?x ?y) - navigate robot ?r from tile ?x to tile ?y to the right, and (left ?r ?x ?y) - move robot ?r from tile ?x to the left tile tile ?y.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(left robot2 tile_8 tile_1)"], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_8) (clear tile_9) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_7 black) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_6) (robot-at robot2 tile_2) (robot-has robot1 white) (robot-has robot2 black) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": 6728997281043527093, "group": "reachable_action_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_5 is to the right of tile_4, tile_8 is to the right of tile_7, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, and tile_6 is to the right of tile_5. Further, tile_5 is down from tile_8, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_9 is down from tile_12, tile_8 is down from tile_11, tile_7 is down from tile_10, tile_2 is down from tile_5, tile_1 is down from tile_4, and tile_4 is down from tile_7 Currently, robot robot1 is at tile_8 and holding color white and robot robot2 is at tile_7 and holding color black; tile_4, tile_2, tile_5, tile_1, tile_6, tile_3, tile_11, tile_9, tile_12, and tile_10 are clear. The available actions are: (change-color ?r ?c ?c2) - alter the color of the robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - apply color ?c to tile ?y above tile ?x using robot ?r, (paint-down ?r ?y ?x ?c) - paint tile ?y down from tile ?x with color ?c using robot ?r, (up ?r ?x ?y) - move the robot ?r from tile ?x to tile ?y going upwards, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - move robot ?r from tile ?x to the right tile tile ?y, and (left ?r ?x ?y) - move the robot ?r from the tile ?x to the tile on its left ?y.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(left robot2 tile_5 tile_7)"], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_10) (clear tile_11) (clear tile_12) (clear tile_2) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_6) (clear tile_9) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_8) (robot-at robot2 tile_7) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": 1731710783203169959, "group": "reachable_action_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 24 tiles and 2 robots. The tiles locations are: tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_5 is to the right of tile_4, tile_23 is to the right of tile_22, tile_9 is to the right of tile_8, tile_20 is to the right of tile_19, tile_14 is to the right of tile_13, tile_17 is to the right of tile_16, tile_10 is to the right of tile_9, tile_22 is to the right of tile_21, tile_8 is to the right of tile_7, tile_15 is to the right of tile_14, tile_4 is to the right of tile_3, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, tile_18 is to the right of tile_17, tile_24 is to the right of tile_23, tile_21 is to the right of tile_20, tile_16 is to the right of tile_15, and tile_6 is to the right of tile_5. Further, tile_6 is down from tile_12, tile_2 is down from tile_8, tile_9 is down from tile_15, tile_18 is down from tile_24, tile_17 is down from tile_23, tile_8 is down from tile_14, tile_12 is down from tile_18, tile_14 is down from tile_20, tile_5 is down from tile_11, tile_10 is down from tile_16, tile_1 is down from tile_7, tile_13 is down from tile_19, tile_16 is down from tile_22, tile_7 is down from tile_13, tile_15 is down from tile_21, tile_11 is down from tile_17, tile_3 is down from tile_9, and tile_4 is down from tile_10 Currently, robot robot1 is at tile_7 and holding color black and robot robot2 is at tile_10 and holding color black; tile_17, tile_16, tile_13, tile_15, tile_1, tile_5, tile_2, tile_21, tile_12, tile_6, tile_9, tile_18, tile_22, tile_23, tile_4, tile_11, and tile_3 are clear; tile_8 is painted black, tile_24 is painted black, tile_20 is painted black, tile_19 is painted white, and tile_14 is painted white. The available actions are: (change-color ?r ?c ?c2) - modify the color of the robot ?r from ?c to ?c2, (paint-up ?r ?y ?x ?c) - paint tile ?y above tile ?x with color ?c using robot ?r, (paint-down ?r ?y ?x ?c) - use robot ?r to paint the tile ?y downwards from the tile ?x with the color ?c, (up ?r ?x ?y) - move robot ?r up from tile ?x to tile ?y, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - move the robot ?r from the tile ?x to the tile ?y which is to the right of the tile ?x, and (left ?r ?x ?y) - move the robot ?r from the tile ?x to the tile on its left ?y.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(up robot1 tile_8 tile_14)"], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-6-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_21 tile_22 tile_23 tile_24 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_11) (clear tile_12) (clear tile_13) (clear tile_15) (clear tile_16) (clear tile_17) (clear tile_18) (clear tile_2) (clear tile_21) (clear tile_22) (clear tile_23) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_6) (clear tile_9) (down tile_1 tile_7) (down tile_10 tile_16) (down tile_11 tile_17) (down tile_12 tile_18) (down tile_13 tile_19) (down tile_14 tile_20) (down tile_15 tile_21) (down tile_16 tile_22) (down tile_17 tile_23) (down tile_18 tile_24) (down tile_2 tile_8) (down tile_3 tile_9) (down tile_4 tile_10) (down tile_5 tile_11) (down tile_6 tile_12) (down tile_7 tile_13) (down tile_8 tile_14) (down tile_9 tile_15) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_15 tile_16) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_20 tile_21) (left tile_21 tile_22) (left tile_22 tile_23) (left tile_23 tile_24) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_14 white) (painted tile_19 white) (painted tile_20 black) (painted tile_24 black) (painted tile_8 black) (right tile_10 tile_9) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_16 tile_15) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_21 tile_20) (right tile_22 tile_21) (right tile_23 tile_22) (right tile_24 tile_23) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_7) (robot-at robot2 tile_10) (robot-has robot1 black) (robot-has robot2 black) (up tile_10 tile_4) (up tile_11 tile_5) (up tile_12 tile_6) (up tile_13 tile_7) (up tile_14 tile_8) (up tile_15 tile_9) (up tile_16 tile_10) (up tile_17 tile_11) (up tile_18 tile_12) (up tile_19 tile_13) (up tile_20 tile_14) (up tile_21 tile_15) (up tile_22 tile_16) (up tile_23 tile_17) (up tile_24 tile_18) (up tile_7 tile_1) (up tile_8 tile_2) (up tile_9 tile_3))\n    (:goal (and (painted tile_7 white) (painted tile_8 black) (painted tile_9 white) (painted tile_10 black) (painted tile_11 white) (painted tile_12 black) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 white) (painted tile_20 black) (painted tile_21 white) (painted tile_22 black) (painted tile_23 white) (painted tile_24 black)))\n)"}
{"id": -1696007418054062333, "group": "reachable_action_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 3 robots. The tiles locations are: tile_10 is to the right of tile_9, tile_12 is to the right of tile_11, tile_13 is to the right of tile_12, tile_18 is to the right of tile_17, tile_5 is to the right of tile_4, tile_9 is to the right of tile_8, tile_8 is to the right of tile_7, tile_4 is to the right of tile_3, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, tile_20 is to the right of tile_19, tile_19 is to the right of tile_18, tile_14 is to the right of tile_13, tile_7 is to the right of tile_6, tile_17 is to the right of tile_16, and tile_15 is to the right of tile_14. Further, tile_12 is down from tile_17, tile_6 is down from tile_11, tile_2 is down from tile_7, tile_4 is down from tile_9, tile_9 is down from tile_14, tile_13 is down from tile_18, tile_5 is down from tile_10, tile_7 is down from tile_12, tile_15 is down from tile_20, tile_10 is down from tile_15, tile_11 is down from tile_16, tile_3 is down from tile_8, tile_14 is down from tile_19, tile_8 is down from tile_13, and tile_1 is down from tile_6 Currently, robot robot1 is at tile_4 and holding color white, robot robot3 is at tile_5 and holding color white, and robot robot2 is at tile_1 and holding color white; tile_7, tile_3, tile_2, tile_6, and tile_12 are clear; tile_20 is painted white, tile_13 is painted black, tile_19 is painted black, tile_8 is painted white, tile_18 is painted white, tile_17 is painted black, tile_10 is painted white, tile_15 is painted black, tile_9 is painted black, tile_11 is painted black, tile_16 is painted white, and tile_14 is painted white. The available actions are: (change-color ?r ?c ?c2) - change the color of robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - paint the tile ?y above the tile ?x with color ?c using the robot ?r, (paint-down ?r ?y ?x ?c) - use robot ?r to paint the tile ?y downwards from the tile ?x with the color ?c, (up ?r ?x ?y) - move the robot ?r from tile ?x to tile ?y upwards, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - move the robot ?r from the tile ?x to the tile ?y which is to the right of the tile ?x, and (left ?r ?x ?y) - move the robot ?r from the tile ?x to the tile on its left ?y.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(up robot1 tile_5 tile_10)"], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-5-3)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 robot3 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_12) (clear tile_2) (clear tile_3) (clear tile_6) (clear tile_7) (down tile_1 tile_6) (down tile_10 tile_15) (down tile_11 tile_16) (down tile_12 tile_17) (down tile_13 tile_18) (down tile_14 tile_19) (down tile_15 tile_20) (down tile_2 tile_7) (down tile_3 tile_8) (down tile_4 tile_9) (down tile_5 tile_10) (down tile_6 tile_11) (down tile_7 tile_12) (down tile_8 tile_13) (down tile_9 tile_14) (left tile_1 tile_2) (left tile_11 tile_12) (left tile_12 tile_13) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_18 tile_19) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_10 white) (painted tile_11 black) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white) (painted tile_8 white) (painted tile_9 black) (right tile_10 tile_9) (right tile_12 tile_11) (right tile_13 tile_12) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_19 tile_18) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_7 tile_6) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_4) (robot-at robot2 tile_1) (robot-at robot3 tile_5) (robot-has robot1 white) (robot-has robot2 white) (robot-has robot3 white) (up tile_10 tile_5) (up tile_11 tile_6) (up tile_12 tile_7) (up tile_13 tile_8) (up tile_14 tile_9) (up tile_15 tile_10) (up tile_16 tile_11) (up tile_17 tile_12) (up tile_18 tile_13) (up tile_19 tile_14) (up tile_20 tile_15) (up tile_6 tile_1) (up tile_7 tile_2) (up tile_8 tile_3) (up tile_9 tile_4))\n    (:goal (and (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white)))\n)"}
{"id": -6245851188501842537, "group": "reachable_action_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 2 robots. The tiles locations are: tile_10 is to the right of tile_9, tile_12 is to the right of tile_11, tile_13 is to the right of tile_12, tile_18 is to the right of tile_17, tile_5 is to the right of tile_4, tile_9 is to the right of tile_8, tile_8 is to the right of tile_7, tile_4 is to the right of tile_3, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, tile_20 is to the right of tile_19, tile_19 is to the right of tile_18, tile_14 is to the right of tile_13, tile_7 is to the right of tile_6, tile_17 is to the right of tile_16, and tile_15 is to the right of tile_14. Further, tile_12 is down from tile_17, tile_6 is down from tile_11, tile_2 is down from tile_7, tile_4 is down from tile_9, tile_9 is down from tile_14, tile_13 is down from tile_18, tile_5 is down from tile_10, tile_7 is down from tile_12, tile_15 is down from tile_20, tile_10 is down from tile_15, tile_11 is down from tile_16, tile_3 is down from tile_8, tile_14 is down from tile_19, tile_8 is down from tile_13, and tile_1 is down from tile_6 Currently, robot robot2 is at tile_4 and holding color black and robot robot1 is at tile_9 and holding color white; tile_1, tile_5, tile_2, and tile_3 are clear; tile_12 is painted white, tile_20 is painted white, tile_13 is painted black, tile_19 is painted black, tile_8 is painted white, tile_18 is painted white, tile_17 is painted black, tile_10 is painted white, tile_7 is painted black, tile_15 is painted black, tile_11 is painted black, tile_16 is painted white, tile_6 is painted white, and tile_14 is painted white. The available actions are: (change-color ?r ?c ?c2) - alter the color of the robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - paint the tile ?y above the tile ?x with color ?c using the robot ?r, (paint-down ?r ?y ?x ?c) - apply color ?c to tile ?y which is below tile ?x using robot ?r, (up ?r ?x ?y) - move the robot ?r up from tile ?x to tile ?y, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - move robot ?r from tile ?x to the right tile tile ?y, and (left ?r ?x ?y) - move the robot ?r from the tile ?x to the tile on its left ?y.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(paint-up robot2 tile_11 tile_6 white)"], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-5-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_2) (clear tile_3) (clear tile_5) (down tile_1 tile_6) (down tile_10 tile_15) (down tile_11 tile_16) (down tile_12 tile_17) (down tile_13 tile_18) (down tile_14 tile_19) (down tile_15 tile_20) (down tile_2 tile_7) (down tile_3 tile_8) (down tile_4 tile_9) (down tile_5 tile_10) (down tile_6 tile_11) (down tile_7 tile_12) (down tile_8 tile_13) (down tile_9 tile_14) (left tile_1 tile_2) (left tile_11 tile_12) (left tile_12 tile_13) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_18 tile_19) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (right tile_10 tile_9) (right tile_12 tile_11) (right tile_13 tile_12) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_19 tile_18) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_7 tile_6) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_9) (robot-at robot2 tile_4) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_5) (up tile_11 tile_6) (up tile_12 tile_7) (up tile_13 tile_8) (up tile_14 tile_9) (up tile_15 tile_10) (up tile_16 tile_11) (up tile_17 tile_12) (up tile_18 tile_13) (up tile_19 tile_14) (up tile_20 tile_15) (up tile_6 tile_1) (up tile_7 tile_2) (up tile_8 tile_3) (up tile_9 tile_4))\n    (:goal (and (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white)))\n)"}
{"id": 5078994518683665143, "group": "reachable_action_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 2 robots. The tiles locations are: tile_10 is to the right of tile_9, tile_12 is to the right of tile_11, tile_13 is to the right of tile_12, tile_18 is to the right of tile_17, tile_5 is to the right of tile_4, tile_9 is to the right of tile_8, tile_8 is to the right of tile_7, tile_4 is to the right of tile_3, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, tile_20 is to the right of tile_19, tile_19 is to the right of tile_18, tile_14 is to the right of tile_13, tile_7 is to the right of tile_6, tile_17 is to the right of tile_16, and tile_15 is to the right of tile_14. Further, tile_12 is down from tile_17, tile_6 is down from tile_11, tile_2 is down from tile_7, tile_4 is down from tile_9, tile_9 is down from tile_14, tile_13 is down from tile_18, tile_5 is down from tile_10, tile_7 is down from tile_12, tile_15 is down from tile_20, tile_10 is down from tile_15, tile_11 is down from tile_16, tile_3 is down from tile_8, tile_14 is down from tile_19, tile_8 is down from tile_13, and tile_1 is down from tile_6 Currently, robot robot1 is at tile_1 and holding color black and robot robot2 is at tile_10 and holding color black; tile_14, tile_5, tile_2, tile_9, tile_8, tile_4, and tile_3 are clear; tile_12 is painted white, tile_20 is painted white, tile_13 is painted black, tile_19 is painted black, tile_18 is painted white, tile_17 is painted black, tile_7 is painted black, tile_15 is painted black, tile_11 is painted black, tile_16 is painted white, and tile_6 is painted white. The available actions are: (change-color ?r ?c ?c2) - alter the color of the robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - use robot ?r to paint the tile ?y above the tile ?x with the color ?c, (paint-down ?r ?y ?x ?c) - apply color ?c to tile ?y which is below tile ?x using robot ?r, (up ?r ?x ?y) - move robot ?r up from tile ?x to tile ?y, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - move robot ?r from tile ?x to the right tile tile ?y, and (left ?r ?x ?y) - move robot ?r from tile ?x to the left tile tile ?y.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(paint-up robot2 tile_6 tile_1 white)"], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-5-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_14) (clear tile_2) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_8) (clear tile_9) (down tile_1 tile_6) (down tile_10 tile_15) (down tile_11 tile_16) (down tile_12 tile_17) (down tile_13 tile_18) (down tile_14 tile_19) (down tile_15 tile_20) (down tile_2 tile_7) (down tile_3 tile_8) (down tile_4 tile_9) (down tile_5 tile_10) (down tile_6 tile_11) (down tile_7 tile_12) (down tile_8 tile_13) (down tile_9 tile_14) (left tile_1 tile_2) (left tile_11 tile_12) (left tile_12 tile_13) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_18 tile_19) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white) (painted tile_6 white) (painted tile_7 black) (right tile_10 tile_9) (right tile_12 tile_11) (right tile_13 tile_12) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_19 tile_18) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_7 tile_6) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_1) (robot-at robot2 tile_10) (robot-has robot1 black) (robot-has robot2 black) (up tile_10 tile_5) (up tile_11 tile_6) (up tile_12 tile_7) (up tile_13 tile_8) (up tile_14 tile_9) (up tile_15 tile_10) (up tile_16 tile_11) (up tile_17 tile_12) (up tile_18 tile_13) (up tile_19 tile_14) (up tile_20 tile_15) (up tile_6 tile_1) (up tile_7 tile_2) (up tile_8 tile_3) (up tile_9 tile_4))\n    (:goal (and (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white)))\n)"}
{"id": -5874268557439267747, "group": "reachable_action_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 12 tiles and 2 robots. \nThe tiles locations are: tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_5 is to the right of tile_4, tile_8 is to the right of tile_7, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, and tile_6 is to the right of tile_5. Further, tile_5 is down from tile_8, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_9 is down from tile_12, tile_8 is down from tile_11, tile_7 is down from tile_10, tile_2 is down from tile_5, tile_1 is down from tile_4, and tile_4 is down from tile_7 \nCurrently, robot robot1 is at tile_3 and holding color white and robot robot2 is at tile_6 and holding color black; tile_8, tile_5, tile_2, and tile_1 are clear; tile_7 is painted black, tile_12 is painted white, tile_4 is painted white, tile_9 is painted black, tile_11 is painted black, and tile_10 is painted white. The available actions are: (change-color ?r ?c ?c2) - modify the color of the robot ?r from ?c to ?c2, (paint-up ?r ?y ?x ?c) - use robot ?r to paint the tile ?y above the tile ?x with the color ?c, (paint-down ?r ?y ?x ?c) - apply color ?c to tile ?y which is below tile ?x using robot ?r, (up ?r ?x ?y) - move the robot ?r up from tile ?x to tile ?y, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - move the robot ?r from the tile ?x to the tile ?y which is to the right of the tile ?x, and (left ?r ?x ?y) - move robot ?r from tile ?x to the left tile tile ?y.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(left robot1 tile_3 tile_8)"], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_2) (clear tile_5) (clear tile_8) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_4 white) (painted tile_7 black) (painted tile_9 black) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_3) (robot-at robot2 tile_6) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": -9103010259519555823, "group": "reachable_action_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 16 tiles and 2 robots. \nThe tiles locations are: tile_10 is to the right of tile_9, tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_4 is to the right of tile_3, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, tile_14 is to the right of tile_13, tile_7 is to the right of tile_6, tile_16 is to the right of tile_15, and tile_15 is to the right of tile_14. Further, tile_6 is down from tile_10, tile_1 is down from tile_5, tile_7 is down from tile_11, tile_8 is down from tile_12, tile_5 is down from tile_9, tile_10 is down from tile_14, tile_2 is down from tile_6, tile_3 is down from tile_7, tile_12 is down from tile_16, tile_11 is down from tile_15, tile_9 is down from tile_13, and tile_4 is down from tile_8 \nCurrently, robot robot1 is at tile_2 and holding color white and robot robot2 is at tile_9 and holding color black; tile_16, tile_4, tile_5, tile_13, tile_7, tile_1, tile_6, tile_3, tile_11, tile_12, and tile_8 are clear; tile_15 is painted white, tile_14 is painted black, and tile_10 is painted white. The available actions are: (change-color ?r ?c ?c2) - change the color of robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - paint the tile ?y above the tile ?x with color ?c using the robot ?r, (paint-down ?r ?y ?x ?c) - apply color ?c to tile ?y which is below tile ?x using robot ?r, (up ?r ?x ?y) - move the robot ?r from tile ?x to tile ?y going upwards, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - move the robot ?r from tile ?x to the right tile ?y, and (left ?r ?x ?y) - move the robot ?r from the tile ?x to the tile on its left ?y.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(right robot2 tile_8 tile_10)"], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-4-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_11) (clear tile_12) (clear tile_13) (clear tile_16) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_6) (clear tile_7) (clear tile_8) (down tile_1 tile_5) (down tile_10 tile_14) (down tile_11 tile_15) (down tile_12 tile_16) (down tile_2 tile_6) (down tile_3 tile_7) (down tile_4 tile_8) (down tile_5 tile_9) (down tile_6 tile_10) (down tile_7 tile_11) (down tile_8 tile_12) (down tile_9 tile_13) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_15 tile_16) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_5 tile_6) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_9 tile_10) (painted tile_10 white) (painted tile_14 black) (painted tile_15 white) (right tile_10 tile_9) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_16 tile_15) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_6 tile_5) (right tile_7 tile_6) (right tile_8 tile_7) (robot-at robot1 tile_2) (robot-at robot2 tile_9) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_6) (up tile_11 tile_7) (up tile_12 tile_8) (up tile_13 tile_9) (up tile_14 tile_10) (up tile_15 tile_11) (up tile_16 tile_12) (up tile_5 tile_1) (up tile_6 tile_2) (up tile_7 tile_3) (up tile_8 tile_4) (up tile_9 tile_5))\n    (:goal (and (painted tile_5 white) (painted tile_6 black) (painted tile_7 white) (painted tile_8 black) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 white) (painted tile_14 black) (painted tile_15 white) (painted tile_16 black)))\n)"}
{"id": 7775947109330558669, "group": "reachable_action_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 9 tiles and 2 robots. \nThe tiles locations are: tile_5 is to the right of tile_4, tile_8 is to the right of tile_7, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, and tile_6 is to the right of tile_5. Further, tile_5 is down from tile_8, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_2 is down from tile_5, tile_1 is down from tile_4, and tile_4 is down from tile_7 \nCurrently, robot robot2 is at tile_4 and holding color black and robot robot1 is at tile_9 and holding color black; tile_2, tile_1, tile_6, and tile_3 are clear; tile_7 is painted black, tile_8 is painted white, and tile_5 is painted black. The available actions are: (change-color ?r ?c ?c2) - change the color of robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - apply color ?c to tile ?y above tile ?x using robot ?r, (paint-down ?r ?y ?x ?c) - use robot ?r to paint the tile ?y downwards from the tile ?x with the color ?c, (up ?r ?x ?y) - move the robot ?r from tile ?x to tile ?y going upwards, (down ?r ?x ?y) - move the robot ?r from the tile ?x to the tile ?y going downwards, (right ?r ?x ?y) - move robot ?r from tile ?x to the right tile tile ?y, and (left ?r ?x ?y) - move the robot ?r from tile ?x to the left tile ?y.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(right robot1 tile_5 tile_9)"], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_2) (clear tile_3) (clear tile_6) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_5 black) (painted tile_7 black) (painted tile_8 white) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_9) (robot-at robot2 tile_4) (robot-has robot1 black) (robot-has robot2 black) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": 7134233130397366402, "group": "reachable_action_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 12 tiles and 2 robots. \nThe tiles locations are: tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_5 is to the right of tile_4, tile_8 is to the right of tile_7, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, and tile_6 is to the right of tile_5. Further, tile_5 is down from tile_8, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_9 is down from tile_12, tile_8 is down from tile_11, tile_7 is down from tile_10, tile_2 is down from tile_5, tile_1 is down from tile_4, and tile_4 is down from tile_7 \nCurrently, robot robot1 is at tile_6 and holding color white and robot robot2 is at tile_11 and holding color black; tile_4, tile_2, tile_5, tile_7, tile_1, tile_9, tile_3, tile_10, and tile_8 are clear; tile_12 is painted white. The available actions are: (change-color ?r ?c ?c2) - modify the color of the robot ?r from ?c to ?c2, (paint-up ?r ?y ?x ?c) - use robot ?r to paint the tile ?y above the tile ?x with the color ?c, (paint-down ?r ?y ?x ?c) - paint tile ?y down from tile ?x with color ?c using robot ?r, (up ?r ?x ?y) - move the robot ?r from tile ?x to tile ?y going upwards, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - move robot ?r from tile ?x to the right tile tile ?y, and (left ?r ?x ?y) - move the robot ?r from tile ?x to the left tile ?y.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(left robot2 tile_10 tile_6)"], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_10) (clear tile_2) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_7) (clear tile_8) (clear tile_9) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_12 white) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_6) (robot-at robot2 tile_11) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": 7858697430857451875, "group": "reachable_action_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 12 tiles and 2 robots. \nThe tiles locations are: tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_5 is to the right of tile_4, tile_8 is to the right of tile_7, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, and tile_6 is to the right of tile_5. Further, tile_5 is down from tile_8, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_9 is down from tile_12, tile_8 is down from tile_11, tile_7 is down from tile_10, tile_2 is down from tile_5, tile_1 is down from tile_4, and tile_4 is down from tile_7 \nCurrently, robot robot1 is at tile_3 and holding color white and robot robot2 is at tile_5 and holding color black; tile_8, tile_2, and tile_1 are clear; tile_7 is painted black, tile_12 is painted white, tile_4 is painted white, tile_9 is painted black, tile_6 is painted white, tile_11 is painted black, and tile_10 is painted white. The available actions are: (change-color ?r ?c ?c2) - change the color of robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - apply color ?c to tile ?y above tile ?x using robot ?r, (paint-down ?r ?y ?x ?c) - paint tile ?y down from tile ?x with color ?c using robot ?r, (up ?r ?x ?y) - move robot ?r up from tile ?x to tile ?y, (down ?r ?x ?y) - move the robot ?r from the tile ?x to the tile ?y going downwards, (right ?r ?x ?y) - move the robot ?r from the tile ?x to the tile ?y which is to the right of the tile ?x, and (left ?r ?x ?y) - move the robot ?r from the tile ?x to the tile on its left ?y.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(paint-up robot1 tile_6 tile_4 white)"], "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_2) (clear tile_8) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_4 white) (painted tile_6 white) (painted tile_7 black) (painted tile_9 black) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_3) (robot-at robot2 tile_5) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
