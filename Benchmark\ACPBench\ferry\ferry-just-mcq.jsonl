{"id": 3065073662335840940, "group": "action_justification_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c1 is at l0; c2 and c0 are at l4. The goal is to reach a state where the following facts hold: Car c0 is at location l3, Car c2 is at location l3, and Car c1 is at location l3.", "question": "Given the plan: \"sail from location l0 to location l2, sail from location l2 to location l0, embark the car c1 at location l0 on to the ferry, sail from location l0 to location l3, debark the car c1 from the ferry to location l3, sail from location l3 to location l4, embark the car c0 at location l4 on to the ferry, sail from location l4 to location l0, sail from location l0 to location l3, debark the car c0 from the ferry to location l3, sail from location l3 to location l4, embark the car c2 at location l4 on to the ferry, sail from location l4 to location l3, debark the car c2 from the ferry to location l3\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. sail from location l4 to location l3 and debark the car c2 from the ferry to location l3. B. sail from location l0 to location l3 and debark the car c0 from the ferry to location l3. C. sail from location l4 to location l0 and sail from location l0 to location l3. D. sail from location l0 to location l2 and sail from location l2 to location l0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["sail from location l4 to location l3 and debark the car c2 from the ferry to location l3", "sail from location l0 to location l3 and debark the car c0 from the ferry to location l3", "sail from location l4 to location l0 and sail from location l0 to location l3", "sail from location l0 to location l2 and sail from location l2 to location l0"]}, "query": "Given the plan: \"sail from location l0 to location l2, sail from location l2 to location l0, embark the car c1 at location l0 on to the ferry, sail from location l0 to location l3, debark the car c1 from the ferry to location l3, sail from location l3 to location l4, embark the car c0 at location l4 on to the ferry, sail from location l4 to location l0, sail from location l0 to location l3, debark the car c0 from the ferry to location l3, sail from location l3 to location l4, embark the car c2 at location l4 on to the ferry, sail from location l4 to location l3, debark the car c2 from the ferry to location l3\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
{"id": -739190100517546532, "group": "action_justification_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c8, c1, c7, c0, c4, and c5 are at l1; c2, c9, and c6 are at l0; c3 is at l2. The goal is to reach a state where the following facts hold: Car c8 is at location l2, Car c6 is at location l2, Car c1 is at location l1, Car c4 is at location l0, Car c3 is at location l2, Car c2 is at location l1, Car c5 is at location l2, Car c7 is at location l0, Car c9 is at location l0, and Car c0 is at location l2.", "question": "Given the plan: \"travel by sea from location l2 to location l1, board the car c0 at the location l1, travel by sea from location l1 to location l0, debark the car c0 from the ferry to location l0, board the car c0 at the location l0, travel by sea from location l0 to location l2, debark the car c0 from the ferry to location l2, travel by sea from location l2 to location l1, board the car c8 at the location l1, travel by sea from location l1 to location l2, debark the car c8 from the ferry to location l2, travel by sea from location l2 to location l1, board the car c4 at the location l1, travel by sea from location l1 to location l0, debark the car c4 from the ferry to location l0, board the car c2 at the location l0, travel by sea from location l0 to location l1, debark the car c2 from the ferry to location l1, board the car c5 at the location l1, travel by sea from location l1 to location l2, debark the car c5 from the ferry to location l2, travel by sea from location l2 to location l1, board the car c7 at the location l1, travel by sea from location l1 to location l0, debark the car c7 from the ferry to location l0, board the car c6 at the location l0, travel by sea from location l0 to location l2, debark the car c6 from the ferry to location l2\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. board the car c0 at the location l1 and travel by sea from location l1 to location l0. B. board the car c0 at the location l0 and travel by sea from location l0 to location l2. C. board the car c6 at the location l0 and travel by sea from location l0 to location l2. D. debark the car c0 from the ferry to location l0 and board the car c0 at the location l0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["board the car c0 at the location l1 and travel by sea from location l1 to location l0", "board the car c0 at the location l0 and travel by sea from location l0 to location l2", "board the car c6 at the location l0 and travel by sea from location l0 to location l2", "debark the car c0 from the ferry to location l0 and board the car c0 at the location l0"]}, "query": "Given the plan: \"travel by sea from location l2 to location l1, board the car c0 at the location l1, travel by sea from location l1 to location l0, debark the car c0 from the ferry to location l0, board the car c0 at the location l0, travel by sea from location l0 to location l2, debark the car c0 from the ferry to location l2, travel by sea from location l2 to location l1, board the car c8 at the location l1, travel by sea from location l1 to location l2, debark the car c8 from the ferry to location l2, travel by sea from location l2 to location l1, board the car c4 at the location l1, travel by sea from location l1 to location l0, debark the car c4 from the ferry to location l0, board the car c2 at the location l0, travel by sea from location l0 to location l1, debark the car c2 from the ferry to location l1, board the car c5 at the location l1, travel by sea from location l1 to location l2, debark the car c5 from the ferry to location l2, travel by sea from location l2 to location l1, board the car c7 at the location l1, travel by sea from location l1 to location l0, debark the car c7 from the ferry to location l0, board the car c6 at the location l0, travel by sea from location l0 to location l2, debark the car c6 from the ferry to location l2\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
{"id": -268982959346488262, "group": "action_justification_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c22, c37, c5, c48, c26, c13, c43, c19, c40, c38, c27, c25, c46, c39, c47, c12, c21, c7, c45, c49, c28, c36, c41, c42, c31, c32, c4, c24, and c34 are at l1; c33, c29, c3, c0, c23, c16, c11, c14, c1, c6, c17, c15, c20, c9, c2, c35, c18, c30, c8, c10, and c44 are at l0. The goal is to reach a state where the following facts hold: Car c21 is at location l0, Car c1 is at location l1, Car c38 is at location l0, Car c6 is at location l1, Car c37 is at location l0, Car c29 is at location l0, Car c43 is at location l0, Car c0 is at location l0, Car c10 is at location l1, Car c5 is at location l1, Car c26 is at location l0, Car c13 is at location l0, Car c23 is at location l1, Car c48 is at location l1, Car c35 is at location l1, Car c22 is at location l0, Car c11 is at location l0, Car c12 is at location l0, Car c14 is at location l0, Car c27 is at location l1, Car c39 is at location l1, Car c47 is at location l1, Car c7 is at location l1, Car c20 is at location l0, Car c28 is at location l1, Car c3 is at location l1, Car c18 is at location l1, Car c49 is at location l1, Car c25 is at location l0, Car c9 is at location l0, Car c40 is at location l0, Car c45 is at location l0, Car c17 is at location l1, Car c36 is at location l0, Car c41 is at location l1, Car c44 is at location l0, Car c16 is at location l1, Car c42 is at location l1, Car c31 is at location l1, Car c2 is at location l0, Car c33 is at location l1, Car c30 is at location l0, Car c19 is at location l0, Car c8 is at location l0, Car c4 is at location l1, Car c32 is at location l1, Car c46 is at location l0, Car c24 is at location l1, Car c34 is at location l1, and Car c15 is at location l1.", "question": "Given the plan: \"board the car c12 at the location l1, travel by sea from location l1 to location l0, debark car c12 to location l0 from the ferry, board the car c23 at the location l0, travel by sea from location l0 to location l1, debark car c23 to location l1 from the ferry, board the car c13 at the location l1, travel by sea from location l1 to location l0, debark car c13 to location l0 from the ferry, board the car c1 at the location l0, travel by sea from location l0 to location l1, debark car c1 to location l1 from the ferry, board the car c19 at the location l1, travel by sea from location l1 to location l0, debark car c19 to location l0 from the ferry, board the car c10 at the location l0, travel by sea from location l0 to location l1, debark car c10 to location l1 from the ferry, board the car c21 at the location l1, travel by sea from location l1 to location l0, debark car c21 to location l0 from the ferry, board the car c15 at the location l0, travel by sea from location l0 to location l1, debark car c15 to location l1 from the ferry, board the car c22 at the location l1, travel by sea from location l1 to location l0, debark car c22 to location l0 from the ferry, board the car c16 at the location l0, travel by sea from location l0 to location l1, debark car c16 to location l1 from the ferry, board the car c25 at the location l1, travel by sea from location l1 to location l0, debark car c25 to location l0 from the ferry, board the car c17 at the location l0, travel by sea from location l0 to location l1, debark car c17 to location l1 from the ferry, board the car c26 at the location l1, travel by sea from location l1 to location l0, debark car c26 to location l0 from the ferry, board the car c18 at the location l0, travel by sea from location l0 to location l1, debark car c18 to location l1 from the ferry, board the car c36 at the location l1, travel by sea from location l1 to location l0, debark car c36 to location l0 from the ferry, board the car c3 at the location l0, travel by sea from location l0 to location l1, debark car c3 to location l1 from the ferry, board the car c37 at the location l1, travel by sea from location l1 to location l0, debark car c37 to location l0 from the ferry, board the car c33 at the location l0, travel by sea from location l0 to location l1, debark car c33 to location l1 from the ferry, board the car c38 at the location l1, travel by sea from location l1 to location l0, debark car c38 to location l0 from the ferry, board the car c35 at the location l0, travel by sea from location l0 to location l1, debark car c35 to location l1 from the ferry, board the car c40 at the location l1, travel by sea from location l1 to location l0, debark car c40 to location l0 from the ferry, board the car c6 at the location l0, travel by sea from location l0 to location l1, debark car c6 to location l1 from the ferry, board the car c43 at the location l1, travel by sea from location l1 to location l0, debark car c43 to location l0 from the ferry, travel by sea from location l0 to location l1, board the car c45 at the location l1, travel by sea from location l1 to location l0, debark car c45 to location l0 from the ferry, travel by sea from location l0 to location l1, board the car c46 at the location l1, travel by sea from location l1 to location l0, debark car c46 to location l0 from the ferry, travel by sea from location l0 to location l1\"; which of the following actions can be removed from this plan and still have a valid plan? A. travel by sea from location l0 to location l1. B. travel by sea from location l1 to location l0. C. board the car c15 at the location l0. D. board the car c17 at the location l0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["travel by sea from location l0 to location l1", "travel by sea from location l1 to location l0", "board the car c15 at the location l0", "board the car c17 at the location l0"]}, "query": "Given the plan: \"board the car c12 at the location l1, travel by sea from location l1 to location l0, debark car c12 to location l0 from the ferry, board the car c23 at the location l0, travel by sea from location l0 to location l1, debark car c23 to location l1 from the ferry, board the car c13 at the location l1, travel by sea from location l1 to location l0, debark car c13 to location l0 from the ferry, board the car c1 at the location l0, travel by sea from location l0 to location l1, debark car c1 to location l1 from the ferry, board the car c19 at the location l1, travel by sea from location l1 to location l0, debark car c19 to location l0 from the ferry, board the car c10 at the location l0, travel by sea from location l0 to location l1, debark car c10 to location l1 from the ferry, board the car c21 at the location l1, travel by sea from location l1 to location l0, debark car c21 to location l0 from the ferry, board the car c15 at the location l0, travel by sea from location l0 to location l1, debark car c15 to location l1 from the ferry, board the car c22 at the location l1, travel by sea from location l1 to location l0, debark car c22 to location l0 from the ferry, board the car c16 at the location l0, travel by sea from location l0 to location l1, debark car c16 to location l1 from the ferry, board the car c25 at the location l1, travel by sea from location l1 to location l0, debark car c25 to location l0 from the ferry, board the car c17 at the location l0, travel by sea from location l0 to location l1, debark car c17 to location l1 from the ferry, board the car c26 at the location l1, travel by sea from location l1 to location l0, debark car c26 to location l0 from the ferry, board the car c18 at the location l0, travel by sea from location l0 to location l1, debark car c18 to location l1 from the ferry, board the car c36 at the location l1, travel by sea from location l1 to location l0, debark car c36 to location l0 from the ferry, board the car c3 at the location l0, travel by sea from location l0 to location l1, debark car c3 to location l1 from the ferry, board the car c37 at the location l1, travel by sea from location l1 to location l0, debark car c37 to location l0 from the ferry, board the car c33 at the location l0, travel by sea from location l0 to location l1, debark car c33 to location l1 from the ferry, board the car c38 at the location l1, travel by sea from location l1 to location l0, debark car c38 to location l0 from the ferry, board the car c35 at the location l0, travel by sea from location l0 to location l1, debark car c35 to location l1 from the ferry, board the car c40 at the location l1, travel by sea from location l1 to location l0, debark car c40 to location l0 from the ferry, board the car c6 at the location l0, travel by sea from location l0 to location l1, debark car c6 to location l1 from the ferry, board the car c43 at the location l1, travel by sea from location l1 to location l0, debark car c43 to location l0 from the ferry, travel by sea from location l0 to location l1, board the car c45 at the location l1, travel by sea from location l1 to location l0, debark car c45 to location l0 from the ferry, travel by sea from location l0 to location l1, board the car c46 at the location l1, travel by sea from location l1 to location l0, debark car c46 to location l0 from the ferry, travel by sea from location l0 to location l1\"; which action can be removed from this plan?", "answer": "A"}
{"id": 1233115037506411455, "group": "action_justification_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c8, c1, c7, c0, c4, and c5 are at l1; c2, c9, and c6 are at l0; c3 is at l2. The goal is to reach a state where the following facts hold: Car c8 is at location l2, Car c6 is at location l2, Car c1 is at location l1, Car c4 is at location l0, Car c3 is at location l2, Car c2 is at location l1, Car c5 is at location l2, Car c7 is at location l0, Car c9 is at location l0, and Car c0 is at location l2.", "question": "Given the plan: \"travel by sea from location l2 to location l1, board the car c4 at the location l1, travel by sea from location l1 to location l2, travel by sea from location l2 to location l0, unload the car c4 from the ferry to location l0, board the car c6 at the location l0, travel by sea from location l0 to location l2, unload the car c6 from the ferry to location l2, travel by sea from location l2 to location l1, board the car c7 at the location l1, travel by sea from location l1 to location l2, travel by sea from location l2 to location l0, unload the car c7 from the ferry to location l0, board the car c2 at the location l0, travel by sea from location l0 to location l1, unload the car c2 from the ferry to location l1, board the car c0 at the location l1, travel by sea from location l1 to location l2, unload the car c0 from the ferry to location l2, travel by sea from location l2 to location l1, board the car c8 at the location l1, travel by sea from location l1 to location l2, unload the car c8 from the ferry to location l2, travel by sea from location l2 to location l1, board the car c5 at the location l1, travel by sea from location l1 to location l2, unload the car c5 from the ferry to location l2, travel by sea from location l2 to location l0\"; which of the following actions can be removed from this plan and still have a valid plan? A. unload the car c4 from the ferry to location l0. B. travel by sea from location l2 to location l0. C. unload the car c8 from the ferry to location l2. D. travel by sea from location l1 to location l2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["unload the car c4 from the ferry to location l0", "travel by sea from location l2 to location l0", "unload the car c8 from the ferry to location l2", "travel by sea from location l1 to location l2"]}, "query": "Given the plan: \"travel by sea from location l2 to location l1, board the car c4 at the location l1, travel by sea from location l1 to location l2, travel by sea from location l2 to location l0, unload the car c4 from the ferry to location l0, board the car c6 at the location l0, travel by sea from location l0 to location l2, unload the car c6 from the ferry to location l2, travel by sea from location l2 to location l1, board the car c7 at the location l1, travel by sea from location l1 to location l2, travel by sea from location l2 to location l0, unload the car c7 from the ferry to location l0, board the car c2 at the location l0, travel by sea from location l0 to location l1, unload the car c2 from the ferry to location l1, board the car c0 at the location l1, travel by sea from location l1 to location l2, unload the car c0 from the ferry to location l2, travel by sea from location l2 to location l1, board the car c8 at the location l1, travel by sea from location l1 to location l2, unload the car c8 from the ferry to location l2, travel by sea from location l2 to location l1, board the car c5 at the location l1, travel by sea from location l1 to location l2, unload the car c5 from the ferry to location l2, travel by sea from location l2 to location l0\"; which action can be removed from this plan?", "answer": "B"}
{"id": 4702865142048811335, "group": "action_justification_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c45, c36, c20, c0, c2, c28, c10, c26, c47, c35, c38, and c14 are at l4; c4, c42, c31, c39, c7, c15, c37, c43, c23, c29, c22, c5, c49, and c3 are at l3; c48, c13, c19, c46, c21, c33, c44, c32, c24, and c34 are at l1; c8, c30, c12, and c16 are at l2; c11, c1, c41, c17, c6, c25, c9, c40, c18, and c27 are at l0. The goal is to reach a state where the following facts hold: Car c43 is at location l2, Car c26 is at location l2, Car c7 is at location l0, Car c8 is at location l4, Car c31 is at location l3, Car c9 is at location l4, Car c39 is at location l2, Car c0 is at location l2, Car c42 is at location l0, Car c25 is at location l3, Car c48 is at location l0, Car c16 is at location l0, Car c11 is at location l1, Car c37 is at location l3, Car c19 is at location l1, Car c41 is at location l4, Car c4 is at location l0, Car c28 is at location l4, Car c20 is at location l3, Car c13 is at location l3, Car c2 is at location l1, Car c1 is at location l0, Car c23 is at location l3, Car c17 is at location l0, Car c29 is at location l4, Car c6 is at location l3, Car c24 is at location l0, Car c12 is at location l3, Car c46 is at location l4, Car c47 is at location l0, Car c49 is at location l1, Car c38 is at location l4, Car c45 is at location l0, Car c36 is at location l0, Car c44 is at location l0, Car c5 is at location l4, Car c22 is at location l2, Car c30 is at location l4, Car c35 is at location l2, Car c33 is at location l1, Car c14 is at location l4, Car c18 is at location l0, Car c27 is at location l4, Car c40 is at location l2, Car c3 is at location l2, Car c32 is at location l1, Car c10 is at location l0, Car c34 is at location l3, Car c21 is at location l2, and Car c15 is at location l1.", "question": "Given the plan: \"load the car c13 at location l1 on to the ferry, sail from location l1 to location l3, debark the car c13 from the ferry to location l3, load the car c22 at location l3 on to the ferry, sail from location l3 to location l2, debark the car c22 from the ferry to location l2, load the car c16 at location l2 on to the ferry, sail from location l2 to location l0, debark the car c16 from the ferry to location l0, load the car c27 at location l0 on to the ferry, sail from location l0 to location l4, debark the car c27 from the ferry to location l4, load the car c10 at location l4 on to the ferry, sail from location l4 to location l0, debark the car c10 from the ferry to location l0, load the car c40 at location l0 on to the ferry, sail from location l0 to location l2, debark the car c40 from the ferry to location l2, sail from location l2 to location l4, load the car c2 at location l4 on to the ferry, sail from location l4 to location l1, debark the car c2 from the ferry to location l1, load the car c21 at location l1 on to the ferry, sail from location l1 to location l2, debark the car c21 from the ferry to location l2, sail from location l2 to location l4, load the car c20 at location l4 on to the ferry, sail from location l4 to location l3, debark the car c20 from the ferry to location l3, load the car c15 at location l3 on to the ferry, sail from location l3 to location l0, sail from location l0 to location l1, debark the car c15 from the ferry to location l1, load the car c24 at location l1 on to the ferry, sail from location l1 to location l0, debark the car c24 from the ferry to location l0, load the car c41 at location l0 on to the ferry, sail from location l0 to location l4, debark the car c41 from the ferry to location l4, load the car c36 at location l4 on to the ferry, sail from location l4 to location l0, debark the car c36 from the ferry to location l0, load the car c9 at location l0 on to the ferry, sail from location l0 to location l4, debark the car c9 from the ferry to location l4, load the car c45 at location l4 on to the ferry, sail from location l4 to location l0, debark the car c45 from the ferry to location l0, load the car c11 at location l0 on to the ferry, sail from location l0 to location l1, debark the car c11 from the ferry to location l1, load the car c34 at location l1 on to the ferry, sail from location l1 to location l0, sail from location l0 to location l3, debark the car c34 from the ferry to location l3, load the car c29 at location l3 on to the ferry, sail from location l3 to location l0, sail from location l0 to location l4, debark the car c29 from the ferry to location l4, load the car c47 at location l4 on to the ferry, sail from location l4 to location l0, debark the car c47 from the ferry to location l0, load the car c25 at location l0 on to the ferry, sail from location l0 to location l1, sail from location l1 to location l3, debark the car c25 from the ferry to location l3, load the car c39 at location l3 on to the ferry, sail from location l3 to location l4, sail from location l4 to location l2, debark the car c39 from the ferry to location l2, load the car c12 at location l2 on to the ferry, sail from location l2 to location l3, debark the car c12 from the ferry to location l3, load the car c43 at location l3 on to the ferry, sail from location l3 to location l4, sail from location l4 to location l2, debark the car c43 from the ferry to location l2, load the car c30 at location l2 on to the ferry, sail from location l2 to location l3, sail from location l3 to location l4, debark the car c30 from the ferry to location l4, load the car c0 at location l4 on to the ferry, sail from location l4 to location l2, debark the car c0 from the ferry to location l2, load the car c8 at location l2 on to the ferry, sail from location l2 to location l3, sail from location l3 to location l4, debark the car c8 from the ferry to location l4, load the car c26 at location l4 on to the ferry, sail from location l4 to location l2, debark the car c26 from the ferry to location l2, sail from location l2 to location l3, load the car c4 at location l3 on to the ferry, sail from location l3 to location l0, debark the car c4 from the ferry to location l0, load the car c6 at location l0 on to the ferry, sail from location l0 to location l1, sail from location l1 to location l3, debark the car c6 from the ferry to location l3, load the car c42 at location l3 on to the ferry, sail from location l3 to location l0, debark the car c42 from the ferry to location l0, sail from location l0 to location l1, load the car c44 at location l1 on to the ferry, sail from location l1 to location l0, debark the car c44 from the ferry to location l0, sail from location l0 to location l1, load the car c46 at location l1 on to the ferry, sail from location l1 to location l2, sail from location l2 to location l4, debark the car c46 from the ferry to location l4, sail from location l4 to location l3, load the car c3 at location l3 on to the ferry, sail from location l3 to location l4, debark the car c3 from the ferry to location l4, load the car c3 at location l4 on to the ferry, sail from location l4 to location l2, debark the car c3 from the ferry to location l2, sail from location l2 to location l1, load the car c48 at location l1 on to the ferry, sail from location l1 to location l0, debark the car c48 from the ferry to location l0, sail from location l0 to location l3, load the car c49 at location l3 on to the ferry, sail from location l3 to location l1, debark the car c49 from the ferry to location l1, sail from location l1 to location l0, sail from location l0 to location l3, load the car c5 at location l3 on to the ferry, sail from location l3 to location l4, debark the car c5 from the ferry to location l4, load the car c35 at location l4 on to the ferry, sail from location l4 to location l0, debark the car c35 from the ferry to location l0, sail from location l0 to location l3, load the car c7 at location l3 on to the ferry, sail from location l3 to location l0, debark the car c7 from the ferry to location l0, load the car c35 at location l0 on to the ferry, sail from location l0 to location l2, debark the car c35 from the ferry to location l2\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. sail from location l3 to location l1 and debark the car c49 from the ferry to location l1. B. load the car c41 at location l0 on to the ferry and sail from location l0 to location l4. C. sail from location l3 to location l0 and sail from location l0 to location l1. D. debark the car c3 from the ferry to location l4 and load the car c3 at location l4 on to the ferry.", "choices": {"label": ["A", "B", "C", "D"], "text": ["sail from location l3 to location l1 and debark the car c49 from the ferry to location l1", "load the car c41 at location l0 on to the ferry and sail from location l0 to location l4", "sail from location l3 to location l0 and sail from location l0 to location l1", "debark the car c3 from the ferry to location l4 and load the car c3 at location l4 on to the ferry"]}, "query": "Given the plan: \"load the car c13 at location l1 on to the ferry, sail from location l1 to location l3, debark the car c13 from the ferry to location l3, load the car c22 at location l3 on to the ferry, sail from location l3 to location l2, debark the car c22 from the ferry to location l2, load the car c16 at location l2 on to the ferry, sail from location l2 to location l0, debark the car c16 from the ferry to location l0, load the car c27 at location l0 on to the ferry, sail from location l0 to location l4, debark the car c27 from the ferry to location l4, load the car c10 at location l4 on to the ferry, sail from location l4 to location l0, debark the car c10 from the ferry to location l0, load the car c40 at location l0 on to the ferry, sail from location l0 to location l2, debark the car c40 from the ferry to location l2, sail from location l2 to location l4, load the car c2 at location l4 on to the ferry, sail from location l4 to location l1, debark the car c2 from the ferry to location l1, load the car c21 at location l1 on to the ferry, sail from location l1 to location l2, debark the car c21 from the ferry to location l2, sail from location l2 to location l4, load the car c20 at location l4 on to the ferry, sail from location l4 to location l3, debark the car c20 from the ferry to location l3, load the car c15 at location l3 on to the ferry, sail from location l3 to location l0, sail from location l0 to location l1, debark the car c15 from the ferry to location l1, load the car c24 at location l1 on to the ferry, sail from location l1 to location l0, debark the car c24 from the ferry to location l0, load the car c41 at location l0 on to the ferry, sail from location l0 to location l4, debark the car c41 from the ferry to location l4, load the car c36 at location l4 on to the ferry, sail from location l4 to location l0, debark the car c36 from the ferry to location l0, load the car c9 at location l0 on to the ferry, sail from location l0 to location l4, debark the car c9 from the ferry to location l4, load the car c45 at location l4 on to the ferry, sail from location l4 to location l0, debark the car c45 from the ferry to location l0, load the car c11 at location l0 on to the ferry, sail from location l0 to location l1, debark the car c11 from the ferry to location l1, load the car c34 at location l1 on to the ferry, sail from location l1 to location l0, sail from location l0 to location l3, debark the car c34 from the ferry to location l3, load the car c29 at location l3 on to the ferry, sail from location l3 to location l0, sail from location l0 to location l4, debark the car c29 from the ferry to location l4, load the car c47 at location l4 on to the ferry, sail from location l4 to location l0, debark the car c47 from the ferry to location l0, load the car c25 at location l0 on to the ferry, sail from location l0 to location l1, sail from location l1 to location l3, debark the car c25 from the ferry to location l3, load the car c39 at location l3 on to the ferry, sail from location l3 to location l4, sail from location l4 to location l2, debark the car c39 from the ferry to location l2, load the car c12 at location l2 on to the ferry, sail from location l2 to location l3, debark the car c12 from the ferry to location l3, load the car c43 at location l3 on to the ferry, sail from location l3 to location l4, sail from location l4 to location l2, debark the car c43 from the ferry to location l2, load the car c30 at location l2 on to the ferry, sail from location l2 to location l3, sail from location l3 to location l4, debark the car c30 from the ferry to location l4, load the car c0 at location l4 on to the ferry, sail from location l4 to location l2, debark the car c0 from the ferry to location l2, load the car c8 at location l2 on to the ferry, sail from location l2 to location l3, sail from location l3 to location l4, debark the car c8 from the ferry to location l4, load the car c26 at location l4 on to the ferry, sail from location l4 to location l2, debark the car c26 from the ferry to location l2, sail from location l2 to location l3, load the car c4 at location l3 on to the ferry, sail from location l3 to location l0, debark the car c4 from the ferry to location l0, load the car c6 at location l0 on to the ferry, sail from location l0 to location l1, sail from location l1 to location l3, debark the car c6 from the ferry to location l3, load the car c42 at location l3 on to the ferry, sail from location l3 to location l0, debark the car c42 from the ferry to location l0, sail from location l0 to location l1, load the car c44 at location l1 on to the ferry, sail from location l1 to location l0, debark the car c44 from the ferry to location l0, sail from location l0 to location l1, load the car c46 at location l1 on to the ferry, sail from location l1 to location l2, sail from location l2 to location l4, debark the car c46 from the ferry to location l4, sail from location l4 to location l3, load the car c3 at location l3 on to the ferry, sail from location l3 to location l4, debark the car c3 from the ferry to location l4, load the car c3 at location l4 on to the ferry, sail from location l4 to location l2, debark the car c3 from the ferry to location l2, sail from location l2 to location l1, load the car c48 at location l1 on to the ferry, sail from location l1 to location l0, debark the car c48 from the ferry to location l0, sail from location l0 to location l3, load the car c49 at location l3 on to the ferry, sail from location l3 to location l1, debark the car c49 from the ferry to location l1, sail from location l1 to location l0, sail from location l0 to location l3, load the car c5 at location l3 on to the ferry, sail from location l3 to location l4, debark the car c5 from the ferry to location l4, load the car c35 at location l4 on to the ferry, sail from location l4 to location l0, debark the car c35 from the ferry to location l0, sail from location l0 to location l3, load the car c7 at location l3 on to the ferry, sail from location l3 to location l0, debark the car c7 from the ferry to location l0, load the car c35 at location l0 on to the ferry, sail from location l0 to location l2, debark the car c35 from the ferry to location l2\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
{"id": -3502759454875738788, "group": "action_justification_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 3 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c2 is at l1; c1 and c0 are at l0. The goal is to reach a state where the following facts hold: Car c2 is at location l1, Car c1 is at location l0, and Car c0 is at location l1.", "question": "Given the plan: \"travel by sea from location l1 to location l0, embark the car c0 at location l0 on to the ferry, debark the car c0 from the ferry to location l0, travel by sea from location l0 to location l1, travel by sea from location l1 to location l0, embark the car c0 at location l0 on to the ferry, travel by sea from location l0 to location l1, travel by sea from location l1 to location l0, travel by sea from location l0 to location l1, debark the car c0 from the ferry to location l1, travel by sea from location l1 to location l0, travel by sea from location l0 to location l1\"; which of the following actions can be removed from this plan and still have a valid plan? A. travel by sea from location l1 to location l0. B. travel by sea from location l0 to location l1. C. travel by sea from location l1 to location l0. D. debark the car c0 from the ferry to location l0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["travel by sea from location l1 to location l0", "travel by sea from location l0 to location l1", "travel by sea from location l1 to location l0", "debark the car c0 from the ferry to location l0"]}, "query": "Given the plan: \"travel by sea from location l1 to location l0, embark the car c0 at location l0 on to the ferry, debark the car c0 from the ferry to location l0, travel by sea from location l0 to location l1, travel by sea from location l1 to location l0, embark the car c0 at location l0 on to the ferry, travel by sea from location l0 to location l1, travel by sea from location l1 to location l0, travel by sea from location l0 to location l1, debark the car c0 from the ferry to location l1, travel by sea from location l1 to location l0, travel by sea from location l0 to location l1\"; which action can be removed from this plan?", "answer": "B"}
{"id": -7252838115220043929, "group": "action_justification_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c8, c1, c7, c0, c4, and c5 are at l1; c2, c9, and c6 are at l0; c3 is at l2. The goal is to reach a state where the following facts hold: Car c8 is at location l2, Car c6 is at location l2, Car c1 is at location l1, Car c4 is at location l0, Car c3 is at location l2, Car c2 is at location l1, Car c5 is at location l2, Car c7 is at location l0, Car c9 is at location l0, and Car c0 is at location l2.", "question": "Given the plan: \"sail from location l2 to location l1, board the car c4 at location l1 on to the ferry, sail from location l1 to location l0, unload the car c4 from the ferry to location l0, board the car c4 at location l0 on to the ferry, unload the car c4 from the ferry to location l0, board the car c6 at location l0 on to the ferry, sail from location l0 to location l2, unload the car c6 from the ferry to location l2, sail from location l2 to location l1, board the car c0 at location l1 on to the ferry, sail from location l1 to location l2, unload the car c0 from the ferry to location l2, sail from location l2 to location l1, board the car c7 at location l1 on to the ferry, sail from location l1 to location l0, unload the car c7 from the ferry to location l0, board the car c2 at location l0 on to the ferry, sail from location l0 to location l1, unload the car c2 from the ferry to location l1, board the car c5 at location l1 on to the ferry, sail from location l1 to location l0, sail from location l0 to location l2, unload the car c5 from the ferry to location l2, sail from location l2 to location l1, board the car c8 at location l1 on to the ferry, sail from location l1 to location l2, unload the car c8 from the ferry to location l2\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. sail from location l1 to location l0 and sail from location l0 to location l2. B. unload the car c5 from the ferry to location l2 and sail from location l2 to location l1. C. board the car c4 at location l0 on to the ferry and unload the car c4 from the ferry to location l0. D. sail from location l1 to location l0 and unload the car c7 from the ferry to location l0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["sail from location l1 to location l0 and sail from location l0 to location l2", "unload the car c5 from the ferry to location l2 and sail from location l2 to location l1", "board the car c4 at location l0 on to the ferry and unload the car c4 from the ferry to location l0", "sail from location l1 to location l0 and unload the car c7 from the ferry to location l0"]}, "query": "Given the plan: \"sail from location l2 to location l1, board the car c4 at location l1 on to the ferry, sail from location l1 to location l0, unload the car c4 from the ferry to location l0, board the car c4 at location l0 on to the ferry, unload the car c4 from the ferry to location l0, board the car c6 at location l0 on to the ferry, sail from location l0 to location l2, unload the car c6 from the ferry to location l2, sail from location l2 to location l1, board the car c0 at location l1 on to the ferry, sail from location l1 to location l2, unload the car c0 from the ferry to location l2, sail from location l2 to location l1, board the car c7 at location l1 on to the ferry, sail from location l1 to location l0, unload the car c7 from the ferry to location l0, board the car c2 at location l0 on to the ferry, sail from location l0 to location l1, unload the car c2 from the ferry to location l1, board the car c5 at location l1 on to the ferry, sail from location l1 to location l0, sail from location l0 to location l2, unload the car c5 from the ferry to location l2, sail from location l2 to location l1, board the car c8 at location l1 on to the ferry, sail from location l1 to location l2, unload the car c8 from the ferry to location l2\"; which pair of consecutive actions can be removed from this plan?", "answer": "C"}
{"id": -3978823352827920916, "group": "action_justification_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c8, c1, c7, c0, c4, and c5 are at l1; c2, c9, and c6 are at l0; c3 is at l2. The goal is to reach a state where the following facts hold: Car c8 is at location l2, Car c6 is at location l2, Car c1 is at location l1, Car c4 is at location l0, Car c3 is at location l2, Car c2 is at location l1, Car c5 is at location l2, Car c7 is at location l0, Car c9 is at location l0, and Car c0 is at location l2.", "question": "Given the plan: \"load the car c3 at location l2 on to the ferry, unload the car c3 from the ferry to location l2, sail from location l2 to location l1, load the car c0 at location l1 on to the ferry, sail from location l1 to location l2, unload the car c0 from the ferry to location l2, sail from location l2 to location l1, load the car c4 at location l1 on to the ferry, sail from location l1 to location l0, unload the car c4 from the ferry to location l0, load the car c2 at location l0 on to the ferry, sail from location l0 to location l1, unload the car c2 from the ferry to location l1, load the car c7 at location l1 on to the ferry, sail from location l1 to location l0, unload the car c7 from the ferry to location l0, load the car c6 at location l0 on to the ferry, sail from location l0 to location l2, unload the car c6 from the ferry to location l2, sail from location l2 to location l1, load the car c5 at location l1 on to the ferry, sail from location l1 to location l2, unload the car c5 from the ferry to location l2, sail from location l2 to location l1, load the car c8 at location l1 on to the ferry, sail from location l1 to location l2, unload the car c8 from the ferry to location l2, sail from location l2 to location l0\"; which of the following actions can be removed from this plan and still have a valid plan? A. unload the car c7 from the ferry to location l0. B. sail from location l1 to location l0. C. sail from location l2 to location l0. D. sail from location l2 to location l1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["unload the car c7 from the ferry to location l0", "sail from location l1 to location l0", "sail from location l2 to location l0", "sail from location l2 to location l1"]}, "query": "Given the plan: \"load the car c3 at location l2 on to the ferry, unload the car c3 from the ferry to location l2, sail from location l2 to location l1, load the car c0 at location l1 on to the ferry, sail from location l1 to location l2, unload the car c0 from the ferry to location l2, sail from location l2 to location l1, load the car c4 at location l1 on to the ferry, sail from location l1 to location l0, unload the car c4 from the ferry to location l0, load the car c2 at location l0 on to the ferry, sail from location l0 to location l1, unload the car c2 from the ferry to location l1, load the car c7 at location l1 on to the ferry, sail from location l1 to location l0, unload the car c7 from the ferry to location l0, load the car c6 at location l0 on to the ferry, sail from location l0 to location l2, unload the car c6 from the ferry to location l2, sail from location l2 to location l1, load the car c5 at location l1 on to the ferry, sail from location l1 to location l2, unload the car c5 from the ferry to location l2, sail from location l2 to location l1, load the car c8 at location l1 on to the ferry, sail from location l1 to location l2, unload the car c8 from the ferry to location l2, sail from location l2 to location l0\"; which action can be removed from this plan?", "answer": "C"}
{"id": 5819457788190746977, "group": "action_justification_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c45, c36, c20, c0, c2, c28, c10, c26, c47, c35, c38, and c14 are at l4; c4, c42, c31, c39, c7, c15, c37, c43, c23, c29, c22, c5, c49, and c3 are at l3; c48, c13, c19, c46, c21, c33, c44, c32, c24, and c34 are at l1; c8, c30, c12, and c16 are at l2; c11, c1, c41, c17, c6, c25, c9, c40, c18, and c27 are at l0. The goal is to reach a state where the following facts hold: Car c43 is at location l2, Car c26 is at location l2, Car c7 is at location l0, Car c8 is at location l4, Car c31 is at location l3, Car c9 is at location l4, Car c39 is at location l2, Car c0 is at location l2, Car c42 is at location l0, Car c25 is at location l3, Car c48 is at location l0, Car c16 is at location l0, Car c11 is at location l1, Car c37 is at location l3, Car c19 is at location l1, Car c41 is at location l4, Car c4 is at location l0, Car c28 is at location l4, Car c20 is at location l3, Car c13 is at location l3, Car c2 is at location l1, Car c1 is at location l0, Car c23 is at location l3, Car c17 is at location l0, Car c29 is at location l4, Car c6 is at location l3, Car c24 is at location l0, Car c12 is at location l3, Car c46 is at location l4, Car c47 is at location l0, Car c49 is at location l1, Car c38 is at location l4, Car c45 is at location l0, Car c36 is at location l0, Car c44 is at location l0, Car c5 is at location l4, Car c22 is at location l2, Car c30 is at location l4, Car c35 is at location l2, Car c33 is at location l1, Car c14 is at location l4, Car c18 is at location l0, Car c27 is at location l4, Car c40 is at location l2, Car c3 is at location l2, Car c32 is at location l1, Car c10 is at location l0, Car c34 is at location l3, Car c21 is at location l2, and Car c15 is at location l1.", "question": "Given the plan: \"embark the car c13 at location l1 on to the ferry, sail from location l1 to location l3, unload the car c13 from the ferry to location l3, embark the car c22 at location l3 on to the ferry, sail from location l3 to location l2, unload the car c22 from the ferry to location l2, embark the car c16 at location l2 on to the ferry, sail from location l2 to location l0, unload the car c16 from the ferry to location l0, embark the car c27 at location l0 on to the ferry, sail from location l0 to location l4, unload the car c27 from the ferry to location l4, embark the car c10 at location l4 on to the ferry, sail from location l4 to location l0, unload the car c10 from the ferry to location l0, embark the car c40 at location l0 on to the ferry, sail from location l0 to location l2, unload the car c40 from the ferry to location l2, sail from location l2 to location l4, embark the car c2 at location l4 on to the ferry, sail from location l4 to location l1, unload the car c2 from the ferry to location l1, embark the car c21 at location l1 on to the ferry, sail from location l1 to location l2, unload the car c21 from the ferry to location l2, sail from location l2 to location l4, embark the car c20 at location l4 on to the ferry, sail from location l4 to location l3, unload the car c20 from the ferry to location l3, embark the car c15 at location l3 on to the ferry, sail from location l3 to location l0, sail from location l0 to location l1, unload the car c15 from the ferry to location l1, embark the car c24 at location l1 on to the ferry, sail from location l1 to location l0, unload the car c24 from the ferry to location l0, embark the car c41 at location l0 on to the ferry, sail from location l0 to location l4, unload the car c41 from the ferry to location l4, embark the car c36 at location l4 on to the ferry, sail from location l4 to location l0, unload the car c36 from the ferry to location l0, embark the car c9 at location l0 on to the ferry, sail from location l0 to location l4, unload the car c9 from the ferry to location l4, embark the car c45 at location l4 on to the ferry, sail from location l4 to location l0, unload the car c45 from the ferry to location l0, embark the car c11 at location l0 on to the ferry, sail from location l0 to location l1, unload the car c11 from the ferry to location l1, embark the car c34 at location l1 on to the ferry, sail from location l1 to location l0, sail from location l0 to location l3, unload the car c34 from the ferry to location l3, embark the car c5 at location l3 on to the ferry, sail from location l3 to location l0, sail from location l0 to location l4, unload the car c5 from the ferry to location l4, embark the car c47 at location l4 on to the ferry, sail from location l4 to location l0, unload the car c47 from the ferry to location l0, embark the car c25 at location l0 on to the ferry, sail from location l0 to location l1, sail from location l1 to location l3, unload the car c25 from the ferry to location l3, embark the car c3 at location l3 on to the ferry, sail from location l3 to location l0, sail from location l0 to location l2, unload the car c3 from the ferry to location l2, embark the car c12 at location l2 on to the ferry, sail from location l2 to location l3, unload the car c12 from the ferry to location l3, embark the car c39 at location l3 on to the ferry, sail from location l3 to location l0, sail from location l0 to location l2, unload the car c39 from the ferry to location l2, embark the car c30 at location l2 on to the ferry, sail from location l2 to location l3, sail from location l3 to location l4, unload the car c30 from the ferry to location l4, embark the car c26 at location l4 on to the ferry, sail from location l4 to location l2, unload the car c26 from the ferry to location l2, embark the car c8 at location l2 on to the ferry, sail from location l2 to location l3, sail from location l3 to location l4, unload the car c8 from the ferry to location l4, embark the car c35 at location l4 on to the ferry, sail from location l4 to location l2, unload the car c35 from the ferry to location l2, sail from location l2 to location l3, embark the car c4 at location l3 on to the ferry, sail from location l3 to location l0, unload the car c4 from the ferry to location l0, embark the car c6 at location l0 on to the ferry, sail from location l0 to location l1, sail from location l1 to location l3, unload the car c6 from the ferry to location l3, embark the car c42 at location l3 on to the ferry, sail from location l3 to location l0, unload the car c42 from the ferry to location l0, sail from location l0 to location l1, embark the car c44 at location l1 on to the ferry, sail from location l1 to location l0, unload the car c44 from the ferry to location l0, sail from location l0 to location l1, embark the car c46 at location l1 on to the ferry, sail from location l1 to location l2, sail from location l2 to location l4, unload the car c46 from the ferry to location l4, embark the car c0 at location l4 on to the ferry, sail from location l4 to location l2, unload the car c0 from the ferry to location l2, sail from location l2 to location l3, embark the car c43 at location l3 on to the ferry, sail from location l3 to location l0, sail from location l0 to location l2, unload the car c43 from the ferry to location l2, sail from location l2 to location l3, embark the car c49 at location l3 on to the ferry, sail from location l3 to location l0, sail from location l0 to location l1, unload the car c49 from the ferry to location l1, embark the car c48 at location l1 on to the ferry, sail from location l1 to location l0, unload the car c48 from the ferry to location l0, sail from location l0 to location l3, embark the car c29 at location l3 on to the ferry, unload the car c29 from the ferry to location l3, embark the car c7 at location l3 on to the ferry, sail from location l3 to location l0, unload the car c7 from the ferry to location l0, sail from location l0 to location l3, embark the car c29 at location l3 on to the ferry, sail from location l3 to location l4, unload the car c29 from the ferry to location l4, sail from location l4 to location l0\"; which of the following actions can be removed from this plan and still have a valid plan? A. sail from location l4 to location l2. B. sail from location l3 to location l2. C. embark the car c35 at location l4 on to the ferry. D. sail from location l4 to location l0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["sail from location l4 to location l2", "sail from location l3 to location l2", "embark the car c35 at location l4 on to the ferry", "sail from location l4 to location l0"]}, "query": "Given the plan: \"embark the car c13 at location l1 on to the ferry, sail from location l1 to location l3, unload the car c13 from the ferry to location l3, embark the car c22 at location l3 on to the ferry, sail from location l3 to location l2, unload the car c22 from the ferry to location l2, embark the car c16 at location l2 on to the ferry, sail from location l2 to location l0, unload the car c16 from the ferry to location l0, embark the car c27 at location l0 on to the ferry, sail from location l0 to location l4, unload the car c27 from the ferry to location l4, embark the car c10 at location l4 on to the ferry, sail from location l4 to location l0, unload the car c10 from the ferry to location l0, embark the car c40 at location l0 on to the ferry, sail from location l0 to location l2, unload the car c40 from the ferry to location l2, sail from location l2 to location l4, embark the car c2 at location l4 on to the ferry, sail from location l4 to location l1, unload the car c2 from the ferry to location l1, embark the car c21 at location l1 on to the ferry, sail from location l1 to location l2, unload the car c21 from the ferry to location l2, sail from location l2 to location l4, embark the car c20 at location l4 on to the ferry, sail from location l4 to location l3, unload the car c20 from the ferry to location l3, embark the car c15 at location l3 on to the ferry, sail from location l3 to location l0, sail from location l0 to location l1, unload the car c15 from the ferry to location l1, embark the car c24 at location l1 on to the ferry, sail from location l1 to location l0, unload the car c24 from the ferry to location l0, embark the car c41 at location l0 on to the ferry, sail from location l0 to location l4, unload the car c41 from the ferry to location l4, embark the car c36 at location l4 on to the ferry, sail from location l4 to location l0, unload the car c36 from the ferry to location l0, embark the car c9 at location l0 on to the ferry, sail from location l0 to location l4, unload the car c9 from the ferry to location l4, embark the car c45 at location l4 on to the ferry, sail from location l4 to location l0, unload the car c45 from the ferry to location l0, embark the car c11 at location l0 on to the ferry, sail from location l0 to location l1, unload the car c11 from the ferry to location l1, embark the car c34 at location l1 on to the ferry, sail from location l1 to location l0, sail from location l0 to location l3, unload the car c34 from the ferry to location l3, embark the car c5 at location l3 on to the ferry, sail from location l3 to location l0, sail from location l0 to location l4, unload the car c5 from the ferry to location l4, embark the car c47 at location l4 on to the ferry, sail from location l4 to location l0, unload the car c47 from the ferry to location l0, embark the car c25 at location l0 on to the ferry, sail from location l0 to location l1, sail from location l1 to location l3, unload the car c25 from the ferry to location l3, embark the car c3 at location l3 on to the ferry, sail from location l3 to location l0, sail from location l0 to location l2, unload the car c3 from the ferry to location l2, embark the car c12 at location l2 on to the ferry, sail from location l2 to location l3, unload the car c12 from the ferry to location l3, embark the car c39 at location l3 on to the ferry, sail from location l3 to location l0, sail from location l0 to location l2, unload the car c39 from the ferry to location l2, embark the car c30 at location l2 on to the ferry, sail from location l2 to location l3, sail from location l3 to location l4, unload the car c30 from the ferry to location l4, embark the car c26 at location l4 on to the ferry, sail from location l4 to location l2, unload the car c26 from the ferry to location l2, embark the car c8 at location l2 on to the ferry, sail from location l2 to location l3, sail from location l3 to location l4, unload the car c8 from the ferry to location l4, embark the car c35 at location l4 on to the ferry, sail from location l4 to location l2, unload the car c35 from the ferry to location l2, sail from location l2 to location l3, embark the car c4 at location l3 on to the ferry, sail from location l3 to location l0, unload the car c4 from the ferry to location l0, embark the car c6 at location l0 on to the ferry, sail from location l0 to location l1, sail from location l1 to location l3, unload the car c6 from the ferry to location l3, embark the car c42 at location l3 on to the ferry, sail from location l3 to location l0, unload the car c42 from the ferry to location l0, sail from location l0 to location l1, embark the car c44 at location l1 on to the ferry, sail from location l1 to location l0, unload the car c44 from the ferry to location l0, sail from location l0 to location l1, embark the car c46 at location l1 on to the ferry, sail from location l1 to location l2, sail from location l2 to location l4, unload the car c46 from the ferry to location l4, embark the car c0 at location l4 on to the ferry, sail from location l4 to location l2, unload the car c0 from the ferry to location l2, sail from location l2 to location l3, embark the car c43 at location l3 on to the ferry, sail from location l3 to location l0, sail from location l0 to location l2, unload the car c43 from the ferry to location l2, sail from location l2 to location l3, embark the car c49 at location l3 on to the ferry, sail from location l3 to location l0, sail from location l0 to location l1, unload the car c49 from the ferry to location l1, embark the car c48 at location l1 on to the ferry, sail from location l1 to location l0, unload the car c48 from the ferry to location l0, sail from location l0 to location l3, embark the car c29 at location l3 on to the ferry, unload the car c29 from the ferry to location l3, embark the car c7 at location l3 on to the ferry, sail from location l3 to location l0, unload the car c7 from the ferry to location l0, sail from location l0 to location l3, embark the car c29 at location l3 on to the ferry, sail from location l3 to location l4, unload the car c29 from the ferry to location l4, sail from location l4 to location l0\"; which action can be removed from this plan?", "answer": "D"}
{"id": -6447239550806466301, "group": "action_justification_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c45, c36, c20, c0, c2, c28, c10, c26, c47, c35, c38, and c14 are at l4; c4, c42, c31, c39, c7, c15, c37, c43, c23, c29, c22, c5, c49, and c3 are at l3; c48, c13, c19, c46, c21, c33, c44, c32, c24, and c34 are at l1; c8, c30, c12, and c16 are at l2; c11, c1, c41, c17, c6, c25, c9, c40, c18, and c27 are at l0. The goal is to reach a state where the following facts hold: Car c43 is at location l2, Car c26 is at location l2, Car c7 is at location l0, Car c8 is at location l4, Car c31 is at location l3, Car c9 is at location l4, Car c39 is at location l2, Car c0 is at location l2, Car c42 is at location l0, Car c25 is at location l3, Car c48 is at location l0, Car c16 is at location l0, Car c11 is at location l1, Car c37 is at location l3, Car c19 is at location l1, Car c41 is at location l4, Car c4 is at location l0, Car c28 is at location l4, Car c20 is at location l3, Car c13 is at location l3, Car c2 is at location l1, Car c1 is at location l0, Car c23 is at location l3, Car c17 is at location l0, Car c29 is at location l4, Car c6 is at location l3, Car c24 is at location l0, Car c12 is at location l3, Car c46 is at location l4, Car c47 is at location l0, Car c49 is at location l1, Car c38 is at location l4, Car c45 is at location l0, Car c36 is at location l0, Car c44 is at location l0, Car c5 is at location l4, Car c22 is at location l2, Car c30 is at location l4, Car c35 is at location l2, Car c33 is at location l1, Car c14 is at location l4, Car c18 is at location l0, Car c27 is at location l4, Car c40 is at location l2, Car c3 is at location l2, Car c32 is at location l1, Car c10 is at location l0, Car c34 is at location l3, Car c21 is at location l2, and Car c15 is at location l1.", "question": "Given the plan: \"board the car c13 at the location l1, sail from location l1 to location l3, debark the car c13 to location l3 from the ferry, board the car c3 at the location l3, sail from location l3 to location l2, debark the car c3 to location l2 from the ferry, board the car c16 at the location l2, sail from location l2 to location l0, debark the car c16 to location l0 from the ferry, board the car c27 at the location l0, sail from location l0 to location l4, debark the car c27 to location l4 from the ferry, board the car c10 at the location l4, sail from location l4 to location l0, debark the car c10 to location l0 from the ferry, board the car c40 at the location l0, sail from location l0 to location l2, debark the car c40 to location l2 from the ferry, sail from location l2 to location l4, board the car c2 at the location l4, sail from location l4 to location l1, debark the car c2 to location l1 from the ferry, board the car c21 at the location l1, sail from location l1 to location l2, debark the car c21 to location l2 from the ferry, sail from location l2 to location l4, board the car c20 at the location l4, sail from location l4 to location l3, debark the car c20 to location l3 from the ferry, board the car c15 at the location l3, sail from location l3 to location l0, sail from location l0 to location l1, debark the car c15 to location l1 from the ferry, board the car c24 at the location l1, sail from location l1 to location l0, debark the car c24 to location l0 from the ferry, board the car c41 at the location l0, sail from location l0 to location l4, debark the car c41 to location l4 from the ferry, board the car c36 at the location l4, sail from location l4 to location l0, debark the car c36 to location l0 from the ferry, board the car c9 at the location l0, sail from location l0 to location l4, debark the car c9 to location l4 from the ferry, board the car c45 at the location l4, sail from location l4 to location l0, debark the car c45 to location l0 from the ferry, board the car c11 at the location l0, sail from location l0 to location l1, debark the car c11 to location l1 from the ferry, board the car c34 at the location l1, sail from location l1 to location l0, sail from location l0 to location l3, debark the car c34 to location l3 from the ferry, board the car c29 at the location l3, sail from location l3 to location l0, sail from location l0 to location l4, debark the car c29 to location l4 from the ferry, board the car c47 at the location l4, sail from location l4 to location l0, debark the car c47 to location l0 from the ferry, board the car c25 at the location l0, sail from location l0 to location l1, sail from location l1 to location l3, debark the car c25 to location l3 from the ferry, board the car c39 at the location l3, sail from location l3 to location l4, sail from location l4 to location l2, debark the car c39 to location l2 from the ferry, board the car c12 at the location l2, sail from location l2 to location l3, debark the car c12 to location l3 from the ferry, board the car c43 at the location l3, sail from location l3 to location l4, sail from location l4 to location l2, debark the car c43 to location l2 from the ferry, board the car c30 at the location l2, sail from location l2 to location l3, sail from location l3 to location l4, debark the car c30 to location l4 from the ferry, board the car c0 at the location l4, sail from location l4 to location l2, debark the car c0 to location l2 from the ferry, board the car c8 at the location l2, sail from location l2 to location l3, sail from location l3 to location l4, debark the car c8 to location l4 from the ferry, board the car c26 at the location l4, sail from location l4 to location l2, debark the car c26 to location l2 from the ferry, sail from location l2 to location l3, board the car c4 at the location l3, sail from location l3 to location l0, debark the car c4 to location l0 from the ferry, board the car c6 at the location l0, sail from location l0 to location l1, sail from location l1 to location l3, debark the car c6 to location l3 from the ferry, board the car c42 at the location l3, sail from location l3 to location l0, debark the car c42 to location l0 from the ferry, sail from location l0 to location l1, board the car c44 at the location l1, sail from location l1 to location l0, debark the car c44 to location l0 from the ferry, sail from location l0 to location l1, board the car c46 at the location l1, sail from location l1 to location l2, sail from location l2 to location l4, debark the car c46 to location l4 from the ferry, sail from location l4 to location l3, board the car c22 at the location l3, sail from location l3 to location l4, debark the car c22 to location l4 from the ferry, board the car c22 at the location l4, sail from location l4 to location l2, debark the car c22 to location l2 from the ferry, sail from location l2 to location l1, board the car c48 at the location l1, sail from location l1 to location l0, debark the car c48 to location l0 from the ferry, sail from location l0 to location l3, board the car c49 at the location l3, sail from location l3 to location l1, debark the car c49 to location l1 from the ferry, sail from location l1 to location l0, sail from location l0 to location l3, board the car c5 at the location l3, sail from location l3 to location l4, debark the car c5 to location l4 from the ferry, board the car c35 at the location l4, sail from location l4 to location l0, debark the car c35 to location l0 from the ferry, sail from location l0 to location l3, board the car c7 at the location l3, sail from location l3 to location l0, debark the car c7 to location l0 from the ferry, board the car c35 at the location l0, sail from location l0 to location l2, debark the car c35 to location l2 from the ferry\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. board the car c48 at the location l1 and sail from location l1 to location l0. B. sail from location l0 to location l1 and board the car c44 at the location l1. C. sail from location l2 to location l4 and debark the car c46 to location l4 from the ferry. D. debark the car c22 to location l4 from the ferry and board the car c22 at the location l4.", "choices": {"label": ["A", "B", "C", "D"], "text": ["board the car c48 at the location l1 and sail from location l1 to location l0", "sail from location l0 to location l1 and board the car c44 at the location l1", "sail from location l2 to location l4 and debark the car c46 to location l4 from the ferry", "debark the car c22 to location l4 from the ferry and board the car c22 at the location l4"]}, "query": "Given the plan: \"board the car c13 at the location l1, sail from location l1 to location l3, debark the car c13 to location l3 from the ferry, board the car c3 at the location l3, sail from location l3 to location l2, debark the car c3 to location l2 from the ferry, board the car c16 at the location l2, sail from location l2 to location l0, debark the car c16 to location l0 from the ferry, board the car c27 at the location l0, sail from location l0 to location l4, debark the car c27 to location l4 from the ferry, board the car c10 at the location l4, sail from location l4 to location l0, debark the car c10 to location l0 from the ferry, board the car c40 at the location l0, sail from location l0 to location l2, debark the car c40 to location l2 from the ferry, sail from location l2 to location l4, board the car c2 at the location l4, sail from location l4 to location l1, debark the car c2 to location l1 from the ferry, board the car c21 at the location l1, sail from location l1 to location l2, debark the car c21 to location l2 from the ferry, sail from location l2 to location l4, board the car c20 at the location l4, sail from location l4 to location l3, debark the car c20 to location l3 from the ferry, board the car c15 at the location l3, sail from location l3 to location l0, sail from location l0 to location l1, debark the car c15 to location l1 from the ferry, board the car c24 at the location l1, sail from location l1 to location l0, debark the car c24 to location l0 from the ferry, board the car c41 at the location l0, sail from location l0 to location l4, debark the car c41 to location l4 from the ferry, board the car c36 at the location l4, sail from location l4 to location l0, debark the car c36 to location l0 from the ferry, board the car c9 at the location l0, sail from location l0 to location l4, debark the car c9 to location l4 from the ferry, board the car c45 at the location l4, sail from location l4 to location l0, debark the car c45 to location l0 from the ferry, board the car c11 at the location l0, sail from location l0 to location l1, debark the car c11 to location l1 from the ferry, board the car c34 at the location l1, sail from location l1 to location l0, sail from location l0 to location l3, debark the car c34 to location l3 from the ferry, board the car c29 at the location l3, sail from location l3 to location l0, sail from location l0 to location l4, debark the car c29 to location l4 from the ferry, board the car c47 at the location l4, sail from location l4 to location l0, debark the car c47 to location l0 from the ferry, board the car c25 at the location l0, sail from location l0 to location l1, sail from location l1 to location l3, debark the car c25 to location l3 from the ferry, board the car c39 at the location l3, sail from location l3 to location l4, sail from location l4 to location l2, debark the car c39 to location l2 from the ferry, board the car c12 at the location l2, sail from location l2 to location l3, debark the car c12 to location l3 from the ferry, board the car c43 at the location l3, sail from location l3 to location l4, sail from location l4 to location l2, debark the car c43 to location l2 from the ferry, board the car c30 at the location l2, sail from location l2 to location l3, sail from location l3 to location l4, debark the car c30 to location l4 from the ferry, board the car c0 at the location l4, sail from location l4 to location l2, debark the car c0 to location l2 from the ferry, board the car c8 at the location l2, sail from location l2 to location l3, sail from location l3 to location l4, debark the car c8 to location l4 from the ferry, board the car c26 at the location l4, sail from location l4 to location l2, debark the car c26 to location l2 from the ferry, sail from location l2 to location l3, board the car c4 at the location l3, sail from location l3 to location l0, debark the car c4 to location l0 from the ferry, board the car c6 at the location l0, sail from location l0 to location l1, sail from location l1 to location l3, debark the car c6 to location l3 from the ferry, board the car c42 at the location l3, sail from location l3 to location l0, debark the car c42 to location l0 from the ferry, sail from location l0 to location l1, board the car c44 at the location l1, sail from location l1 to location l0, debark the car c44 to location l0 from the ferry, sail from location l0 to location l1, board the car c46 at the location l1, sail from location l1 to location l2, sail from location l2 to location l4, debark the car c46 to location l4 from the ferry, sail from location l4 to location l3, board the car c22 at the location l3, sail from location l3 to location l4, debark the car c22 to location l4 from the ferry, board the car c22 at the location l4, sail from location l4 to location l2, debark the car c22 to location l2 from the ferry, sail from location l2 to location l1, board the car c48 at the location l1, sail from location l1 to location l0, debark the car c48 to location l0 from the ferry, sail from location l0 to location l3, board the car c49 at the location l3, sail from location l3 to location l1, debark the car c49 to location l1 from the ferry, sail from location l1 to location l0, sail from location l0 to location l3, board the car c5 at the location l3, sail from location l3 to location l4, debark the car c5 to location l4 from the ferry, board the car c35 at the location l4, sail from location l4 to location l0, debark the car c35 to location l0 from the ferry, sail from location l0 to location l3, board the car c7 at the location l3, sail from location l3 to location l0, debark the car c7 to location l0 from the ferry, board the car c35 at the location l0, sail from location l0 to location l2, debark the car c35 to location l2 from the ferry\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
{"id": -9031846171017339656, "group": "action_justification_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 5 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c2, c1, c3, and c0 are at l0; c4 is at l1. The goal is to reach a state where the following facts hold: Car c4 is at location l0, Car c0 is at location l1, Car c2 is at location l1, Car c1 is at location l0, and Car c3 is at location l0.", "question": "Given the plan: \"board car c3 at location l0, unload the car c3 from the ferry to location l0, board car c0 at location l0, travel by sea from location l0 to location l1, unload the car c0 from the ferry to location l1, board car c4 at location l1, unload the car c4 from the ferry to location l1, travel by sea from location l1 to location l0, board car c2 at location l0, travel by sea from location l0 to location l1, unload the car c2 from the ferry to location l1, board car c4 at location l1, travel by sea from location l1 to location l0, unload the car c4 from the ferry to location l0\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. travel by sea from location l1 to location l0 and board car c2 at location l0. B. board car c3 at location l0 and unload the car c3 from the ferry to location l0. C. travel by sea from location l0 to location l1 and unload the car c0 from the ferry to location l1. D. board car c2 at location l0 and travel by sea from location l0 to location l1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["travel by sea from location l1 to location l0 and board car c2 at location l0", "board car c3 at location l0 and unload the car c3 from the ferry to location l0", "travel by sea from location l0 to location l1 and unload the car c0 from the ferry to location l1", "board car c2 at location l0 and travel by sea from location l0 to location l1"]}, "query": "Given the plan: \"board car c3 at location l0, unload the car c3 from the ferry to location l0, board car c0 at location l0, travel by sea from location l0 to location l1, unload the car c0 from the ferry to location l1, board car c4 at location l1, unload the car c4 from the ferry to location l1, travel by sea from location l1 to location l0, board car c2 at location l0, travel by sea from location l0 to location l1, unload the car c2 from the ferry to location l1, board car c4 at location l1, travel by sea from location l1 to location l0, unload the car c4 from the ferry to location l0\"; which pair of consecutive actions can be removed from this plan?", "answer": "B"}
{"id": -6499695625485002841, "group": "action_justification_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 2 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c0 and c1 are at l1. The goal is to reach a state where the following facts hold: Car c1 is at location l2 and Car c0 is at location l0.", "question": "Given the plan: \"board car c1 at location l1, travel by sea from location l1 to location l2, travel by sea from location l2 to location l1, travel by sea from location l1 to location l2, debark the car c1 to location l2 from the ferry, travel by sea from location l2 to location l1, board car c0 at location l1, travel by sea from location l1 to location l2, travel by sea from location l2 to location l0, debark the car c0 to location l0 from the ferry\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. travel by sea from location l2 to location l1 and travel by sea from location l1 to location l2. B. travel by sea from location l2 to location l1 and board car c0 at location l1. C. board car c0 at location l1 and travel by sea from location l1 to location l2. D. travel by sea from location l2 to location l0 and debark the car c0 to location l0 from the ferry.", "choices": {"label": ["A", "B", "C", "D"], "text": ["travel by sea from location l2 to location l1 and travel by sea from location l1 to location l2", "travel by sea from location l2 to location l1 and board car c0 at location l1", "board car c0 at location l1 and travel by sea from location l1 to location l2", "travel by sea from location l2 to location l0 and debark the car c0 to location l0 from the ferry"]}, "query": "Given the plan: \"board car c1 at location l1, travel by sea from location l1 to location l2, travel by sea from location l2 to location l1, travel by sea from location l1 to location l2, debark the car c1 to location l2 from the ferry, travel by sea from location l2 to location l1, board car c0 at location l1, travel by sea from location l1 to location l2, travel by sea from location l2 to location l0, debark the car c0 to location l0 from the ferry\"; which pair of consecutive actions can be removed from this plan?", "answer": "A"}
{"id": 3903123391386162053, "group": "action_justification_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 2 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c1 and c0 are at l0. The goal is to reach a state where the following facts hold: Car c0 is at location l1 and Car c1 is at location l1.", "question": "Given the plan: \"board the car c0 at the location l0, travel by sea from location l0 to location l1, unload the car c0 from the ferry to location l1, travel by sea from location l1 to location l0, board the car c1 at the location l0, travel by sea from location l0 to location l1, unload the car c1 from the ferry to location l1, board the car c0 at the location l1, unload the car c0 from the ferry to location l1\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. board the car c0 at the location l0 and travel by sea from location l0 to location l1. B. unload the car c1 from the ferry to location l1 and board the car c0 at the location l1. C. travel by sea from location l0 to location l1 and unload the car c1 from the ferry to location l1. D. board the car c0 at the location l1 and unload the car c0 from the ferry to location l1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["board the car c0 at the location l0 and travel by sea from location l0 to location l1", "unload the car c1 from the ferry to location l1 and board the car c0 at the location l1", "travel by sea from location l0 to location l1 and unload the car c1 from the ferry to location l1", "board the car c0 at the location l1 and unload the car c0 from the ferry to location l1"]}, "query": "Given the plan: \"board the car c0 at the location l0, travel by sea from location l0 to location l1, unload the car c0 from the ferry to location l1, travel by sea from location l1 to location l0, board the car c1 at the location l0, travel by sea from location l0 to location l1, unload the car c1 from the ferry to location l1, board the car c0 at the location l1, unload the car c0 from the ferry to location l1\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
{"id": 3456808734159078218, "group": "action_justification_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 10 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c2, c8, c1, c9, c3, c0, and c6 are at l0; c7, c4, and c5 are at l1. The goal is to reach a state where the following facts hold: Car c9 is at location l1, Car c3 is at location l1, Car c4 is at location l0, Car c5 is at location l0, Car c2 is at location l1, Car c1 is at location l0, Car c7 is at location l0, Car c8 is at location l0, Car c0 is at location l0, and Car c6 is at location l0.", "question": "Given the plan: \"load the car c2 at location l0 on to the ferry, sail from location l0 to location l1, debark the car c2 from the ferry to location l1, load the car c2 at location l1 on to the ferry, debark the car c2 from the ferry to location l1, load the car c2 at location l1 on to the ferry, debark the car c2 from the ferry to location l1, load the car c4 at location l1 on to the ferry, sail from location l1 to location l0, debark the car c4 from the ferry to location l0, load the car c3 at location l0 on to the ferry, sail from location l0 to location l1, debark the car c3 from the ferry to location l1, load the car c5 at location l1 on to the ferry, sail from location l1 to location l0, debark the car c5 from the ferry to location l0, load the car c9 at location l0 on to the ferry, sail from location l0 to location l1, debark the car c9 from the ferry to location l1, load the car c7 at location l1 on to the ferry, sail from location l1 to location l0, debark the car c7 from the ferry to location l0\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. load the car c2 at location l1 on to the ferry and debark the car c2 from the ferry to location l1. B. debark the car c4 from the ferry to location l0 and load the car c3 at location l0 on to the ferry. C. load the car c2 at location l0 on to the ferry and sail from location l0 to location l1. D. load the car c5 at location l1 on to the ferry and sail from location l1 to location l0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["load the car c2 at location l1 on to the ferry and debark the car c2 from the ferry to location l1", "debark the car c4 from the ferry to location l0 and load the car c3 at location l0 on to the ferry", "load the car c2 at location l0 on to the ferry and sail from location l0 to location l1", "load the car c5 at location l1 on to the ferry and sail from location l1 to location l0"]}, "query": "Given the plan: \"load the car c2 at location l0 on to the ferry, sail from location l0 to location l1, debark the car c2 from the ferry to location l1, load the car c2 at location l1 on to the ferry, debark the car c2 from the ferry to location l1, load the car c2 at location l1 on to the ferry, debark the car c2 from the ferry to location l1, load the car c4 at location l1 on to the ferry, sail from location l1 to location l0, debark the car c4 from the ferry to location l0, load the car c3 at location l0 on to the ferry, sail from location l0 to location l1, debark the car c3 from the ferry to location l1, load the car c5 at location l1 on to the ferry, sail from location l1 to location l0, debark the car c5 from the ferry to location l0, load the car c9 at location l0 on to the ferry, sail from location l0 to location l1, debark the car c9 from the ferry to location l1, load the car c7 at location l1 on to the ferry, sail from location l1 to location l0, debark the car c7 from the ferry to location l0\"; which pair of consecutive actions can be removed from this plan?", "answer": "A"}
{"id": 4153259526725623630, "group": "action_justification_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 2 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c0 and c1 are at l1. The goal is to reach a state where the following facts hold: Car c1 is at location l2 and Car c0 is at location l0.", "question": "Given the plan: \"board car c1 at location l1, sail from location l1 to location l2, debark the car c1 from the ferry to location l2, sail from location l2 to location l1, board car c0 at location l1, sail from location l1 to location l2, debark the car c0 from the ferry to location l2, board car c0 at location l2, sail from location l2 to location l0, debark the car c0 from the ferry to location l0, sail from location l0 to location l1\"; which of the following actions can be removed from this plan and still have a valid plan? A. board car c1 at location l1. B. debark the car c0 from the ferry to location l0. C. debark the car c1 from the ferry to location l2. D. sail from location l0 to location l1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["board car c1 at location l1", "debark the car c0 from the ferry to location l0", "debark the car c1 from the ferry to location l2", "sail from location l0 to location l1"]}, "query": "Given the plan: \"board car c1 at location l1, sail from location l1 to location l2, debark the car c1 from the ferry to location l2, sail from location l2 to location l1, board car c0 at location l1, sail from location l1 to location l2, debark the car c0 from the ferry to location l2, board car c0 at location l2, sail from location l2 to location l0, debark the car c0 from the ferry to location l0, sail from location l0 to location l1\"; which action can be removed from this plan?", "answer": "D"}
