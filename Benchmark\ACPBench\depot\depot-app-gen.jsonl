{"id": 1855495312763355198, "group": "applicable_actions_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 2 distributors, 2 depots, 4 pallets, 2 crates, 4 hoists, numbered consecutively. Currently, crate0, pallet2, crate1, and pallet1 are clear; hoist3, hoist2, hoist1, and hoist0 are available; truck1 is at depot0, crate0 is at depot0, hoist3 is at distributor1, pallet3 is at distributor1, hoist2 is at distributor0, hoist1 is at depot1, truck0 is at distributor0, crate1 is at distributor1, pallet0 is at depot0, pallet1 is at depot1, pallet2 is at distributor0, and hoist0 is at depot0; crate1 is on pallet3 and crate0 is on pallet0. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from place ?y to place ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the ground ?z at position ?p using the hoist ?x, (drop ?x ?y ?z ?p) - drop the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - load the crate ?y into the truck ?z at the place ?p with the hoist ?x, and (unload ?x ?y ?z ?p) - unload the crate ?y from the truck ?z at the place ?p using the hoist ?x.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(lift hoist0 crate0 pallet0 depot0)", "(drive truck0 distributor0 distributor1)", "(drive truck1 depot0 depot1)", "(drive truck0 distributor0 distributor0)", "(lift hoist3 crate1 pallet3 distributor1)", "(drive truck1 depot0 distributor0)", "(drive truck1 depot0 distributor1)", "(drive truck0 distributor0 depot0)", "(drive truck0 distributor0 depot1)", "(drive truck1 depot0 depot0)"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-2-2-2-4-4-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 - Hoist pallet0 pallet1 pallet2 pallet3 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot0) (at crate1 distributor1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 distributor0) (at hoist3 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 distributor0) (at pallet3 distributor1) (at truck0 distributor0) (at truck1 depot0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (clear crate0) (clear crate1) (clear pallet1) (clear pallet2) (on crate0 pallet0) (on crate1 pallet3))\n    (:goal (and (on crate0 crate1) (on crate1 pallet0)))\n)"}
{"id": -6800416465499459702, "group": "applicable_actions_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 2 distributors, 2 depots, 4 pallets, 2 crates, 4 hoists, numbered consecutively. Currently, pallet2, pallet3, crate1, and pallet1 are clear; hoist3, hoist2, and hoist1 are available; hoist3 is at distributor1, pallet3 is at distributor1, crate1 is at depot0, hoist2 is at distributor0, hoist1 is at depot1, truck0 is at distributor0, truck1 is at distributor0, pallet0 is at depot0, pallet1 is at depot1, pallet2 is at distributor0, and hoist0 is at depot0; crate1 is on pallet0; hoist0 is lifting crate0. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from the place ?y to the place ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the surface ?z at place ?p using the hoist ?x, (drop ?x ?y ?z ?p) - drop the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - use the hoist ?x to lift and place the crate ?y from place ?p into the truck ?z, and (unload ?x ?y ?z ?p) - unload the crate ?y from the truck ?z at the place ?p using the hoist ?x.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(drop hoist0 crate0 crate1 depot0)", "(drive truck0 distributor0 distributor1)", "(drive truck1 distributor0 distributor0)", "(drive truck1 distributor0 distributor1)", "(drive truck0 distributor0 distributor0)", "(drive truck1 distributor0 depot0)", "(drive truck1 distributor0 depot1)", "(drive truck0 distributor0 depot0)", "(drive truck0 distributor0 depot1)"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-2-2-2-4-4-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 - Hoist pallet0 pallet1 pallet2 pallet3 - Pallet truck0 truck1 - Truck)\n    (:init (at crate1 depot0) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 distributor0) (at hoist3 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 distributor0) (at pallet3 distributor1) (at truck0 distributor0) (at truck1 distributor0) (available hoist1) (available hoist2) (available hoist3) (clear crate1) (clear pallet1) (clear pallet2) (clear pallet3) (lifting hoist0 crate0) (on crate1 pallet0))\n    (:goal (and (on crate0 crate1) (on crate1 pallet0)))\n)"}
{"id": 8451016848524157625, "group": "applicable_actions_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 2 distributors, 2 depots, 4 pallets, 2 crates, 4 hoists, numbered consecutively. Currently, pallet2, pallet3, crate1, and pallet1 are clear; hoist3, hoist2, and hoist1 are available; hoist3 is at distributor1, pallet3 is at distributor1, crate1 is at depot0, hoist2 is at distributor0, hoist1 is at depot1, truck1 is at distributor0, pallet0 is at depot0, pallet1 is at depot1, pallet2 is at distributor0, hoist0 is at depot0, and truck0 is at depot1; crate1 is on pallet0; hoist0 is lifting crate0. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from location ?y to location ?z, (lift ?x ?y ?z ?p) - use the hoist ?x to lift the crate ?y from the surface ?z at location ?p, (drop ?x ?y ?z ?p) - lower the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - use the hoist ?x to lift and place the crate ?y from place ?p into the truck ?z, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(drop hoist0 crate0 crate1 depot0)", "(drive truck0 depot1 depot0)", "(drive truck1 distributor0 distributor0)", "(drive truck0 depot1 distributor1)", "(drive truck1 distributor0 distributor1)", "(drive truck0 depot1 distributor0)", "(drive truck1 distributor0 depot0)", "(drive truck1 distributor0 depot1)", "(drive truck0 depot1 depot1)"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-2-2-2-4-4-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 - Hoist pallet0 pallet1 pallet2 pallet3 - Pallet truck0 truck1 - Truck)\n    (:init (at crate1 depot0) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 distributor0) (at hoist3 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 distributor0) (at pallet3 distributor1) (at truck0 depot1) (at truck1 distributor0) (available hoist1) (available hoist2) (available hoist3) (clear crate1) (clear pallet1) (clear pallet2) (clear pallet3) (lifting hoist0 crate0) (on crate1 pallet0))\n    (:goal (and (on crate0 crate1) (on crate1 pallet0)))\n)"}
{"id": 6032459916782033873, "group": "applicable_actions_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 2 distributors, 2 depots, 4 pallets, 2 crates, 4 hoists, numbered consecutively. Currently, crate0, pallet2, pallet3, and pallet1 are clear; hoist2, hoist1, and hoist0 are available; truck1 is at depot0, crate0 is at depot0, hoist3 is at distributor1, pallet3 is at distributor1, hoist2 is at distributor0, hoist1 is at depot1, truck0 is at depot0, pallet0 is at depot0, pallet1 is at depot1, pallet2 is at distributor0, and hoist0 is at depot0; crate0 is on pallet0; hoist3 is lifting crate1. The available actions are: (drive ?x ?y ?z) - drive the truck ?x from ?y to ?z, (lift ?x ?y ?z ?p) - use hoist ?x to lift crate ?y from surface ?z at place ?p, (drop ?x ?y ?z ?p) - drop crate ?y from hoist ?x onto surface ?z at place ?p, (load ?x ?y ?z ?p) - load crate ?y into truck ?z at place ?p with hoist ?x, and (unload ?x ?y ?z ?p) - unload the crate ?y from the truck ?z at the place ?p using the hoist ?x.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(lift hoist0 crate0 pallet0 depot0)", "(drive truck0 depot0 depot0)", "(drive truck0 depot0 distributor0)", "(drive truck1 depot0 depot1)", "(drive truck0 depot0 depot1)", "(drive truck1 depot0 distributor0)", "(drive truck1 depot0 distributor1)", "(drive truck0 depot0 distributor1)", "(drop hoist3 crate1 pallet3 distributor1)", "(drive truck1 depot0 depot0)"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-2-2-2-4-4-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 - Hoist pallet0 pallet1 pallet2 pallet3 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot0) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 distributor0) (at hoist3 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 distributor0) (at pallet3 distributor1) (at truck0 depot0) (at truck1 depot0) (available hoist0) (available hoist1) (available hoist2) (clear crate0) (clear pallet1) (clear pallet2) (clear pallet3) (lifting hoist3 crate1) (on crate0 pallet0))\n    (:goal (and (on crate0 crate1) (on crate1 pallet0)))\n)"}
{"id": 5079940520531792094, "group": "applicable_actions_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 2 distributors, 2 depots, 4 pallets, 2 crates, 4 hoists, numbered consecutively. Currently, pallet2, pallet3, crate1, and pallet1 are clear; hoist3, hoist2, and hoist1 are available; hoist3 is at distributor1, pallet3 is at distributor1, crate1 is at depot0, hoist2 is at distributor0, hoist1 is at depot1, truck1 is at depot1, truck0 is at distributor0, pallet0 is at depot0, pallet1 is at depot1, pallet2 is at distributor0, and hoist0 is at depot0; crate1 is on pallet0; hoist0 is lifting crate0. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from place ?y to place ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the surface ?z at place ?p using the hoist ?x, (drop ?x ?y ?z ?p) - lower the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - load the crate ?y into the truck ?z at the place ?p with the hoist ?x, and (unload ?x ?y ?z ?p) - unload the crate ?y from the truck ?z at the place ?p using the hoist ?x.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(drive truck1 depot1 depot1)", "(drive truck1 depot1 depot0)", "(drop hoist0 crate0 crate1 depot0)", "(drive truck0 distributor0 distributor1)", "(drive truck1 depot1 distributor1)", "(drive truck0 distributor0 distributor0)", "(drive truck0 distributor0 depot0)", "(drive truck1 depot1 distributor0)", "(drive truck0 distributor0 depot1)"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-2-2-2-4-4-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 - Hoist pallet0 pallet1 pallet2 pallet3 - Pallet truck0 truck1 - Truck)\n    (:init (at crate1 depot0) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 distributor0) (at hoist3 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 distributor0) (at pallet3 distributor1) (at truck0 distributor0) (at truck1 depot1) (available hoist1) (available hoist2) (available hoist3) (clear crate1) (clear pallet1) (clear pallet2) (clear pallet3) (lifting hoist0 crate0) (on crate1 pallet0))\n    (:goal (and (on crate0 crate1) (on crate1 pallet0)))\n)"}
{"id": 1228143714931272996, "group": "applicable_actions_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 2 distributors, 2 depots, 4 pallets, 2 crates, 4 hoists, numbered consecutively. Currently, pallet2, pallet3, crate1, and pallet1 are clear; hoist3, hoist2, hoist1, and hoist0 are available; hoist3 is at distributor1, pallet3 is at distributor1, crate1 is at depot0, hoist2 is at distributor0, truck0 is at distributor1, hoist1 is at depot1, truck1 is at distributor0, pallet0 is at depot0, pallet1 is at depot1, pallet2 is at distributor0, and hoist0 is at depot0; crate1 is on pallet0; crate0 is in truck1. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from the place ?y to the place ?z, (lift ?x ?y ?z ?p) - use hoist ?x to lift crate ?y from surface ?z at place ?p, (drop ?x ?y ?z ?p) - lower the crate ?y from the hoist ?x and place it on the surface ?z at location ?p, (load ?x ?y ?z ?p) - use hoist ?x to load crate ?y into truck ?z at place ?p, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(drive truck1 distributor0 distributor0)", "(drive truck0 distributor1 distributor1)", "(unload hoist2 crate0 truck1 distributor0)", "(drive truck1 distributor0 distributor1)", "(drive truck0 distributor1 distributor0)", "(drive truck1 distributor0 depot0)", "(drive truck1 distributor0 depot1)", "(drive truck0 distributor1 depot1)", "(drive truck0 distributor1 depot0)", "(lift hoist0 crate1 pallet0 depot0)"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-2-2-2-4-4-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 - Hoist pallet0 pallet1 pallet2 pallet3 - Pallet truck0 truck1 - Truck)\n    (:init (at crate1 depot0) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 distributor0) (at hoist3 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 distributor0) (at pallet3 distributor1) (at truck0 distributor1) (at truck1 distributor0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (clear crate1) (clear pallet1) (clear pallet2) (clear pallet3) (in crate0 truck1) (on crate1 pallet0))\n    (:goal (and (on crate0 crate1) (on crate1 pallet0)))\n)"}
{"id": -2964003854940434263, "group": "applicable_actions_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 2 distributors, 2 depots, 4 pallets, 2 crates, 4 hoists, numbered consecutively. Currently, crate0, pallet2, pallet3, and pallet1 are clear; hoist3, hoist2, hoist1, and hoist0 are available; crate0 is at depot0, hoist3 is at distributor1, pallet3 is at distributor1, hoist2 is at distributor0, hoist1 is at depot1, truck1 is at depot1, truck0 is at distributor0, pallet0 is at depot0, pallet1 is at depot1, pallet2 is at distributor0, and hoist0 is at depot0; crate0 is on pallet0; crate1 is in truck1. The available actions are: (drive ?x ?y ?z) - drive the truck ?x from ?y to ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the ground ?z at position ?p using the hoist ?x, (drop ?x ?y ?z ?p) - drop crate ?y from hoist ?x onto surface ?z at place ?p, (load ?x ?y ?z ?p) - use hoist ?x to load crate ?y into truck ?z at place ?p, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(drive truck1 depot1 depot1)", "(lift hoist0 crate0 pallet0 depot0)", "(drive truck1 depot1 depot0)", "(drive truck0 distributor0 distributor1)", "(drive truck1 depot1 distributor1)", "(drive truck0 distributor0 distributor0)", "(unload hoist1 crate1 truck1 depot1)", "(drive truck0 distributor0 depot0)", "(drive truck1 depot1 distributor0)", "(drive truck0 distributor0 depot1)"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-2-2-2-4-4-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 - Hoist pallet0 pallet1 pallet2 pallet3 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot0) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 distributor0) (at hoist3 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 distributor0) (at pallet3 distributor1) (at truck0 distributor0) (at truck1 depot1) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (clear crate0) (clear pallet1) (clear pallet2) (clear pallet3) (in crate1 truck1) (on crate0 pallet0))\n    (:goal (and (on crate0 crate1) (on crate1 pallet0)))\n)"}
{"id": -6158242781090249492, "group": "applicable_actions_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 2 distributors, 2 depots, 4 pallets, 2 crates, 4 hoists, numbered consecutively. Currently, pallet2, pallet3, pallet0, and pallet1 are clear; hoist3, hoist2, hoist1, and hoist0 are available; hoist3 is at distributor1, pallet3 is at distributor1, hoist2 is at distributor0, hoist1 is at depot1, truck1 is at distributor0, pallet0 is at depot0, pallet1 is at depot1, pallet2 is at distributor0, hoist0 is at depot0, and truck0 is at depot1; crate1 is in truck1 and crate0 is in truck1. The available actions are: (drive ?x ?y ?z) - drive truck ?x from place ?y to place ?z, (lift ?x ?y ?z ?p) - lift crate ?y from surface ?z at place ?p using hoist ?x, (drop ?x ?y ?z ?p) - drop the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - load the crate ?y into the truck ?z at the place ?p with the hoist ?x, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(drive truck0 depot1 depot0)", "(drive truck1 distributor0 distributor0)", "(drive truck0 depot1 distributor1)", "(unload hoist2 crate0 truck1 distributor0)", "(drive truck1 distributor0 distributor1)", "(drive truck0 depot1 distributor0)", "(drive truck1 distributor0 depot0)", "(drive truck1 distributor0 depot1)", "(unload hoist2 crate1 truck1 distributor0)", "(drive truck0 depot1 depot1)"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-2-2-2-4-4-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 - Hoist pallet0 pallet1 pallet2 pallet3 - Pallet truck0 truck1 - Truck)\n    (:init (at hoist0 depot0) (at hoist1 depot1) (at hoist2 distributor0) (at hoist3 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 distributor0) (at pallet3 distributor1) (at truck0 depot1) (at truck1 distributor0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet3) (in crate0 truck1) (in crate1 truck1))\n    (:goal (and (on crate0 crate1) (on crate1 pallet0)))\n)"}
{"id": 6166550663854998683, "group": "applicable_actions_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 2 distributors, 2 depots, 4 pallets, 2 crates, 4 hoists, numbered consecutively. Currently, pallet2, pallet3, pallet0, and pallet1 are clear; hoist2, hoist1, and hoist0 are available; hoist3 is at distributor1, pallet3 is at distributor1, hoist2 is at distributor0, hoist1 is at depot1, truck0 is at distributor0, truck1 is at distributor0, pallet0 is at depot0, pallet1 is at depot1, pallet2 is at distributor0, and hoist0 is at depot0; crate1 is in truck1; hoist3 is lifting crate0. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from the place ?y to the place ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the surface ?z at place ?p using the hoist ?x, (drop ?x ?y ?z ?p) - drop the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - load the crate ?y from place ?p with hoist ?x into the truck ?z, and (unload ?x ?y ?z ?p) - unload crate ?y from truck ?z at place ?p using hoist ?x.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(drive truck0 distributor0 distributor1)", "(drive truck1 distributor0 distributor0)", "(drive truck1 distributor0 distributor1)", "(drive truck0 distributor0 distributor0)", "(drop hoist3 crate0 pallet3 distributor1)", "(drive truck1 distributor0 depot0)", "(drive truck1 distributor0 depot1)", "(unload hoist2 crate1 truck1 distributor0)", "(drive truck0 distributor0 depot0)", "(drive truck0 distributor0 depot1)"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-2-2-2-4-4-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 - Hoist pallet0 pallet1 pallet2 pallet3 - Pallet truck0 truck1 - Truck)\n    (:init (at hoist0 depot0) (at hoist1 depot1) (at hoist2 distributor0) (at hoist3 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 distributor0) (at pallet3 distributor1) (at truck0 distributor0) (at truck1 distributor0) (available hoist0) (available hoist1) (available hoist2) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet3) (in crate1 truck1) (lifting hoist3 crate0))\n    (:goal (and (on crate0 crate1) (on crate1 pallet0)))\n)"}
{"id": -8404147825345700498, "group": "applicable_actions_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 2 distributors, 2 depots, 4 pallets, 2 crates, 4 hoists, numbered consecutively. Currently, crate0, pallet2, crate1, and pallet1 are clear; hoist3, hoist2, hoist1, and hoist0 are available; crate0 is at depot0, hoist3 is at distributor1, pallet3 is at distributor1, hoist2 is at distributor0, hoist1 is at depot1, truck0 is at distributor0, truck1 is at distributor0, crate1 is at distributor1, pallet0 is at depot0, pallet1 is at depot1, pallet2 is at distributor0, and hoist0 is at depot0; crate1 is on pallet3 and crate0 is on pallet0. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from location ?y to location ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the ground ?z at position ?p using the hoist ?x, (drop ?x ?y ?z ?p) - lower the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - load the crate ?y from place ?p with hoist ?x into the truck ?z, and (unload ?x ?y ?z ?p) - unload crate ?y from truck ?z at place ?p using hoist ?x.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(lift hoist0 crate0 pallet0 depot0)", "(drive truck0 distributor0 distributor1)", "(drive truck1 distributor0 distributor0)", "(drive truck1 distributor0 distributor1)", "(drive truck0 distributor0 distributor0)", "(drive truck1 distributor0 depot0)", "(lift hoist3 crate1 pallet3 distributor1)", "(drive truck1 distributor0 depot1)", "(drive truck0 distributor0 depot0)", "(drive truck0 distributor0 depot1)"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-2-2-2-4-4-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 - Hoist pallet0 pallet1 pallet2 pallet3 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot0) (at crate1 distributor1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 distributor0) (at hoist3 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 distributor0) (at pallet3 distributor1) (at truck0 distributor0) (at truck1 distributor0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (clear crate0) (clear crate1) (clear pallet1) (clear pallet2) (on crate0 pallet0) (on crate1 pallet3))\n    (:goal (and (on crate0 crate1) (on crate1 pallet0)))\n)"}
