{"id": 8560346137411232700, "group": "landmarks_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-2f and its arm is empty. The following locations have hard rock: f2-1f, f2-2f, and f0-3f. The following locations have soft rock: f0-2f and f2-3f. The gold is at f1-3f location. The laser is at f1-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The laser is at f2-3f location. B. The robot is at position f1-1f. C. The laser is at f2-0f location. D. The robot is at position f1-3f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The laser is at f2-3f location", "The robot is at position f1-1f", "The laser is at f2-0f location", "The robot is at position f1-3f"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 8254514148218295006, "group": "landmarks_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and is holding a bomb. The following locations have soft rock: f0-3f, f2-2f, f2-1f, f1-3f, f1-2f, and f2-3f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The robot's arm is empty. B. The robot is at position f1-3f. C. The laser is at f2-2f location. D. The laser is at f2-1f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot's arm is empty", "The robot is at position f1-3f", "The laser is at f2-2f location", "The laser is at f2-1f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -2286980433751099581, "group": "landmarks_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x3 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and its arm is empty. The following locations have hard rock: f1-2f. The following locations have soft rock: f2-2f and f2-1f. The gold is at f0-2f location. The laser is at f1-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The laser is at f1-2f location. B. The robot is at position f0-2f. C. Soft rock at f0-2f. D. Location(s) f1-2f is clear.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The laser is at f1-2f location", "The robot is at position f0-2f", "Soft rock at f0-2f", "Location(s) f1-2f is clear"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 7141105574059734251, "group": "landmarks_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and is holding a laser. The following locations have soft rock: f0-3f, f2-2f, f2-1f, f1-3f, f1-2f, and f2-3f. The gold is at f0-3f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Soft rock at f0-1f. B. The robot is at position f1-1f. C. Location(s) f0-3f is clear. D. The laser is at f2-1f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Soft rock at f0-1f", "The robot is at position f1-1f", "Location(s) f0-3f is clear", "The laser is at f2-1f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -5277026456352773288, "group": "landmarks_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x3 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and its arm is empty. The following locations have hard rock: f1-2f. The following locations have soft rock: f2-2f, f0-1f, f1-1f, and f0-2f. The gold is at f0-2f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The robot is at position f2-1f. B. Location(s) f2-2f is clear. C. The robot is at position f0-2f. D. The laser is at f1-0f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot is at position f2-1f", "Location(s) f2-2f is clear", "The robot is at position f0-2f", "The laser is at f1-0f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -1142956497938763211, "group": "landmarks_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-2f and its arm is empty. The following locations have hard rock: f1-1f, f1-3f, and f1-4f. The following locations have soft rock: f2-1f, f2-4f, f0-4f, f2-2f, and f2-3f. The gold is at f0-4f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The robot is at position f1-4f. B. The robot is at position f1-0f. C. Location(s) f0-4f is clear. D. Location(s) f2-1f is clear.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot is at position f1-4f", "The robot is at position f1-0f", "Location(s) f0-4f is clear", "Location(s) f2-1f is clear"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -2771737250744636651, "group": "landmarks_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-2f and its arm is empty. The following locations have hard rock: f1-1f, f1-3f, and f1-4f. The following locations have soft rock: f2-1f, f1-2f, f2-4f, f0-4f, f2-2f, and f2-3f. The gold is at f0-4f location. The laser is at f0-2f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The laser is at f2-4f location. B. Location(s) f0-4f is clear. C. The laser is at f0-3f location. D. The laser is at f2-3f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The laser is at f2-4f location", "Location(s) f0-4f is clear", "The laser is at f0-3f location", "The laser is at f2-3f location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 4634419972385712283, "group": "landmarks_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f3-0f and is holding a bomb. The following locations have hard rock: f3-1f, f1-1f, f3-3f, f1-3f, and f1-2f. The following locations have soft rock: f2-1f, f3-2f, f0-3f, f2-2f, and f2-3f. The gold is at f0-3f location. The laser is at f0-1f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The robot is at position f1-3f. B. Location(s) f2-2f is clear. C. Location(s) f0-3f is clear. D. The robot is at position f2-3f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot is at position f1-3f", "Location(s) f2-2f is clear", "Location(s) f0-3f is clear", "The robot is at position f2-3f"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 68184643332045963, "group": "landmarks_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and is holding a laser. The following locations have hard rock: f1-3f and f1-4f. The following locations have soft rock: f2-2f, f2-1f, f2-4f, f2-3f, and f0-4f. The gold is at f0-4f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The robot is at position f2-3f. B. The robot is at position f2-0f. C. Location(s) f1-4f is clear. D. The robot is at position f0-4f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot is at position f2-3f", "The robot is at position f2-0f", "Location(s) f1-4f is clear", "The robot is at position f0-4f"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -8650716301187257740, "group": "landmarks_mcq", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and is holding a bomb. The following locations have hard rock: f3-1f, f1-1f, f3-3f, f1-3f, and f1-2f. The following locations have soft rock: f2-1f, f0-2f, f3-2f, f0-3f, f2-2f, and f2-3f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. The laser is at f0-3f location. B. Location(s) f0-3f is clear. C. Location(s) f1-3f is clear. D. The robot is at position f2-1f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The laser is at f0-3f location", "Location(s) f0-3f is clear", "Location(s) f1-3f is clear", "The robot is at position f2-1f"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
