{"id": -6063279510202348158, "group": "validation_gen", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-1f, f1-3f, f1-2f, f3-1f, and f3-3f. The following locations have soft rock: f0-2f, f0-3f, f2-2f, f3-2f, f0-1f, f2-3f, and f2-1f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold. The available actions are: (move ?x ?y) - travel from location ?x to location ?y, (pickup-laser ?x) - acquire the laser from location ?x, (pickup-bomb ?x) - pick up the bomb at loc ?x, (putdown-laser ?x) - place the laser at location ?x, (detonate-bomb ?x ?y) - detonate the bomb at location ?x connected to location ?y, (fire-laser ?x ?y) - fire the laser from location ?x to location ?y, and (pick-gold ?x) - retrieve gold from location ?x.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move f2-0f f1-0f) (move f1-0f f0-0f) (pickup-laser f0-0f) (fire-laser f0-0f f0-1f) (fire-laser f0-0f f0-1f) (fire-laser f0-0f f0-1f) (putdown-laser f0-0f) (pickup-bomb f0-0f) (move f0-0f f0-1f) (detonate-bomb f0-1f f0-2f) (move f0-1f f0-0f) (move f1-3f f1-2f) (move f0-0f f0-1f) (move f0-1f f0-2f) (detonate-bomb f0-2f f0-3f) (move f0-2f f0-3f) (pick-gold f0-3f)\"?", "answer": 11, "PDDL_domain": "(define (domain gold-miner-typed)\n    (:requirements :typing)\n    (:types loc)\n    (:predicates (arm-empty) (bomb-at ?x - loc)  (clear ?x - loc)  (connected ?x - loc ?y - loc)  (gold-at ?x - loc)  (hard-rock-at ?x - loc)  (holds-bomb) (holds-gold) (holds-laser) (laser-at ?x - loc)  (robot-at ?x - loc)  (soft-rock-at ?x - loc))\n    (:action detonate-bomb\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-bomb) (connected ?x ?y) (soft-rock-at ?y))\n        :effect (and (not (holds-bomb)) (arm-empty) (clear ?y) (not (soft-rock-at ?y)))\n    )\n     (:action fire-laser\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-laser) (connected ?x ?y))\n        :effect (and (clear ?y) (not (soft-rock-at ?y)) (not (gold-at ?y)) (not (hard-rock-at ?y)))\n    )\n     (:action move\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (connected ?x ?y) (clear ?y))\n        :effect (and (robot-at ?y) (not (robot-at ?x)))\n    )\n     (:action pick-gold\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (arm-empty) (gold-at ?x))\n        :effect (and (not (arm-empty)) (holds-gold))\n    )\n     (:action pickup-bomb\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (bomb-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-bomb))\n    )\n     (:action pickup-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (laser-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-laser) (not (laser-at ?x)))\n    )\n     (:action putdown-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (holds-laser))\n        :effect (and (arm-empty) (not (holds-laser)) (laser-at ?x))\n    )\n)", "PDDL_problem": "(define (problem typed-bomberman-rows4-cols4)\n    (:domain gold-miner-typed)\n    (:requirements :typing)\n    (:objects f0-0f f0-1f f0-2f f0-3f f1-0f f1-1f f1-2f f1-3f f2-0f f2-1f f2-2f f2-3f f3-0f f3-1f f3-2f f3-3f - loc)\n    (:init (arm-empty) (bomb-at f0-0f) (clear f0-0f) (clear f1-0f) (clear f2-0f) (clear f3-0f) (connected f0-0f f0-1f) (connected f0-0f f1-0f) (connected f0-1f f0-0f) (connected f0-1f f0-2f) (connected f0-1f f1-1f) (connected f0-2f f0-1f) (connected f0-2f f0-3f) (connected f0-2f f1-2f) (connected f0-3f f0-2f) (connected f0-3f f1-3f) (connected f1-0f f0-0f) (connected f1-0f f1-1f) (connected f1-0f f2-0f) (connected f1-1f f0-1f) (connected f1-1f f1-0f) (connected f1-1f f1-2f) (connected f1-1f f2-1f) (connected f1-2f f0-2f) (connected f1-2f f1-1f) (connected f1-2f f1-3f) (connected f1-2f f2-2f) (connected f1-3f f0-3f) (connected f1-3f f1-2f) (connected f1-3f f2-3f) (connected f2-0f f1-0f) (connected f2-0f f2-1f) (connected f2-0f f3-0f) (connected f2-1f f1-1f) (connected f2-1f f2-0f) (connected f2-1f f2-2f) (connected f2-1f f3-1f) (connected f2-2f f1-2f) (connected f2-2f f2-1f) (connected f2-2f f2-3f) (connected f2-2f f3-2f) (connected f2-3f f1-3f) (connected f2-3f f2-2f) (connected f2-3f f3-3f) (connected f3-0f f2-0f) (connected f3-0f f3-1f) (connected f3-1f f2-1f) (connected f3-1f f3-0f) (connected f3-1f f3-2f) (connected f3-2f f2-2f) (connected f3-2f f3-1f) (connected f3-2f f3-3f) (connected f3-3f f2-3f) (connected f3-3f f3-2f) (gold-at f0-3f) (hard-rock-at f1-1f) (hard-rock-at f1-2f) (hard-rock-at f1-3f) (hard-rock-at f3-1f) (hard-rock-at f3-3f) (laser-at f0-0f) (robot-at f2-0f) (soft-rock-at f0-1f) (soft-rock-at f0-2f) (soft-rock-at f0-3f) (soft-rock-at f2-1f) (soft-rock-at f2-2f) (soft-rock-at f2-3f) (soft-rock-at f3-2f))\n    (:goal (holds-gold))\n)"}
{"id": 6761549788614742250, "group": "validation_gen", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x3 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-2f. The following locations have soft rock: f2-1f, f0-2f, f1-1f, f0-1f, and f2-2f. The gold is at f0-2f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold. The available actions are: (move ?x ?y) - travel from location ?x to location ?y, (pickup-laser ?x) - pick up the laser at location ?x, (pickup-bomb ?x) - pick up the bomb at location ?x, (putdown-laser ?x) - put down laser at loc ?x, (detonate-bomb ?x ?y) - detonate the bomb at location ?x connected to location ?y, (fire-laser ?x ?y) - aim the laser from location ?x to location ?y, and (pick-gold ?x) - pick up the gold from location ?x.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move f2-0f f1-0f) (move f1-0f f0-0f) (pickup-bomb f0-0f) (detonate-bomb f0-0f f0-1f) (pickup-laser f0-0f) (putdown-laser f0-0f) (pickup-laser f0-0f) (putdown-laser f0-0f) (pickup-bomb f0-0f) (move f0-0f f0-1f) (putdown-laser f2-2f) (move f0-1f f0-2f) (pick-gold f0-2f) (move f0-2f f0-1f)\"?", "answer": 10, "PDDL_domain": "(define (domain gold-miner-typed)\n    (:requirements :typing)\n    (:types loc)\n    (:predicates (arm-empty) (bomb-at ?x - loc)  (clear ?x - loc)  (connected ?x - loc ?y - loc)  (gold-at ?x - loc)  (hard-rock-at ?x - loc)  (holds-bomb) (holds-gold) (holds-laser) (laser-at ?x - loc)  (robot-at ?x - loc)  (soft-rock-at ?x - loc))\n    (:action detonate-bomb\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-bomb) (connected ?x ?y) (soft-rock-at ?y))\n        :effect (and (not (holds-bomb)) (arm-empty) (clear ?y) (not (soft-rock-at ?y)))\n    )\n     (:action fire-laser\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-laser) (connected ?x ?y))\n        :effect (and (clear ?y) (not (soft-rock-at ?y)) (not (gold-at ?y)) (not (hard-rock-at ?y)))\n    )\n     (:action move\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (connected ?x ?y) (clear ?y))\n        :effect (and (robot-at ?y) (not (robot-at ?x)))\n    )\n     (:action pick-gold\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (arm-empty) (gold-at ?x))\n        :effect (and (not (arm-empty)) (holds-gold))\n    )\n     (:action pickup-bomb\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (bomb-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-bomb))\n    )\n     (:action pickup-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (laser-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-laser) (not (laser-at ?x)))\n    )\n     (:action putdown-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (holds-laser))\n        :effect (and (arm-empty) (not (holds-laser)) (laser-at ?x))\n    )\n)", "PDDL_problem": "(define (problem typed-bomberman-rows3-cols3)\n    (:domain gold-miner-typed)\n    (:requirements :typing)\n    (:objects f0-0f f0-1f f0-2f f1-0f f1-1f f1-2f f2-0f f2-1f f2-2f - loc)\n    (:init (arm-empty) (bomb-at f0-0f) (clear f0-0f) (clear f1-0f) (clear f2-0f) (connected f0-0f f0-1f) (connected f0-0f f1-0f) (connected f0-1f f0-0f) (connected f0-1f f0-2f) (connected f0-1f f1-1f) (connected f0-2f f0-1f) (connected f0-2f f1-2f) (connected f1-0f f0-0f) (connected f1-0f f1-1f) (connected f1-0f f2-0f) (connected f1-1f f0-1f) (connected f1-1f f1-0f) (connected f1-1f f1-2f) (connected f1-1f f2-1f) (connected f1-2f f0-2f) (connected f1-2f f1-1f) (connected f1-2f f2-2f) (connected f2-0f f1-0f) (connected f2-0f f2-1f) (connected f2-1f f1-1f) (connected f2-1f f2-0f) (connected f2-1f f2-2f) (connected f2-2f f1-2f) (connected f2-2f f2-1f) (gold-at f0-2f) (hard-rock-at f1-2f) (laser-at f0-0f) (robot-at f2-0f) (soft-rock-at f0-1f) (soft-rock-at f0-2f) (soft-rock-at f1-1f) (soft-rock-at f2-1f) (soft-rock-at f2-2f))\n    (:goal (holds-gold))\n)"}
{"id": 3933227415677159560, "group": "validation_gen", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-1f, f1-3f, and f1-4f. The following locations have soft rock: f0-2f, f0-3f, f2-2f, f0-1f, f0-4f, f2-3f, f2-1f, f1-2f, and f2-4f. The gold is at f0-4f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold. The available actions are: (move ?x ?y) - move from loc ?x to loc ?y, (pickup-laser ?x) - pick up laser at loc ?x, (pickup-bomb ?x) - pick up the bomb at location ?x, (putdown-laser ?x) - place the laser at location ?x, (detonate-bomb ?x ?y) - detonate bomb at loc ?x connected to loc ?y, (fire-laser ?x ?y) - fire the laser from location ?x to location ?y, and (pick-gold ?x) - pick up gold at loc ?x.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move f2-0f f1-0f) (move f1-0f f0-0f) (pickup-laser f0-0f) (fire-laser f0-0f f0-1f) (move f0-0f f0-1f) (fire-laser f0-1f f0-2f) (move f0-1f f0-2f) (move f0-2f f0-1f) (move f0-1f f0-2f) (fire-laser f0-2f f0-3f) (move f0-2f f0-1f) (putdown-laser f0-1f) (move f0-1f f0-0f) (pickup-bomb f0-0f) (move f0-0f f0-1f) (move f0-1f f0-2f) (move f0-4f f0-3f) (detonate-bomb f0-3f f0-4f) (move f0-3f f0-4f) (pick-gold f0-4f)\"?", "answer": 16, "PDDL_domain": "(define (domain gold-miner-typed)\n    (:requirements :typing)\n    (:types loc)\n    (:predicates (arm-empty) (bomb-at ?x - loc)  (clear ?x - loc)  (connected ?x - loc ?y - loc)  (gold-at ?x - loc)  (hard-rock-at ?x - loc)  (holds-bomb) (holds-gold) (holds-laser) (laser-at ?x - loc)  (robot-at ?x - loc)  (soft-rock-at ?x - loc))\n    (:action detonate-bomb\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-bomb) (connected ?x ?y) (soft-rock-at ?y))\n        :effect (and (not (holds-bomb)) (arm-empty) (clear ?y) (not (soft-rock-at ?y)))\n    )\n     (:action fire-laser\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-laser) (connected ?x ?y))\n        :effect (and (clear ?y) (not (soft-rock-at ?y)) (not (gold-at ?y)) (not (hard-rock-at ?y)))\n    )\n     (:action move\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (connected ?x ?y) (clear ?y))\n        :effect (and (robot-at ?y) (not (robot-at ?x)))\n    )\n     (:action pick-gold\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (arm-empty) (gold-at ?x))\n        :effect (and (not (arm-empty)) (holds-gold))\n    )\n     (:action pickup-bomb\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (bomb-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-bomb))\n    )\n     (:action pickup-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (laser-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-laser) (not (laser-at ?x)))\n    )\n     (:action putdown-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (holds-laser))\n        :effect (and (arm-empty) (not (holds-laser)) (laser-at ?x))\n    )\n)", "PDDL_problem": "(define (problem typed-bomberman-rows3-cols5)\n    (:domain gold-miner-typed)\n    (:requirements :typing)\n    (:objects f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f - loc)\n    (:init (arm-empty) (bomb-at f0-0f) (clear f0-0f) (clear f1-0f) (clear f2-0f) (connected f0-0f f0-1f) (connected f0-0f f1-0f) (connected f0-1f f0-0f) (connected f0-1f f0-2f) (connected f0-1f f1-1f) (connected f0-2f f0-1f) (connected f0-2f f0-3f) (connected f0-2f f1-2f) (connected f0-3f f0-2f) (connected f0-3f f0-4f) (connected f0-3f f1-3f) (connected f0-4f f0-3f) (connected f0-4f f1-4f) (connected f1-0f f0-0f) (connected f1-0f f1-1f) (connected f1-0f f2-0f) (connected f1-1f f0-1f) (connected f1-1f f1-0f) (connected f1-1f f1-2f) (connected f1-1f f2-1f) (connected f1-2f f0-2f) (connected f1-2f f1-1f) (connected f1-2f f1-3f) (connected f1-2f f2-2f) (connected f1-3f f0-3f) (connected f1-3f f1-2f) (connected f1-3f f1-4f) (connected f1-3f f2-3f) (connected f1-4f f0-4f) (connected f1-4f f1-3f) (connected f1-4f f2-4f) (connected f2-0f f1-0f) (connected f2-0f f2-1f) (connected f2-1f f1-1f) (connected f2-1f f2-0f) (connected f2-1f f2-2f) (connected f2-2f f1-2f) (connected f2-2f f2-1f) (connected f2-2f f2-3f) (connected f2-3f f1-3f) (connected f2-3f f2-2f) (connected f2-3f f2-4f) (connected f2-4f f1-4f) (connected f2-4f f2-3f) (gold-at f0-4f) (hard-rock-at f1-1f) (hard-rock-at f1-3f) (hard-rock-at f1-4f) (laser-at f0-0f) (robot-at f2-0f) (soft-rock-at f0-1f) (soft-rock-at f0-2f) (soft-rock-at f0-3f) (soft-rock-at f0-4f) (soft-rock-at f1-2f) (soft-rock-at f2-1f) (soft-rock-at f2-2f) (soft-rock-at f2-3f) (soft-rock-at f2-4f))\n    (:goal (holds-gold))\n)"}
{"id": -6196239168872252458, "group": "validation_gen", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-1f, f1-3f, and f1-4f. The following locations have soft rock: f0-2f, f0-3f, f2-2f, f0-1f, f0-4f, f2-3f, f2-1f, f1-2f, and f2-4f. The gold is at f0-4f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold. The available actions are: (move ?x ?y) - move from location ?x to location ?y, (pickup-laser ?x) - pick up laser at loc ?x, (pickup-bomb ?x) - retrieve the bomb from location ?x, (putdown-laser ?x) - place the laser at location ?x, (detonate-bomb ?x ?y) - detonate bomb at loc ?x connected to loc ?y, (fire-laser ?x ?y) - fire the laser from location ?x to location ?y, and (pick-gold ?x) - pick up the gold from location ?x.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move f2-0f f1-0f) (move f1-0f f0-0f) (pickup-bomb f0-0f) (detonate-bomb f0-0f f0-1f) (pickup-laser f0-0f) (move f0-0f f0-1f) (fire-laser f0-1f f0-2f) (move f2-4f f1-4f) (fire-laser f0-2f f0-3f) (move f0-2f f0-1f) (move f0-1f f0-0f) (putdown-laser f0-0f) (pickup-bomb f0-0f) (move f0-0f f0-1f) (move f0-1f f0-2f) (move f0-2f f0-3f) (detonate-bomb f0-3f f0-4f) (move f0-3f f0-4f) (pick-gold f0-4f) (move f0-4f f0-3f)\"?", "answer": 7, "PDDL_domain": "(define (domain gold-miner-typed)\n    (:requirements :typing)\n    (:types loc)\n    (:predicates (arm-empty) (bomb-at ?x - loc)  (clear ?x - loc)  (connected ?x - loc ?y - loc)  (gold-at ?x - loc)  (hard-rock-at ?x - loc)  (holds-bomb) (holds-gold) (holds-laser) (laser-at ?x - loc)  (robot-at ?x - loc)  (soft-rock-at ?x - loc))\n    (:action detonate-bomb\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-bomb) (connected ?x ?y) (soft-rock-at ?y))\n        :effect (and (not (holds-bomb)) (arm-empty) (clear ?y) (not (soft-rock-at ?y)))\n    )\n     (:action fire-laser\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-laser) (connected ?x ?y))\n        :effect (and (clear ?y) (not (soft-rock-at ?y)) (not (gold-at ?y)) (not (hard-rock-at ?y)))\n    )\n     (:action move\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (connected ?x ?y) (clear ?y))\n        :effect (and (robot-at ?y) (not (robot-at ?x)))\n    )\n     (:action pick-gold\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (arm-empty) (gold-at ?x))\n        :effect (and (not (arm-empty)) (holds-gold))\n    )\n     (:action pickup-bomb\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (bomb-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-bomb))\n    )\n     (:action pickup-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (laser-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-laser) (not (laser-at ?x)))\n    )\n     (:action putdown-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (holds-laser))\n        :effect (and (arm-empty) (not (holds-laser)) (laser-at ?x))\n    )\n)", "PDDL_problem": "(define (problem typed-bomberman-rows3-cols5)\n    (:domain gold-miner-typed)\n    (:requirements :typing)\n    (:objects f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f - loc)\n    (:init (arm-empty) (bomb-at f0-0f) (clear f0-0f) (clear f1-0f) (clear f2-0f) (connected f0-0f f0-1f) (connected f0-0f f1-0f) (connected f0-1f f0-0f) (connected f0-1f f0-2f) (connected f0-1f f1-1f) (connected f0-2f f0-1f) (connected f0-2f f0-3f) (connected f0-2f f1-2f) (connected f0-3f f0-2f) (connected f0-3f f0-4f) (connected f0-3f f1-3f) (connected f0-4f f0-3f) (connected f0-4f f1-4f) (connected f1-0f f0-0f) (connected f1-0f f1-1f) (connected f1-0f f2-0f) (connected f1-1f f0-1f) (connected f1-1f f1-0f) (connected f1-1f f1-2f) (connected f1-1f f2-1f) (connected f1-2f f0-2f) (connected f1-2f f1-1f) (connected f1-2f f1-3f) (connected f1-2f f2-2f) (connected f1-3f f0-3f) (connected f1-3f f1-2f) (connected f1-3f f1-4f) (connected f1-3f f2-3f) (connected f1-4f f0-4f) (connected f1-4f f1-3f) (connected f1-4f f2-4f) (connected f2-0f f1-0f) (connected f2-0f f2-1f) (connected f2-1f f1-1f) (connected f2-1f f2-0f) (connected f2-1f f2-2f) (connected f2-2f f1-2f) (connected f2-2f f2-1f) (connected f2-2f f2-3f) (connected f2-3f f1-3f) (connected f2-3f f2-2f) (connected f2-3f f2-4f) (connected f2-4f f1-4f) (connected f2-4f f2-3f) (gold-at f0-4f) (hard-rock-at f1-1f) (hard-rock-at f1-3f) (hard-rock-at f1-4f) (laser-at f0-0f) (robot-at f2-0f) (soft-rock-at f0-1f) (soft-rock-at f0-2f) (soft-rock-at f0-3f) (soft-rock-at f0-4f) (soft-rock-at f1-2f) (soft-rock-at f2-1f) (soft-rock-at f2-2f) (soft-rock-at f2-3f) (soft-rock-at f2-4f))\n    (:goal (holds-gold))\n)"}
{"id": 930629436980502896, "group": "validation_gen", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x3 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-2f. The following locations have soft rock: f2-1f, f0-2f, f1-1f, f0-1f, and f2-2f. The gold is at f0-2f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold. The available actions are: (move ?x ?y) - move from location ?x to location ?y, (pickup-laser ?x) - acquire the laser from location ?x, (pickup-bomb ?x) - retrieve the bomb from location ?x, (putdown-laser ?x) - put down the laser at location ?x, (detonate-bomb ?x ?y) - detonate bomb at loc ?x connected to loc ?y, (fire-laser ?x ?y) - fire laser from loc ?x to loc ?y, and (pick-gold ?x) - retrieve gold from location ?x.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move f2-0f f1-0f) (move f1-0f f2-0f) (move f2-0f f1-0f) (move f1-0f f0-0f) (pickup-bomb f0-0f) (move f1-0f f0-0f) (pickup-bomb f0-0f) (move f0-0f f0-1f) (detonate-bomb f0-1f f0-2f) (move f0-1f f0-2f) (pick-gold f0-2f) (move f0-2f f0-1f) (move f0-1f f0-0f) (move f0-0f f1-0f)\"?", "answer": 5, "PDDL_domain": "(define (domain gold-miner-typed)\n    (:requirements :typing)\n    (:types loc)\n    (:predicates (arm-empty) (bomb-at ?x - loc)  (clear ?x - loc)  (connected ?x - loc ?y - loc)  (gold-at ?x - loc)  (hard-rock-at ?x - loc)  (holds-bomb) (holds-gold) (holds-laser) (laser-at ?x - loc)  (robot-at ?x - loc)  (soft-rock-at ?x - loc))\n    (:action detonate-bomb\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-bomb) (connected ?x ?y) (soft-rock-at ?y))\n        :effect (and (not (holds-bomb)) (arm-empty) (clear ?y) (not (soft-rock-at ?y)))\n    )\n     (:action fire-laser\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-laser) (connected ?x ?y))\n        :effect (and (clear ?y) (not (soft-rock-at ?y)) (not (gold-at ?y)) (not (hard-rock-at ?y)))\n    )\n     (:action move\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (connected ?x ?y) (clear ?y))\n        :effect (and (robot-at ?y) (not (robot-at ?x)))\n    )\n     (:action pick-gold\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (arm-empty) (gold-at ?x))\n        :effect (and (not (arm-empty)) (holds-gold))\n    )\n     (:action pickup-bomb\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (bomb-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-bomb))\n    )\n     (:action pickup-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (laser-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-laser) (not (laser-at ?x)))\n    )\n     (:action putdown-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (holds-laser))\n        :effect (and (arm-empty) (not (holds-laser)) (laser-at ?x))\n    )\n)", "PDDL_problem": "(define (problem typed-bomberman-rows3-cols3)\n    (:domain gold-miner-typed)\n    (:requirements :typing)\n    (:objects f0-0f f0-1f f0-2f f1-0f f1-1f f1-2f f2-0f f2-1f f2-2f - loc)\n    (:init (arm-empty) (bomb-at f0-0f) (clear f0-0f) (clear f1-0f) (clear f2-0f) (connected f0-0f f0-1f) (connected f0-0f f1-0f) (connected f0-1f f0-0f) (connected f0-1f f0-2f) (connected f0-1f f1-1f) (connected f0-2f f0-1f) (connected f0-2f f1-2f) (connected f1-0f f0-0f) (connected f1-0f f1-1f) (connected f1-0f f2-0f) (connected f1-1f f0-1f) (connected f1-1f f1-0f) (connected f1-1f f1-2f) (connected f1-1f f2-1f) (connected f1-2f f0-2f) (connected f1-2f f1-1f) (connected f1-2f f2-2f) (connected f2-0f f1-0f) (connected f2-0f f2-1f) (connected f2-1f f1-1f) (connected f2-1f f2-0f) (connected f2-1f f2-2f) (connected f2-2f f1-2f) (connected f2-2f f2-1f) (gold-at f0-2f) (hard-rock-at f1-2f) (laser-at f0-0f) (robot-at f2-0f) (soft-rock-at f0-1f) (soft-rock-at f0-2f) (soft-rock-at f1-1f) (soft-rock-at f2-1f) (soft-rock-at f2-2f))\n    (:goal (holds-gold))\n)"}
{"id": 7976457220319077038, "group": "validation_gen", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f0-3f, f2-2f, and f2-1f. The following locations have soft rock: f0-2f, f1-1f, f1-3f, f0-1f, f1-2f, and f2-3f. The gold is at f1-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold. The available actions are: (move ?x ?y) - move from loc ?x to loc ?y, (pickup-laser ?x) - acquire the laser from location ?x, (pickup-bomb ?x) - pick up the bomb at loc ?x, (putdown-laser ?x) - put down the laser at location ?x, (detonate-bomb ?x ?y) - detonate bomb at loc ?x connected to loc ?y, (fire-laser ?x ?y) - aim the laser from location ?x to location ?y, and (pick-gold ?x) - retrieve gold from location ?x.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move f2-0f f1-0f) (move f1-0f f0-0f) (pickup-laser f0-0f) (fire-laser f0-0f f0-1f) (move f0-0f f1-0f) (fire-laser f1-0f f1-1f) (move f1-0f f1-1f) (fire-laser f1-1f f1-2f) (putdown-laser f1-1f) (move f1-1f f1-0f) (move f1-0f f0-0f) (pickup-bomb f0-0f) (fire-laser f1-3f f1-2f) (move f1-0f f1-1f) (move f1-1f f1-2f) (detonate-bomb f1-2f f1-3f) (move f1-2f f1-3f) (pick-gold f1-3f) (move f1-3f f1-2f)\"?", "answer": 12, "PDDL_domain": "(define (domain gold-miner-typed)\n    (:requirements :typing)\n    (:types loc)\n    (:predicates (arm-empty) (bomb-at ?x - loc)  (clear ?x - loc)  (connected ?x - loc ?y - loc)  (gold-at ?x - loc)  (hard-rock-at ?x - loc)  (holds-bomb) (holds-gold) (holds-laser) (laser-at ?x - loc)  (robot-at ?x - loc)  (soft-rock-at ?x - loc))\n    (:action detonate-bomb\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-bomb) (connected ?x ?y) (soft-rock-at ?y))\n        :effect (and (not (holds-bomb)) (arm-empty) (clear ?y) (not (soft-rock-at ?y)))\n    )\n     (:action fire-laser\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-laser) (connected ?x ?y))\n        :effect (and (clear ?y) (not (soft-rock-at ?y)) (not (gold-at ?y)) (not (hard-rock-at ?y)))\n    )\n     (:action move\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (connected ?x ?y) (clear ?y))\n        :effect (and (robot-at ?y) (not (robot-at ?x)))\n    )\n     (:action pick-gold\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (arm-empty) (gold-at ?x))\n        :effect (and (not (arm-empty)) (holds-gold))\n    )\n     (:action pickup-bomb\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (bomb-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-bomb))\n    )\n     (:action pickup-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (laser-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-laser) (not (laser-at ?x)))\n    )\n     (:action putdown-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (holds-laser))\n        :effect (and (arm-empty) (not (holds-laser)) (laser-at ?x))\n    )\n)", "PDDL_problem": "(define (problem typed-bomberman-rows3-cols4)\n    (:domain gold-miner-typed)\n    (:requirements :typing)\n    (:objects f0-0f f0-1f f0-2f f0-3f f1-0f f1-1f f1-2f f1-3f f2-0f f2-1f f2-2f f2-3f - loc)\n    (:init (arm-empty) (bomb-at f0-0f) (clear f0-0f) (clear f1-0f) (clear f2-0f) (connected f0-0f f0-1f) (connected f0-0f f1-0f) (connected f0-1f f0-0f) (connected f0-1f f0-2f) (connected f0-1f f1-1f) (connected f0-2f f0-1f) (connected f0-2f f0-3f) (connected f0-2f f1-2f) (connected f0-3f f0-2f) (connected f0-3f f1-3f) (connected f1-0f f0-0f) (connected f1-0f f1-1f) (connected f1-0f f2-0f) (connected f1-1f f0-1f) (connected f1-1f f1-0f) (connected f1-1f f1-2f) (connected f1-1f f2-1f) (connected f1-2f f0-2f) (connected f1-2f f1-1f) (connected f1-2f f1-3f) (connected f1-2f f2-2f) (connected f1-3f f0-3f) (connected f1-3f f1-2f) (connected f1-3f f2-3f) (connected f2-0f f1-0f) (connected f2-0f f2-1f) (connected f2-1f f1-1f) (connected f2-1f f2-0f) (connected f2-1f f2-2f) (connected f2-2f f1-2f) (connected f2-2f f2-1f) (connected f2-2f f2-3f) (connected f2-3f f1-3f) (connected f2-3f f2-2f) (gold-at f1-3f) (hard-rock-at f0-3f) (hard-rock-at f2-1f) (hard-rock-at f2-2f) (laser-at f0-0f) (robot-at f2-0f) (soft-rock-at f0-1f) (soft-rock-at f0-2f) (soft-rock-at f1-1f) (soft-rock-at f1-2f) (soft-rock-at f1-3f) (soft-rock-at f2-3f))\n    (:goal (holds-gold))\n)"}
{"id": -5055535071951284183, "group": "validation_gen", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-1f, f1-3f, f1-2f, f3-1f, and f3-3f. The following locations have soft rock: f0-2f, f0-3f, f2-2f, f3-2f, f0-1f, f2-3f, and f2-1f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold. The available actions are: (move ?x ?y) - travel from location ?x to location ?y, (pickup-laser ?x) - pick up laser at loc ?x, (pickup-bomb ?x) - pick up bomb at loc ?x, (putdown-laser ?x) - put down the laser at location ?x, (detonate-bomb ?x ?y) - detonate the bomb at location ?x connected to location ?y, (fire-laser ?x ?y) - fire the laser from location ?x to location ?y, and (pick-gold ?x) - pick up the gold from location ?x.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move f2-0f f1-0f) (move f1-0f f0-0f) (pickup-laser f0-0f) (fire-laser f0-0f f0-1f) (putdown-laser f0-2f) (fire-laser f0-1f f0-2f) (fire-laser f0-1f f0-2f) (move f0-1f f0-0f) (putdown-laser f0-0f) (move f0-0f f0-1f) (move f0-1f f0-0f) (pickup-bomb f0-0f) (move f0-0f f0-1f) (move f0-1f f0-2f) (detonate-bomb f0-2f f0-3f) (move f0-2f f0-3f) (pick-gold f0-3f)\"?", "answer": 4, "PDDL_domain": "(define (domain gold-miner-typed)\n    (:requirements :typing)\n    (:types loc)\n    (:predicates (arm-empty) (bomb-at ?x - loc)  (clear ?x - loc)  (connected ?x - loc ?y - loc)  (gold-at ?x - loc)  (hard-rock-at ?x - loc)  (holds-bomb) (holds-gold) (holds-laser) (laser-at ?x - loc)  (robot-at ?x - loc)  (soft-rock-at ?x - loc))\n    (:action detonate-bomb\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-bomb) (connected ?x ?y) (soft-rock-at ?y))\n        :effect (and (not (holds-bomb)) (arm-empty) (clear ?y) (not (soft-rock-at ?y)))\n    )\n     (:action fire-laser\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-laser) (connected ?x ?y))\n        :effect (and (clear ?y) (not (soft-rock-at ?y)) (not (gold-at ?y)) (not (hard-rock-at ?y)))\n    )\n     (:action move\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (connected ?x ?y) (clear ?y))\n        :effect (and (robot-at ?y) (not (robot-at ?x)))\n    )\n     (:action pick-gold\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (arm-empty) (gold-at ?x))\n        :effect (and (not (arm-empty)) (holds-gold))\n    )\n     (:action pickup-bomb\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (bomb-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-bomb))\n    )\n     (:action pickup-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (laser-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-laser) (not (laser-at ?x)))\n    )\n     (:action putdown-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (holds-laser))\n        :effect (and (arm-empty) (not (holds-laser)) (laser-at ?x))\n    )\n)", "PDDL_problem": "(define (problem typed-bomberman-rows4-cols4)\n    (:domain gold-miner-typed)\n    (:requirements :typing)\n    (:objects f0-0f f0-1f f0-2f f0-3f f1-0f f1-1f f1-2f f1-3f f2-0f f2-1f f2-2f f2-3f f3-0f f3-1f f3-2f f3-3f - loc)\n    (:init (arm-empty) (bomb-at f0-0f) (clear f0-0f) (clear f1-0f) (clear f2-0f) (clear f3-0f) (connected f0-0f f0-1f) (connected f0-0f f1-0f) (connected f0-1f f0-0f) (connected f0-1f f0-2f) (connected f0-1f f1-1f) (connected f0-2f f0-1f) (connected f0-2f f0-3f) (connected f0-2f f1-2f) (connected f0-3f f0-2f) (connected f0-3f f1-3f) (connected f1-0f f0-0f) (connected f1-0f f1-1f) (connected f1-0f f2-0f) (connected f1-1f f0-1f) (connected f1-1f f1-0f) (connected f1-1f f1-2f) (connected f1-1f f2-1f) (connected f1-2f f0-2f) (connected f1-2f f1-1f) (connected f1-2f f1-3f) (connected f1-2f f2-2f) (connected f1-3f f0-3f) (connected f1-3f f1-2f) (connected f1-3f f2-3f) (connected f2-0f f1-0f) (connected f2-0f f2-1f) (connected f2-0f f3-0f) (connected f2-1f f1-1f) (connected f2-1f f2-0f) (connected f2-1f f2-2f) (connected f2-1f f3-1f) (connected f2-2f f1-2f) (connected f2-2f f2-1f) (connected f2-2f f2-3f) (connected f2-2f f3-2f) (connected f2-3f f1-3f) (connected f2-3f f2-2f) (connected f2-3f f3-3f) (connected f3-0f f2-0f) (connected f3-0f f3-1f) (connected f3-1f f2-1f) (connected f3-1f f3-0f) (connected f3-1f f3-2f) (connected f3-2f f2-2f) (connected f3-2f f3-1f) (connected f3-2f f3-3f) (connected f3-3f f2-3f) (connected f3-3f f3-2f) (gold-at f0-3f) (hard-rock-at f1-1f) (hard-rock-at f1-2f) (hard-rock-at f1-3f) (hard-rock-at f3-1f) (hard-rock-at f3-3f) (laser-at f0-0f) (robot-at f2-0f) (soft-rock-at f0-1f) (soft-rock-at f0-2f) (soft-rock-at f0-3f) (soft-rock-at f2-1f) (soft-rock-at f2-2f) (soft-rock-at f2-3f) (soft-rock-at f3-2f))\n    (:goal (holds-gold))\n)"}
{"id": 9007379300494052855, "group": "validation_gen", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f0-3f, f2-2f, and f2-1f. The following locations have soft rock: f0-2f, f1-1f, f1-3f, f0-1f, f1-2f, and f2-3f. The gold is at f1-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold. The available actions are: (move ?x ?y) - move from location ?x to location ?y, (pickup-laser ?x) - pick up the laser at location ?x, (pickup-bomb ?x) - retrieve the bomb from location ?x, (putdown-laser ?x) - place the laser at location ?x, (detonate-bomb ?x ?y) - detonate bomb at loc ?x connected to loc ?y, (fire-laser ?x ?y) - fire laser from loc ?x to loc ?y, and (pick-gold ?x) - pick up gold at location ?x.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move f2-0f f1-0f) (move f2-1f f1-1f) (pickup-laser f0-0f) (fire-laser f0-0f f0-1f) (move f0-0f f1-0f) (fire-laser f1-0f f1-1f) (move f1-0f f1-1f) (fire-laser f1-1f f1-2f) (move f1-1f f1-0f) (move f1-0f f0-0f) (putdown-laser f0-0f) (pickup-bomb f0-0f) (move f0-0f f1-0f) (move f1-0f f1-1f) (move f1-1f f1-2f) (detonate-bomb f1-2f f1-3f) (move f1-2f f1-3f) (pick-gold f1-3f) (move f1-3f f1-2f)\"?", "answer": 1, "PDDL_domain": "(define (domain gold-miner-typed)\n    (:requirements :typing)\n    (:types loc)\n    (:predicates (arm-empty) (bomb-at ?x - loc)  (clear ?x - loc)  (connected ?x - loc ?y - loc)  (gold-at ?x - loc)  (hard-rock-at ?x - loc)  (holds-bomb) (holds-gold) (holds-laser) (laser-at ?x - loc)  (robot-at ?x - loc)  (soft-rock-at ?x - loc))\n    (:action detonate-bomb\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-bomb) (connected ?x ?y) (soft-rock-at ?y))\n        :effect (and (not (holds-bomb)) (arm-empty) (clear ?y) (not (soft-rock-at ?y)))\n    )\n     (:action fire-laser\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-laser) (connected ?x ?y))\n        :effect (and (clear ?y) (not (soft-rock-at ?y)) (not (gold-at ?y)) (not (hard-rock-at ?y)))\n    )\n     (:action move\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (connected ?x ?y) (clear ?y))\n        :effect (and (robot-at ?y) (not (robot-at ?x)))\n    )\n     (:action pick-gold\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (arm-empty) (gold-at ?x))\n        :effect (and (not (arm-empty)) (holds-gold))\n    )\n     (:action pickup-bomb\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (bomb-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-bomb))\n    )\n     (:action pickup-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (laser-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-laser) (not (laser-at ?x)))\n    )\n     (:action putdown-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (holds-laser))\n        :effect (and (arm-empty) (not (holds-laser)) (laser-at ?x))\n    )\n)", "PDDL_problem": "(define (problem typed-bomberman-rows3-cols4)\n    (:domain gold-miner-typed)\n    (:requirements :typing)\n    (:objects f0-0f f0-1f f0-2f f0-3f f1-0f f1-1f f1-2f f1-3f f2-0f f2-1f f2-2f f2-3f - loc)\n    (:init (arm-empty) (bomb-at f0-0f) (clear f0-0f) (clear f1-0f) (clear f2-0f) (connected f0-0f f0-1f) (connected f0-0f f1-0f) (connected f0-1f f0-0f) (connected f0-1f f0-2f) (connected f0-1f f1-1f) (connected f0-2f f0-1f) (connected f0-2f f0-3f) (connected f0-2f f1-2f) (connected f0-3f f0-2f) (connected f0-3f f1-3f) (connected f1-0f f0-0f) (connected f1-0f f1-1f) (connected f1-0f f2-0f) (connected f1-1f f0-1f) (connected f1-1f f1-0f) (connected f1-1f f1-2f) (connected f1-1f f2-1f) (connected f1-2f f0-2f) (connected f1-2f f1-1f) (connected f1-2f f1-3f) (connected f1-2f f2-2f) (connected f1-3f f0-3f) (connected f1-3f f1-2f) (connected f1-3f f2-3f) (connected f2-0f f1-0f) (connected f2-0f f2-1f) (connected f2-1f f1-1f) (connected f2-1f f2-0f) (connected f2-1f f2-2f) (connected f2-2f f1-2f) (connected f2-2f f2-1f) (connected f2-2f f2-3f) (connected f2-3f f1-3f) (connected f2-3f f2-2f) (gold-at f1-3f) (hard-rock-at f0-3f) (hard-rock-at f2-1f) (hard-rock-at f2-2f) (laser-at f0-0f) (robot-at f2-0f) (soft-rock-at f0-1f) (soft-rock-at f0-2f) (soft-rock-at f1-1f) (soft-rock-at f1-2f) (soft-rock-at f1-3f) (soft-rock-at f2-3f))\n    (:goal (holds-gold))\n)"}
{"id": -1686580793434714376, "group": "validation_gen", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-1f, f1-3f, f1-2f, f3-1f, and f3-3f. The following locations have soft rock: f0-2f, f0-3f, f2-2f, f3-2f, f0-1f, f2-3f, and f2-1f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold. The available actions are: (move ?x ?y) - travel from location ?x to location ?y, (pickup-laser ?x) - pick up laser at loc ?x, (pickup-bomb ?x) - pick up the bomb at location ?x, (putdown-laser ?x) - put down the laser at location ?x, (detonate-bomb ?x ?y) - detonate the bomb at location ?x connected to location ?y, (fire-laser ?x ?y) - fire laser from loc ?x to loc ?y, and (pick-gold ?x) - retrieve gold from location ?x.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move f2-0f f1-0f) (move f1-0f f0-0f) (pickup-bomb f0-0f) (detonate-bomb f0-0f f0-1f) (pickup-laser f0-0f) (fire-laser f0-0f f0-1f) (move f3-3f f3-2f) (fire-laser f0-1f f0-2f) (move f0-1f f0-0f) (putdown-laser f0-0f) (pickup-bomb f0-0f) (move f0-0f f0-1f) (move f0-1f f0-2f) (detonate-bomb f0-2f f0-3f) (move f0-2f f0-3f) (pick-gold f0-3f) (move f0-3f f0-2f)\"?", "answer": 6, "PDDL_domain": "(define (domain gold-miner-typed)\n    (:requirements :typing)\n    (:types loc)\n    (:predicates (arm-empty) (bomb-at ?x - loc)  (clear ?x - loc)  (connected ?x - loc ?y - loc)  (gold-at ?x - loc)  (hard-rock-at ?x - loc)  (holds-bomb) (holds-gold) (holds-laser) (laser-at ?x - loc)  (robot-at ?x - loc)  (soft-rock-at ?x - loc))\n    (:action detonate-bomb\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-bomb) (connected ?x ?y) (soft-rock-at ?y))\n        :effect (and (not (holds-bomb)) (arm-empty) (clear ?y) (not (soft-rock-at ?y)))\n    )\n     (:action fire-laser\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-laser) (connected ?x ?y))\n        :effect (and (clear ?y) (not (soft-rock-at ?y)) (not (gold-at ?y)) (not (hard-rock-at ?y)))\n    )\n     (:action move\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (connected ?x ?y) (clear ?y))\n        :effect (and (robot-at ?y) (not (robot-at ?x)))\n    )\n     (:action pick-gold\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (arm-empty) (gold-at ?x))\n        :effect (and (not (arm-empty)) (holds-gold))\n    )\n     (:action pickup-bomb\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (bomb-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-bomb))\n    )\n     (:action pickup-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (laser-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-laser) (not (laser-at ?x)))\n    )\n     (:action putdown-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (holds-laser))\n        :effect (and (arm-empty) (not (holds-laser)) (laser-at ?x))\n    )\n)", "PDDL_problem": "(define (problem typed-bomberman-rows4-cols4)\n    (:domain gold-miner-typed)\n    (:requirements :typing)\n    (:objects f0-0f f0-1f f0-2f f0-3f f1-0f f1-1f f1-2f f1-3f f2-0f f2-1f f2-2f f2-3f f3-0f f3-1f f3-2f f3-3f - loc)\n    (:init (arm-empty) (bomb-at f0-0f) (clear f0-0f) (clear f1-0f) (clear f2-0f) (clear f3-0f) (connected f0-0f f0-1f) (connected f0-0f f1-0f) (connected f0-1f f0-0f) (connected f0-1f f0-2f) (connected f0-1f f1-1f) (connected f0-2f f0-1f) (connected f0-2f f0-3f) (connected f0-2f f1-2f) (connected f0-3f f0-2f) (connected f0-3f f1-3f) (connected f1-0f f0-0f) (connected f1-0f f1-1f) (connected f1-0f f2-0f) (connected f1-1f f0-1f) (connected f1-1f f1-0f) (connected f1-1f f1-2f) (connected f1-1f f2-1f) (connected f1-2f f0-2f) (connected f1-2f f1-1f) (connected f1-2f f1-3f) (connected f1-2f f2-2f) (connected f1-3f f0-3f) (connected f1-3f f1-2f) (connected f1-3f f2-3f) (connected f2-0f f1-0f) (connected f2-0f f2-1f) (connected f2-0f f3-0f) (connected f2-1f f1-1f) (connected f2-1f f2-0f) (connected f2-1f f2-2f) (connected f2-1f f3-1f) (connected f2-2f f1-2f) (connected f2-2f f2-1f) (connected f2-2f f2-3f) (connected f2-2f f3-2f) (connected f2-3f f1-3f) (connected f2-3f f2-2f) (connected f2-3f f3-3f) (connected f3-0f f2-0f) (connected f3-0f f3-1f) (connected f3-1f f2-1f) (connected f3-1f f3-0f) (connected f3-1f f3-2f) (connected f3-2f f2-2f) (connected f3-2f f3-1f) (connected f3-2f f3-3f) (connected f3-3f f2-3f) (connected f3-3f f3-2f) (gold-at f0-3f) (hard-rock-at f1-1f) (hard-rock-at f1-2f) (hard-rock-at f1-3f) (hard-rock-at f3-1f) (hard-rock-at f3-3f) (laser-at f0-0f) (robot-at f2-0f) (soft-rock-at f0-1f) (soft-rock-at f0-2f) (soft-rock-at f0-3f) (soft-rock-at f2-1f) (soft-rock-at f2-2f) (soft-rock-at f2-3f) (soft-rock-at f3-2f))\n    (:goal (holds-gold))\n)"}
{"id": 3435309204228617380, "group": "validation_gen", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-1f, f1-3f, and f1-4f. The following locations have soft rock: f0-2f, f0-3f, f2-2f, f0-1f, f0-4f, f2-3f, f2-1f, f1-2f, and f2-4f. The gold is at f0-4f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold. The available actions are: (move ?x ?y) - move to location ?y from location ?x, (pickup-laser ?x) - acquire the laser from location ?x, (pickup-bomb ?x) - pick up the bomb at loc ?x, (putdown-laser ?x) - put down laser at loc ?x, (detonate-bomb ?x ?y) - detonate bomb at loc ?x connected to loc ?y, (fire-laser ?x ?y) - direct the laser from location ?x to location ?y, and (pick-gold ?x) - pick up gold at loc ?x.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move f2-0f f1-0f) (move f1-0f f0-0f) (pickup-laser f0-0f) (fire-laser f0-0f f0-1f) (move f0-0f f0-1f) (fire-laser f0-1f f0-2f) (move f0-1f f0-2f) (fire-laser f0-2f f0-3f) (fire-laser f0-2f f1-2f) (putdown-laser f0-2f) (move f0-2f f0-1f) (move f0-1f f0-0f) (pickup-bomb f0-0f) (fire-laser f1-1f f1-2f) (move f0-1f f0-2f) (move f0-2f f0-3f) (detonate-bomb f0-3f f0-4f) (move f0-3f f0-4f) (pick-gold f0-4f) (move f0-4f f0-3f)\"?", "answer": 13, "PDDL_domain": "(define (domain gold-miner-typed)\n    (:requirements :typing)\n    (:types loc)\n    (:predicates (arm-empty) (bomb-at ?x - loc)  (clear ?x - loc)  (connected ?x - loc ?y - loc)  (gold-at ?x - loc)  (hard-rock-at ?x - loc)  (holds-bomb) (holds-gold) (holds-laser) (laser-at ?x - loc)  (robot-at ?x - loc)  (soft-rock-at ?x - loc))\n    (:action detonate-bomb\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-bomb) (connected ?x ?y) (soft-rock-at ?y))\n        :effect (and (not (holds-bomb)) (arm-empty) (clear ?y) (not (soft-rock-at ?y)))\n    )\n     (:action fire-laser\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-laser) (connected ?x ?y))\n        :effect (and (clear ?y) (not (soft-rock-at ?y)) (not (gold-at ?y)) (not (hard-rock-at ?y)))\n    )\n     (:action move\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (connected ?x ?y) (clear ?y))\n        :effect (and (robot-at ?y) (not (robot-at ?x)))\n    )\n     (:action pick-gold\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (arm-empty) (gold-at ?x))\n        :effect (and (not (arm-empty)) (holds-gold))\n    )\n     (:action pickup-bomb\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (bomb-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-bomb))\n    )\n     (:action pickup-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (laser-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-laser) (not (laser-at ?x)))\n    )\n     (:action putdown-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (holds-laser))\n        :effect (and (arm-empty) (not (holds-laser)) (laser-at ?x))\n    )\n)", "PDDL_problem": "(define (problem typed-bomberman-rows3-cols5)\n    (:domain gold-miner-typed)\n    (:requirements :typing)\n    (:objects f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f - loc)\n    (:init (arm-empty) (bomb-at f0-0f) (clear f0-0f) (clear f1-0f) (clear f2-0f) (connected f0-0f f0-1f) (connected f0-0f f1-0f) (connected f0-1f f0-0f) (connected f0-1f f0-2f) (connected f0-1f f1-1f) (connected f0-2f f0-1f) (connected f0-2f f0-3f) (connected f0-2f f1-2f) (connected f0-3f f0-2f) (connected f0-3f f0-4f) (connected f0-3f f1-3f) (connected f0-4f f0-3f) (connected f0-4f f1-4f) (connected f1-0f f0-0f) (connected f1-0f f1-1f) (connected f1-0f f2-0f) (connected f1-1f f0-1f) (connected f1-1f f1-0f) (connected f1-1f f1-2f) (connected f1-1f f2-1f) (connected f1-2f f0-2f) (connected f1-2f f1-1f) (connected f1-2f f1-3f) (connected f1-2f f2-2f) (connected f1-3f f0-3f) (connected f1-3f f1-2f) (connected f1-3f f1-4f) (connected f1-3f f2-3f) (connected f1-4f f0-4f) (connected f1-4f f1-3f) (connected f1-4f f2-4f) (connected f2-0f f1-0f) (connected f2-0f f2-1f) (connected f2-1f f1-1f) (connected f2-1f f2-0f) (connected f2-1f f2-2f) (connected f2-2f f1-2f) (connected f2-2f f2-1f) (connected f2-2f f2-3f) (connected f2-3f f1-3f) (connected f2-3f f2-2f) (connected f2-3f f2-4f) (connected f2-4f f1-4f) (connected f2-4f f2-3f) (gold-at f0-4f) (hard-rock-at f1-1f) (hard-rock-at f1-3f) (hard-rock-at f1-4f) (laser-at f0-0f) (robot-at f2-0f) (soft-rock-at f0-1f) (soft-rock-at f0-2f) (soft-rock-at f0-3f) (soft-rock-at f0-4f) (soft-rock-at f1-2f) (soft-rock-at f2-1f) (soft-rock-at f2-2f) (soft-rock-at f2-3f) (soft-rock-at f2-4f))\n    (:goal (holds-gold))\n)"}
