{"id": 5568815713613709562, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f0-4f and its arm is empty. All the positions are open except the following: f2-1f has shape1 shaped lock, f3-4f has shape1 shaped lock. Key key1-0 is at position f1-2f. Key key1-1 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key1-1 is at f4-4f location and Key key1-0 is at f1-0f location.", "question": "Given the plan: \"travel from the current position f0-4f to the next position f0-3f, travel from the current position f0-3f to the next position f1-3f, travel from the current position f1-3f to the next position f1-2f, retrieve the key key1-0 from its current position f1-2f, travel from the current position f1-2f to the next position f1-1f, put down the key key1-0 at the current position f1-1f, retrieve the key key1-0 from its current position f1-1f, travel from the current position f1-1f to the next position f1-0f, put down the key key1-0 at the current position f1-0f, travel from the current position f1-0f to the next position f2-0f, travel from the current position f2-0f to the next position f3-0f, retrieve the key key1-1 from its current position f3-0f, travel from the current position f3-0f to the next position f4-0f, travel from the current position f4-0f to the next position f4-1f, travel from the current position f4-1f to the next position f4-2f, travel from the current position f4-2f to the next position f4-3f, travel from the current position f4-3f to the next position f4-4f, put down the key key1-1 at the current position f4-4f\"; can the following action be removed from this plan and still have a valid plan: retrieve the key key1-0 from its current position f1-1f?", "answer": "no"}
{"id": -4510498081521909201, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-3f and its arm is empty. All the positions are open except the following: f2-0f has shape0 shaped lock, f2-2f has shape0 shaped lock, f4-2f has shape0 shaped lock. Key key0-1 is at position f2-2f. Key key0-0 is at position f1-3f. Key key0-2 is at position f0-4f. The goal is to reach a state where the following facts hold: Key key0-0 is at f3-3f location, Key key0-2 is at f3-3f location, and Key key0-1 is at f1-2f location.", "question": "Given the plan: \"move from f2-3f to f2-4f, move from f2-4f to f1-4f, move from f1-4f to f0-4f, pick up key key0-2 from place f0-4f, move from f0-4f to f1-4f, move from f1-4f to f2-4f, move from f2-4f to f2-3f, unlock the place f2-2f with the key key0-2 of the shape shape0 from the current position place f2-3f, move from f2-3f to f3-3f, put the key key0-2 at the current position place f3-3f, move from f3-3f to f3-2f, move from f3-2f to f2-2f, pick up key key0-1 from place f2-2f, move from f2-2f to f1-2f, put the key key0-1 at the current position place f1-2f, move from f1-2f to f1-3f, pick up key key0-0 from place f1-3f, move from f1-3f to f2-3f, move from f2-3f to f3-3f, pick up the key key0-2 from the current position f3-3f and loose the key key0-0 which is being held, put the key key0-2 at the current position place f3-3f\"; can the following action be removed from this plan and still have a valid plan: move from f2-3f to f3-3f?", "answer": "no"}
{"id": 7346202495984112720, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-3 is of shape shape0.  Currently, the robot is at position f3-1f and its arm is empty. All the positions are open except the following: f0-2f has shape0 shaped lock, f0-0f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-1 is at position f4-2f. Key key0-3 is at position f3-1f. Key key0-0 is at position f0-0f. Key key0-2 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-4f location, Key key0-3 is at f0-1f location, Key key0-0 is at f0-1f location, and Key key0-2 is at f4-0f location.", "question": "Given the plan: \"acquire the key key0-3 from the place f3-1f, put the key key0-3 at the current position place f3-1f, acquire the key key0-3 from the place f3-1f, transition from the current position f3-1f to the next position f2-1f, transition from the current position f2-1f to the next position f1-1f, transition from the current position f1-1f to the next position f0-1f, use the key key0-3 of shape shape0 to unlock the place f0-0f from its current position f0-1f, put the key key0-3 at the current position place f0-1f, transition from the current position f0-1f to the next position f0-0f, acquire the key key0-0 from the place f0-0f, transition from the current position f0-0f to the next position f0-1f, put the key key0-0 at the current position place f0-1f, transition from the current position f0-1f to the next position f1-1f, transition from the current position f1-1f to the next position f2-1f, transition from the current position f2-1f to the next position f3-1f, transition from the current position f3-1f to the next position f3-0f, acquire the key key0-2 from the place f3-0f, use the key key0-2 of shape shape0 to unlock the place f4-0f from its current position f3-0f, transition from the current position f3-0f to the next position f4-0f, put the key key0-2 at the current position place f4-0f, transition from the current position f4-0f to the next position f4-1f, transition from the current position f4-1f to the next position f4-2f, acquire the key key0-1 from the place f4-2f, transition from the current position f4-2f to the next position f3-2f, transition from the current position f3-2f to the next position f2-2f, transition from the current position f2-2f to the next position f2-3f, transition from the current position f2-3f to the next position f2-4f, transition from the current position f2-4f to the next position f1-4f, put the key key0-1 at the current position place f1-4f\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: acquire the key key0-3 from the place f3-1f and put the key key0-3 at the current position place f3-1f?", "answer": "yes"}
{"id": 8530234960789714198, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-3 is of shape shape0.  Currently, the robot is at position f3-1f and its arm is empty. All the positions are open except the following: f0-2f has shape0 shaped lock, f0-0f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-1 is at position f4-2f. Key key0-3 is at position f3-1f. Key key0-0 is at position f0-0f. Key key0-2 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-4f location, Key key0-3 is at f0-1f location, Key key0-0 is at f0-1f location, and Key key0-2 is at f4-0f location.", "question": "Given the plan: \"retrieve the key key0-3 from its current position f3-1f, put the key key0-3 at the current position place f3-1f, retrieve the key key0-3 from its current position f3-1f, move from f3-1f to f2-1f, move from f2-1f to f1-1f, move from f1-1f to f0-1f, unlock the place f0-0f with key key0-3 of shape shape0 from the current position place f0-1f, put the key key0-3 at the current position place f0-1f, move from f0-1f to f0-0f, retrieve the key key0-0 from its current position f0-0f, move from f0-0f to f0-1f, put the key key0-0 at the current position place f0-1f, move from f0-1f to f1-1f, move from f1-1f to f2-1f, move from f2-1f to f3-1f, move from f3-1f to f3-0f, retrieve the key key0-2 from its current position f3-0f, unlock the place f4-0f with key key0-2 of shape shape0 from the current position place f3-0f, move from f3-0f to f4-0f, put the key key0-2 at the current position place f4-0f, move from f4-0f to f4-1f, move from f4-1f to f4-2f, retrieve the key key0-1 from its current position f4-2f, move from f4-2f to f4-3f, move from f4-3f to f3-3f, move from f3-3f to f2-3f, move from f2-3f to f1-3f, move from f1-3f to f1-4f, put the key key0-1 at the current position place f1-4f\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: retrieve the key key0-3 from its current position f3-1f and put the key key0-3 at the current position place f3-1f?", "answer": "yes"}
{"id": -7161641395773530309, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f0-4f and its arm is empty. All the positions are open except the following: f2-1f has shape1 shaped lock, f3-4f has shape1 shaped lock. Key key1-0 is at position f1-2f. Key key1-1 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key1-1 is at f4-4f location and Key key1-0 is at f1-0f location.", "question": "Given the plan: \"travel from the current position f0-4f to the next position f0-3f, travel from the current position f0-3f to the next position f1-3f, travel from the current position f1-3f to the next position f1-2f, acquire the key key1-0 from the place f1-2f, travel from the current position f1-2f to the next position f1-1f, travel from the current position f1-1f to the next position f1-0f, place the key key1-0 at the current position place f1-0f, travel from the current position f1-0f to the next position f2-0f, travel from the current position f2-0f to the next position f3-0f, acquire the key key1-1 from the place f3-0f, travel from the current position f3-0f to the next position f4-0f, travel from the current position f4-0f to the next position f4-1f, travel from the current position f4-1f to the next position f4-2f, travel from the current position f4-2f to the next position f4-3f, place the key key1-1 at the current position place f4-3f, acquire the key key1-1 from the place f4-3f, travel from the current position f4-3f to the next position f4-4f, place the key key1-1 at the current position place f4-4f\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: place the key key1-1 at the current position place f4-3f and acquire the key key1-1 from the place f4-3f?", "answer": "yes"}
{"id": -1796231726266805070, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f0-4f and its arm is empty. All the positions are open except the following: f2-1f has shape1 shaped lock, f3-4f has shape1 shaped lock. Key key1-0 is at position f1-2f. Key key1-1 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key1-1 is at f4-4f location and Key key1-0 is at f1-0f location.", "question": "Given the plan: \"transition from the current position f0-4f to the next position f1-4f, transition from the current position f1-4f to the next position f1-3f, transition from the current position f1-3f to the next position f1-2f, acquire the key key1-0 from the place f1-2f, transition from the current position f1-2f to the next position f1-1f, transition from the current position f1-1f to the next position f1-0f, put down key key1-0 at current position place f1-0f, transition from the current position f1-0f to the next position f2-0f, transition from the current position f2-0f to the next position f3-0f, acquire the key key1-1 from the place f3-0f, transition from the current position f3-0f to the next position f3-1f, transition from the current position f3-1f to the next position f3-2f, transition from the current position f3-2f to the next position f3-3f, transition from the current position f3-3f to the next position f4-3f, transition from the current position f4-3f to the next position f4-4f, put down key key1-1 at current position place f4-4f, acquire the key key1-1 from the place f4-4f, put down key key1-1 at current position place f4-4f\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: acquire the key key1-1 from the place f4-4f and put down key key1-1 at current position place f4-4f?", "answer": "yes"}
{"id": 576121559234460464, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f0-4f and its arm is empty. All the positions are open except the following: f2-1f has shape1 shaped lock, f3-4f has shape1 shaped lock. Key key1-0 is at position f1-2f. Key key1-1 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key1-1 is at f4-4f location and Key key1-0 is at f1-0f location.", "question": "Given the plan: \"move from place f0-4f to place f0-3f, move from place f0-3f to place f0-2f, move from place f0-2f to place f1-2f, retrieve the key key1-0 from its current position f1-2f, move from place f1-2f to place f1-1f, move from place f1-1f to place f1-0f, place the key key1-0 at the current position place f1-0f, move from place f1-0f to place f2-0f, move from place f2-0f to place f3-0f, retrieve the key key1-1 from its current position f3-0f, move from place f3-0f to place f3-1f, move from place f3-1f to place f3-2f, move from place f3-2f to place f3-3f, move from place f3-3f to place f4-3f, move from place f4-3f to place f4-4f, use the key key1-1 of shape shape1 to unlock the place f3-4f from its current position f4-4f, place the key key1-1 at the current position place f4-4f, move from place f4-4f to place f4-3f\"; can the following action be removed from this plan and still have a valid plan: move from place f4-3f to place f4-4f?", "answer": "no"}
{"id": 8146825907596388729, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-3f and its arm is empty. All the positions are open except the following: f4-4f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-0 is at position f0-1f. Key key0-1 is at position f1-2f. Key key0-2 is at position f4-3f. The goal is to reach a state where the following facts hold: Key key0-0 is at f2-2f location, Key key0-2 is at f1-4f location, and Key key0-1 is at f3-2f location.", "question": "Given the plan: \"move from place f2-3f to place f3-3f, move from place f3-3f to place f4-3f, pick up key key0-2 from place f4-3f, move from place f4-3f to place f3-3f, move from place f3-3f to place f2-3f, move from place f2-3f to place f2-4f, move from place f2-4f to place f1-4f, place the key key0-2 at the current position place f1-4f, move from place f1-4f to place f1-3f, move from place f1-3f to place f1-2f, move from place f1-2f to place f0-2f, move from place f0-2f to place f0-1f, pick up key key0-0 from place f0-1f, move from place f0-1f to place f0-2f, move from place f0-2f to place f1-2f, move from place f1-2f to place f2-2f, place the key key0-0 at the current position place f2-2f, move from place f2-2f to place f1-2f, pick up key key0-1 from place f1-2f, move from place f1-2f to place f2-2f, move from place f2-2f to place f3-2f, place the key key0-1 at the current position place f3-2f\"; can the following action be removed from this plan and still have a valid plan: move from place f4-3f to place f3-3f?", "answer": "no"}
{"id": 2374419046295360203, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-3f and its arm is empty. All the positions are open except the following: f2-0f has shape0 shaped lock, f2-2f has shape0 shaped lock, f4-2f has shape0 shaped lock. Key key0-1 is at position f2-2f. Key key0-0 is at position f1-3f. Key key0-2 is at position f0-4f. The goal is to reach a state where the following facts hold: Key key0-0 is at f3-3f location, Key key0-2 is at f3-3f location, and Key key0-1 is at f1-2f location.", "question": "Given the plan: \"move from place f2-3f to place f1-3f, move from place f1-3f to place f0-3f, move from place f0-3f to place f0-4f, pick up key key0-2 from place f0-4f, move from place f0-4f to place f0-3f, move from place f0-3f to place f1-3f, move from place f1-3f to place f2-3f, put the key key0-2 at the current position place f2-3f, pick up key key0-2 from place f2-3f, use the key key0-2 of shape shape0 to unlock the place f2-2f from its current position f2-3f, move from place f2-3f to place f3-3f, put the key key0-2 at the current position place f3-3f, move from place f3-3f to place f3-2f, move from place f3-2f to place f2-2f, pick up key key0-1 from place f2-2f, move from place f2-2f to place f1-2f, put the key key0-1 at the current position place f1-2f, move from place f1-2f to place f1-3f, pick up key key0-0 from place f1-3f, move from place f1-3f to place f2-3f, move from place f2-3f to place f3-3f, pick up the key key0-2 from the current position f3-3f and loose the key key0-0 which is being held, put the key key0-2 at the current position place f3-3f\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: put the key key0-2 at the current position place f2-3f and pick up key key0-2 from place f2-3f?", "answer": "yes"}
{"id": -6116387683511340320, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-3f and its arm is empty. All the positions are open except the following: f4-4f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-0 is at position f0-1f. Key key0-1 is at position f1-2f. Key key0-2 is at position f4-3f. The goal is to reach a state where the following facts hold: Key key0-0 is at f2-2f location, Key key0-2 is at f1-4f location, and Key key0-1 is at f3-2f location.", "question": "Given the plan: \"move from place f2-3f to place f2-2f, move from place f2-2f to place f1-2f, move from place f1-2f to place f1-1f, move from place f1-1f to place f0-1f, retrieve the key key0-0 from its current position f0-1f, move from place f0-1f to place f0-2f, move from place f0-2f to place f1-2f, move from place f1-2f to place f2-2f, put down the key key0-0 at the current position f2-2f, move from place f2-2f to place f1-2f, retrieve the key key0-1 from its current position f1-2f, move from place f1-2f to place f2-2f, move from place f2-2f to place f3-2f, put down the key key0-1 at the current position f3-2f, move from place f3-2f to place f3-3f, move from place f3-3f to place f4-3f, retrieve the key key0-2 from its current position f4-3f, unlock place f4-4f with key key0-2 of shape shape0 from current position place f4-3f, move from place f4-3f to place f3-3f, move from place f3-3f to place f2-3f, move from place f2-3f to place f2-4f, move from place f2-4f to place f1-4f, put down the key key0-2 at the current position f1-4f\"; can the following action be removed from this plan and still have a valid plan: unlock place f4-4f with key key0-2 of shape shape0 from current position place f4-3f?", "answer": "yes"}
{"id": -4843711614533901271, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f3-1f and its arm is empty. All the positions are open except the following: f0-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-1 is at position f4-0f. Key key0-0 is at position f0-2f. The goal is to reach a state where the following facts hold: Key key0-1 is at f3-0f location and Key key0-0 is at f0-4f location.", "question": "Given the plan: \"move from f3-1f to f3-0f, move from f3-0f to f4-0f, pick up key key0-1 from place f4-0f, move from f4-0f to f3-0f, put down key key0-1 at current position place f3-0f, move from f3-0f to f2-0f, move from f2-0f to f2-1f, move from f2-1f to f2-0f, move from f2-0f to f2-1f, move from f2-1f to f1-1f, move from f1-1f to f0-1f, move from f0-1f to f0-2f, pick up key key0-0 from place f0-2f, unlock place f0-3f with key key0-0 of shape shape0 from current position place f0-2f, move from f0-2f to f0-3f, move from f0-3f to f0-4f, put down key key0-0 at current position place f0-4f\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: move from f2-0f to f2-1f and move from f2-1f to f2-0f?", "answer": "yes"}
{"id": -1688917226210797311, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f0-3f and its arm is empty. All the positions are open except the following: f2-1f has shape0 shaped lock, f0-1f has shape0 shaped lock, f2-2f has shape0 shaped lock. Key key0-1 is at position f3-3f. Key key0-2 is at position f1-2f. Key key0-0 is at position f4-1f. The goal is to reach a state where the following facts hold: Key key0-2 is at f1-1f location, Key key0-1 is at f2-4f location, and Key key0-0 is at f1-0f location.", "question": "Given the plan: \"move from place f0-3f to place f1-3f, move from place f1-3f to place f2-3f, move from place f2-3f to place f3-3f, acquire the key key0-1 from the place f3-3f, move from place f3-3f to place f2-3f, move from place f2-3f to place f2-4f, put down key key0-1 at current position place f2-4f, move from place f2-4f to place f2-3f, move from place f2-3f to place f3-3f, move from place f3-3f to place f3-2f, move from place f3-2f to place f3-1f, move from place f3-1f to place f4-1f, acquire the key key0-0 from the place f4-1f, move from place f4-1f to place f3-1f, unlock place f2-1f with key key0-0 of shape shape0 from current position place f3-1f, move from place f3-1f to place f2-1f, move from place f2-1f to place f1-1f, move from place f1-1f to place f1-0f, put down key key0-0 at current position place f1-0f, move from place f1-0f to place f1-1f, move from place f1-1f to place f1-2f, acquire the key key0-2 from the place f1-2f, move from place f1-2f to place f1-1f, put down key key0-2 at current position place f1-1f\"; can the following action be removed from this plan and still have a valid plan: move from place f3-3f to place f2-3f?", "answer": "no"}
{"id": -3271071171870454789, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-1f and its arm is empty. All the positions are open except the following: f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-1 is at position f0-1f. Key key0-0 is at position f4-4f. The goal is to reach a state where the following facts hold: Key key0-1 is at f4-2f location and Key key0-0 is at f2-4f location.", "question": "Given the plan: \"travel from the current position f2-1f to the next position f1-1f, travel from the current position f1-1f to the next position f0-1f, retrieve the key key0-1 from its current position f0-1f, travel from the current position f0-1f to the next position f1-1f, travel from the current position f1-1f to the next position f0-1f, travel from the current position f0-1f to the next position f1-1f, travel from the current position f1-1f to the next position f2-1f, travel from the current position f2-1f to the next position f2-2f, travel from the current position f2-2f to the next position f3-2f, travel from the current position f3-2f to the next position f4-2f, put down key key0-1 at current position place f4-2f, travel from the current position f4-2f to the next position f4-3f, travel from the current position f4-3f to the next position f4-4f, retrieve the key key0-0 from its current position f4-4f, travel from the current position f4-4f to the next position f3-4f, travel from the current position f3-4f to the next position f2-4f, put down key key0-0 at current position place f2-4f\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: travel from the current position f1-1f to the next position f0-1f and travel from the current position f0-1f to the next position f1-1f?", "answer": "yes"}
{"id": 3809837843735447162, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f3-1f and its arm is empty. All the positions are open except the following: f0-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-1 is at position f4-0f. Key key0-0 is at position f0-2f. The goal is to reach a state where the following facts hold: Key key0-1 is at f3-0f location and Key key0-0 is at f0-4f location.", "question": "Given the plan: \"move from place f3-1f to place f4-1f, move from place f4-1f to place f4-0f, acquire the key key0-1 from the place f4-0f, move from place f4-0f to place f3-0f, place the key key0-1 at the current position place f3-0f, move from place f3-0f to place f3-1f, move from place f3-1f to place f2-1f, move from place f2-1f to place f2-2f, move from place f2-2f to place f1-2f, move from place f1-2f to place f0-2f, acquire the key key0-0 from the place f0-2f, unlock the place f0-3f with key key0-0 of shape shape0 from the current position place f0-2f, move from place f0-2f to place f0-3f, move from place f0-3f to place f0-4f, place the key key0-0 at the current position place f0-4f, move from place f0-4f to place f0-3f\"; can the following action be removed from this plan and still have a valid plan: move from place f0-4f to place f0-3f?", "answer": "yes"}
{"id": -3904794027343157419, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f0-4f and its arm is empty. All the positions are open except the following: f2-1f has shape0 shaped lock, f3-4f has shape0 shaped lock. Key key0-1 is at position f3-0f. Key key0-0 is at position f1-2f. The goal is to reach a state where the following facts hold: Key key0-0 is at f1-0f location and Key key0-1 is at f4-4f location.", "question": "Given the plan: \"move from f0-4f to f0-3f, move from f0-3f to f1-3f, move from f1-3f to f1-2f, pick up key key0-0 from place f1-2f, move from f1-2f to f1-1f, move from f1-1f to f1-0f, place the key key0-0 at the current position place f1-0f, move from f1-0f to f2-0f, move from f2-0f to f3-0f, pick up key key0-1 from place f3-0f, move from f3-0f to f3-1f, move from f3-1f to f3-0f, move from f3-0f to f3-1f, move from f3-1f to f4-1f, move from f4-1f to f4-2f, move from f4-2f to f4-3f, move from f4-3f to f4-4f, place the key key0-1 at the current position place f4-4f\"; can the following action be removed from this plan and still have a valid plan: move from f3-0f to f3-1f?", "answer": "no"}
