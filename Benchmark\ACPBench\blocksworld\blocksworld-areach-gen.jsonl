{"id": 4029983117286477421, "group": "reachable_action_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_5. The following block(s) are on the table: block_1, block_2, and block_4. The following block(s) is stacked on top of another block: block_3 is on block_1. The available actions are: (pick-up ?ob) - collect the object ?ob from the table, (put-down ?ob) - put down the object ?ob, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(stack block_5 block_5)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_3) (clear block_4) (holding block_5) (on block_3 block_1) (ontable block_1) (ontable block_2) (ontable block_4))\n    (:goal (and (on block_3 block_5) (on block_4 block_1) (on block_5 block_4)))\n)"}
{"id": 7085238290522280400, "group": "reachable_action_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is holding block_10. The following block(s) are on the table: block_12, block_14, block_7, block_19, block_18, block_1, and block_6. The following block(s) are stacked on top of another block: block_4 is on block_11, block_3 is on block_2, block_13 is on block_4, block_9 is on block_15, block_17 is on block_12, block_11 is on block_1, block_8 is on block_7, block_20 is on block_8, block_15 is on block_16, block_5 is on block_14, block_16 is on block_20, and block_2 is on block_18. The available actions are: (pick-up ?ob) - pick up object ?ob from the table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - stack the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack the object ?ob from the object ?underob.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(stack block_17 block_17)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-20)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_11 block_12 block_13 block_14 block_15 block_16 block_17 block_18 block_19 block_2 block_20 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_13) (clear block_17) (clear block_19) (clear block_3) (clear block_5) (clear block_6) (clear block_9) (holding block_10) (on block_11 block_1) (on block_13 block_4) (on block_15 block_16) (on block_16 block_20) (on block_17 block_12) (on block_2 block_18) (on block_20 block_8) (on block_3 block_2) (on block_4 block_11) (on block_5 block_14) (on block_8 block_7) (on block_9 block_15) (ontable block_1) (ontable block_12) (ontable block_14) (ontable block_18) (ontable block_19) (ontable block_6) (ontable block_7))\n    (:goal (and (on block_1 block_17) (on block_2 block_14) (on block_3 block_13) (on block_4 block_6) (on block_5 block_1) (on block_7 block_10) (on block_8 block_4) (on block_9 block_2) (on block_11 block_8) (on block_12 block_7) (on block_14 block_20) (on block_15 block_9) (on block_16 block_11) (on block_17 block_12) (on block_18 block_5) (on block_19 block_16)))\n)"}
{"id": 4067642471119498375, "group": "reachable_action_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_5, block_2, and block_4. The following block(s) are stacked on top of another block: block_1 is on block_5 and block_3 is on block_2. The available actions are: (pick-up ?ob) - collect the object ?ob from the table, (put-down ?ob) - put down object ?ob, (stack ?ob ?underob) - stack the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(stack block_5 block_5)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_1) (clear block_3) (clear block_4) (handempty) (on block_1 block_5) (on block_3 block_2) (ontable block_2) (ontable block_4) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_5) (on block_4 block_2) (on block_5 block_1)))\n)"}
{"id": -2508531197643978563, "group": "reachable_action_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1, block_5, block_2, and block_4. The following block(s) is stacked on top of another block: block_3 is on block_4. The available actions are: (pick-up ?ob) - pick up object ?ob from the table, (put-down ?ob) - put down object ?ob, (stack ?ob ?underob) - stack the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - remove the object ?ob from on top of the object ?underob.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(stack block_5 block_5)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_1) (clear block_2) (clear block_3) (clear block_5) (handempty) (on block_3 block_4) (ontable block_1) (ontable block_2) (ontable block_4) (ontable block_5))\n    (:goal (and (on block_3 block_5) (on block_4 block_1) (on block_5 block_4)))\n)"}
{"id": -8309067916359362897, "group": "reachable_action_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_5. The following block(s) are on the table: block_1, block_2, and block_3. The following block(s) is stacked on top of another block: block_4 is on block_1. The available actions are: (pick-up ?ob) - collect the object ?ob from the table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - unstack the object ?ob from the object ?underob.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(stack block_5 block_5)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_3) (clear block_4) (holding block_5) (on block_4 block_1) (ontable block_1) (ontable block_2) (ontable block_3))\n    (:goal (and (on block_3 block_5) (on block_4 block_1) (on block_5 block_4)))\n)"}
{"id": -8103954623355069100, "group": "reachable_action_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_5. The following block(s) are on the table: block_1 and block_2. The following block(s) are stacked on top of another block: block_4 is on block_1 and block_3 is on block_2. The available actions are: (pick-up ?ob) - pick up the object ?ob from the table, (put-down ?ob) - put down object ?ob, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - remove the object ?ob from on top of the object ?underob.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(stack block_5 block_5)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_3) (clear block_4) (holding block_5) (on block_3 block_2) (on block_4 block_1) (ontable block_1) (ontable block_2))\n    (:goal (and (on block_3 block_5) (on block_4 block_1) (on block_5 block_4)))\n)"}
{"id": 9151380831188132847, "group": "reachable_action_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1, block_5, and block_4. The following block(s) are stacked on top of another block: block_2 is on block_4 and block_3 is on block_5. The available actions are: (pick-up ?ob) - collect the object ?ob from the table, (put-down ?ob) - put down object ?ob, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - remove the object ?ob from on top of the object ?underob.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(stack block_5 block_5)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_1) (clear block_2) (clear block_3) (handempty) (on block_2 block_4) (on block_3 block_5) (ontable block_1) (ontable block_4) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_5) (on block_4 block_2) (on block_5 block_1)))\n)"}
{"id": 4208748787631077764, "group": "reachable_action_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_3. The following block(s) are on the table: block_10, block_1, block_2, and block_8. The following block(s) are stacked on top of another block: block_7 is on block_10, block_6 is on block_1, block_4 is on block_9, block_9 is on block_5, and block_5 is on block_2. The available actions are: (pick-up ?ob) - pick up object ?ob from the table, (put-down ?ob) - put down the object ?ob, (stack ?ob ?underob) - stack the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(unstack block_10 block_10)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-10)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_2 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_4) (clear block_6) (clear block_7) (clear block_8) (holding block_3) (on block_4 block_9) (on block_5 block_2) (on block_6 block_1) (on block_7 block_10) (on block_9 block_5) (ontable block_1) (ontable block_10) (ontable block_2) (ontable block_8))\n    (:goal (and (on block_1 block_8) (on block_2 block_5) (on block_3 block_7) (on block_4 block_2) (on block_5 block_9) (on block_6 block_3) (on block_7 block_10) (on block_9 block_1)))\n)"}
{"id": 6029919216632193446, "group": "reachable_action_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_10, block_4, block_2, and block_8. The following block(s) are stacked on top of another block: block_9 is on block_1, block_6 is on block_3, block_7 is on block_10, block_5 is on block_9, block_1 is on block_8, and block_3 is on block_7. The available actions are: (pick-up ?ob) - collect the object ?ob from the table, (put-down ?ob) - put down the object ?ob, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - remove the object ?ob from on top of the object ?underob.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(stack block_9 block_9)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-10)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_2 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_2) (clear block_4) (clear block_5) (clear block_6) (handempty) (on block_1 block_8) (on block_3 block_7) (on block_5 block_9) (on block_6 block_3) (on block_7 block_10) (on block_9 block_1) (ontable block_10) (ontable block_2) (ontable block_4) (ontable block_8))\n    (:goal (and (on block_1 block_8) (on block_2 block_5) (on block_3 block_7) (on block_4 block_2) (on block_5 block_9) (on block_6 block_3) (on block_7 block_10) (on block_9 block_1)))\n)"}
{"id": -3511155290499638887, "group": "reachable_action_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is holding block_8. The following block(s) are on the table: block_14, block_7, block_20, block_1, block_16, block_10, and block_6. The following block(s) are stacked on top of another block: block_4 is on block_11, block_12 is on block_17, block_19 is on block_16, block_3 is on block_13, block_13 is on block_4, block_15 is on block_9, block_9 is on block_2, block_11 is on block_1, block_5 is on block_14, block_17 is on block_3, block_18 is on block_5, and block_2 is on block_12. The available actions are: (pick-up ?ob) - remove ?ob from table, (put-down ?ob) - put down the object ?ob, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(stack block_5 block_5)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-20)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_10 block_11 block_12 block_13 block_14 block_15 block_16 block_17 block_18 block_19 block_2 block_20 block_3 block_4 block_5 block_6 block_7 block_8 block_9)\n    (:init (clear block_10) (clear block_15) (clear block_18) (clear block_19) (clear block_20) (clear block_6) (clear block_7) (holding block_8) (on block_11 block_1) (on block_12 block_17) (on block_13 block_4) (on block_15 block_9) (on block_17 block_3) (on block_18 block_5) (on block_19 block_16) (on block_2 block_12) (on block_3 block_13) (on block_4 block_11) (on block_5 block_14) (on block_9 block_2) (ontable block_1) (ontable block_10) (ontable block_14) (ontable block_16) (ontable block_20) (ontable block_6) (ontable block_7))\n    (:goal (and (on block_1 block_17) (on block_2 block_14) (on block_3 block_13) (on block_4 block_6) (on block_5 block_1) (on block_7 block_10) (on block_8 block_4) (on block_9 block_2) (on block_11 block_8) (on block_12 block_7) (on block_14 block_20) (on block_15 block_9) (on block_16 block_11) (on block_17 block_12) (on block_18 block_5) (on block_19 block_16)))\n)"}
{"id": 6944749776549910780, "group": "reachable_action_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is holding block_5. The following block(s) are on the table: block_1 and block_3. The following block(s) are stacked on top of another block: block_4 is on block_3 and block_2 is on block_1. The available actions are: (pick-up ?ob) - pick up the object ?ob from the table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - stack object ?ob on top of object ?underob, and (unstack ?ob ?underob) - unstack the object ?ob from the object ?underob.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(stack block_5 block_5)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_4) (holding block_5) (on block_2 block_1) (on block_4 block_3) (ontable block_1) (ontable block_3))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": 2013108380780204354, "group": "reachable_action_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is holding block_1. The following block(s) is on the table: block_5. The following block(s) are stacked on top of another block: block_4 is on block_5, block_2 is on block_4, and block_3 is on block_2. The available actions are: (pick-up ?ob) - pick up object ?ob from the table, (put-down ?ob) - put down the object ?ob, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(stack block_5 block_5)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_3) (holding block_1) (on block_2 block_4) (on block_3 block_2) (on block_4 block_5) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": 4983142035616378970, "group": "reachable_action_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_5 and block_2. The following block(s) are stacked on top of another block: block_1 is on block_4, block_4 is on block_5, and block_3 is on block_2. The available actions are: (pick-up ?ob) - pick up object ?ob from the table, (put-down ?ob) - place the object ?ob on the table, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack object ?ob from object ?underob.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(stack block_5 block_5)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_1) (clear block_3) (handempty) (on block_1 block_4) (on block_3 block_2) (on block_4 block_5) (ontable block_2) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": 5516267193248218029, "group": "reachable_action_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_5 and block_3. The following block(s) are stacked on top of another block: block_2 is on block_4, block_1 is on block_3, and block_4 is on block_1. The available actions are: (pick-up ?ob) - pick up the object ?ob from the table, (put-down ?ob) - put down object ?ob, (stack ?ob ?underob) - place the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - remove the object ?ob from on top of the object ?underob.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(stack block_5 block_5)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_5) (handempty) (on block_1 block_3) (on block_2 block_4) (on block_4 block_1) (ontable block_3) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": -472053232446630144, "group": "reachable_action_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is holding block_5. The following block(s) are on the table: block_2 and block_3. The following block(s) are stacked on top of another block: block_1 is on block_3 and block_4 is on block_2. The available actions are: (pick-up ?ob) - collect the object ?ob from the table, (put-down ?ob) - put down object ?ob, (stack ?ob ?underob) - stack the object ?ob on top of the object ?underob, and (unstack ?ob ?underob) - unstack the object ?ob from the object ?underob.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(stack block_5 block_5)"], "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_1) (clear block_4) (holding block_5) (on block_1 block_3) (on block_4 block_2) (ontable block_2) (ontable block_3))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
