{"id": -836934929485309563, "group": "validation_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l2-1, l2-2, and l2-0 are in c2; l4-2, l4-1, and l4-0 are in c4; l1-0, l1-2, and l1-1 are in c1; l3-1, l3-2, and l3-0 are in c3. Currently, t1 is at l1-2, p1 is at l3-1, p2 is at l2-1, p0 and t2 are at l2-2, p3 is at l4-1, t0 is at l0-2, a0 is at l2-0, t4 is at l4-0, t3 is at l3-2. The goal is to reach a state where the following facts hold: p0 is at l3-0, p1 is at l3-2, p3 is at l3-2, and p2 is at l2-2. The available actions are: (load-truck ?obj ?truck ?loc) - place the object ?obj into the truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - place the object ?obj onto the airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - unload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload object ?obj from airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck from location ?loc-from in city ?city to location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - operate the airplane ?airplane from airport ?loc-from to airport ?loc-to.", "question": "What is the first inapplicable action in the next sequence of actions: \"(DRIVE-TRUCK t3 l3-2 l3-1 c3) (LOAD-TRUCK p1 t3 l3-1) (DRIVE-TRUCK t3 l3-1 l3-0 c3) (LOAD-TRUCK p0 t2 l2-2) (DRIVE-TRUCK t2 l2-2 l2-1 c2) (LOAD-TRUCK p2 t2 l2-1) (DRIVE-TRUCK t2 l2-1 l2-2 c2) (UNLOAD-TRUCK p2 t2 l2-2) (DRIVE-TRUCK t2 l2-2 l2-0 c2) (UNLOAD-TRUCK p0 t2 l2-0) (LOAD-AIRPLANE p0 a0 l2-0) (FLY-AIRPLANE a0 l2-0 l4-0) (DRIVE-TRUCK t4 l4-0 l4-1 c4) (LOAD-TRUCK p3 t4 l4-1) (DRIVE-TRUCK t4 l4-1 l4-0 c4) (UNLOAD-TRUCK p3 t4 l4-0) (LOAD-AIRPLANE p3 a0 l4-0) (FLY-AIRPLANE a0 l4-0 l3-0) (UNLOAD-AIRPLANE p0 a0 l3-0) (UNLOAD-AIRPLANE p3 a0 l3-0) (LOAD-TRUCK p3 t3 l3-0) (DRIVE-TRUCK t3 l3-0 l3-2 c3) (UNLOAD-TRUCK p1 t3 l3-2) (UNLOAD-TRUCK p3 t3 l3-2) (LOAD-AIRPLANE p0 a0 l1-0)\"?", "answer": 24, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c5-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 l3-0 l4-0 - airport c0 c1 c2 c3 c4 - city l0-1 l0-2 l1-1 l1-2 l2-1 l2-2 l3-1 l3-2 l4-1 l4-2 - location p0 p1 p2 p3 - package t0 t1 t2 t3 t4 - truck)\n    (:init (at a0 l2-0) (at p0 l2-2) (at p1 l3-1) (at p2 l2-1) (at p3 l4-1) (at t0 l0-2) (at t1 l1-2) (at t2 l2-2) (at t3 l3-2) (at t4 l4-0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2) (in-city l3-0 c3) (in-city l3-1 c3) (in-city l3-2 c3) (in-city l4-0 c4) (in-city l4-1 c4) (in-city l4-2 c4))\n    (:goal (and (at p0 l3-0) (at p1 l3-2) (at p2 l2-2) (at p3 l3-2)))\n)"}
{"id": 7919384016067706158, "group": "validation_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-0 and l0-1 are in c0; l1-0 and l1-1 are in c1. Currently, t0, p3, and a0 are at l0-0, p0 and p2 are at l0-1, t1 and p1 are at l1-0. The goal is to reach a state where the following facts hold: p0 is at l1-1, p3 is at l1-1, p2 is at l1-0, and p1 is at l1-1. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - place the object ?obj onto the airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - offload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - remove the object ?obj from the airplane ?airplane and place it on the location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck which is in location ?loc-from in city ?city to another location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly airplane ?airplane from airport ?loc-from to airport ?loc-to.", "question": "What is the first inapplicable action in the next sequence of actions: \"(LOAD-TRUCK p1 t1 l1-0) (DRIVE-TRUCK t0 l0-0 l0-1 c0) (LOAD-TRUCK p0 t0 l0-1) (LOAD-TRUCK p2 t0 l0-1) (LOAD-AIRPLANE p3 a0 l0-0) (DRIVE-TRUCK t0 l0-1 l0-0 c0) (UNLOAD-TRUCK p0 t0 l0-0) (UNLOAD-TRUCK p2 t0 l0-0) (LOAD-AIRPLANE p0 a0 l0-0) (LOAD-AIRPLANE p2 a0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (UNLOAD-AIRPLANE p0 a0 l1-0) (LOAD-TRUCK p0 t1 l1-0) (UNLOAD-AIRPLANE p2 a0 l1-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (UNLOAD-TRUCK p0 t1 l1-1) (UNLOAD-TRUCK p1 t1 l1-1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (UNLOAD-AIRPLANE p3 a0 l1-0) (LOAD-TRUCK p3 t1 l1-0) (UNLOAD-TRUCK p3 t1 l1-0) (LOAD-TRUCK p3 t1 l1-0) (UNLOAD-TRUCK p3 t1 l1-0) (LOAD-TRUCK p3 t1 l1-0) (UNLOAD-TRUCK p3 t1 l1-0) (LOAD-TRUCK p3 t1 l1-0) (UNLOAD-TRUCK p3 t1 l1-0) (LOAD-TRUCK p2 t0 l0-1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (UNLOAD-TRUCK p3 t1 l1-1)\"?", "answer": 27, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s2-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l1-1 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p0 l0-1) (at p1 l1-0) (at p2 l0-1) (at p3 l0-0) (at t0 l0-0) (at t1 l1-0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l1-0 c1) (in-city l1-1 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-1)))\n)"}
{"id": 2285060383276408918, "group": "validation_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l1-0, l1-2, and l1-1 are in c1. Currently, a0 and p2 are at l1-0, p1 and p3 are at l0-2, p0 and t0 are at l0-0, t1 is at l1-1. The goal is to reach a state where the following facts hold: p0 is at l0-1, p2 is at l0-1, p3 is at l0-1, and p1 is at l0-1. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - load object ?obj into airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - unload object ?obj from truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - offload the object ?obj from the airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive the truck ?truck in city ?city from location ?loc-from to location ?loc-to, and (fly-airplane ?airplane ?loc-from ?loc-to) - operate the airplane ?airplane from airport ?loc-from to airport ?loc-to.", "question": "What is the first inapplicable action in the next sequence of actions: \"(LOAD-TRUCK p0 t0 l0-0) (DRIVE-TRUCK t0 l0-0 l0-2 c0) (LOAD-TRUCK p2 t0 l0-2) (LOAD-TRUCK p1 t0 l0-2) (DRIVE-TRUCK t0 l0-2 l0-0 c0) (UNLOAD-TRUCK p3 t0 l0-0) (LOAD-AIRPLANE p2 a0 l1-0) (FLY-AIRPLANE a0 l1-0 l0-0) (UNLOAD-AIRPLANE p2 a0 l0-0) (LOAD-TRUCK p2 t0 l0-0) (LOAD-TRUCK p3 t0 l0-0) (DRIVE-TRUCK t0 l0-0 l0-1 c0) (UNLOAD-TRUCK p3 t0 l0-1) (UNLOAD-TRUCK p2 t0 l0-1) (UNLOAD-TRUCK p1 t0 l0-1) (UNLOAD-TRUCK p0 t0 l0-1)\"?", "answer": 2, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l1-0) (at p0 l0-0) (at p1 l0-2) (at p2 l1-0) (at p3 l0-2) (at t0 l0-0) (at t1 l1-1) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l0-1) (at p1 l0-1) (at p2 l0-1) (at p3 l0-1)))\n)"}
{"id": 1295576228215394045, "group": "validation_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-0 and l0-1 are in c0; l1-0 and l1-1 are in c1. Currently, t0, p3, and a0 are at l0-0, p0 and p2 are at l0-1, t1 and p1 are at l1-0. The goal is to reach a state where the following facts hold: p0 is at l1-1, p3 is at l1-1, p2 is at l1-0, and p1 is at l1-1. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - load object ?obj into airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - unload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload object ?obj from airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck from its current location ?loc-from in city ?city to the new location ?loc-to within the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly airplane ?airplane from airport ?loc-from to airport ?loc-to.", "question": "What is the first inapplicable action in the next sequence of actions: \"(LOAD-TRUCK p1 t1 l1-0) (DRIVE-TRUCK t0 l0-0 l0-1 c0) (LOAD-TRUCK p0 t0 l0-1) (LOAD-TRUCK p2 t0 l0-1) (LOAD-AIRPLANE p3 a0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (UNLOAD-AIRPLANE p3 a0 l1-0) (LOAD-TRUCK p3 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (UNLOAD-TRUCK p1 t1 l1-1) (UNLOAD-TRUCK p3 t1 l1-1) (DRIVE-TRUCK t0 l0-1 l0-0 c0) (LOAD-AIRPLANE p1 a0 l1-0) (UNLOAD-TRUCK p2 t0 l0-0) (FLY-AIRPLANE a0 l1-0 l0-0) (LOAD-AIRPLANE p0 a0 l0-0) (LOAD-AIRPLANE p2 a0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (UNLOAD-AIRPLANE p0 a0 l1-0) (UNLOAD-AIRPLANE p2 a0 l1-0) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (LOAD-TRUCK p0 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (UNLOAD-TRUCK p0 t1 l1-1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1)\"?", "answer": 12, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s2-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l1-1 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p0 l0-1) (at p1 l1-0) (at p2 l0-1) (at p3 l0-0) (at t0 l0-0) (at t1 l1-0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l1-0 c1) (in-city l1-1 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-1)))\n)"}
{"id": -2708230492635484296, "group": "validation_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-0 and l0-1 are in c0; l1-0 and l1-1 are in c1. Currently, t0, p3, and a0 are at l0-0, p0 and p2 are at l0-1, t1 and p1 are at l1-0. The goal is to reach a state where the following facts hold: p0 is at l1-1, p3 is at l1-1, p2 is at l1-0, and p1 is at l1-1. The available actions are: (load-truck ?obj ?truck ?loc) - load object ?obj into truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - place the object ?obj onto the airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - remove the object ?obj from the truck ?truck and place it on the location ?loc, (unload-airplane ?obj ?airplane ?loc) - offload the object ?obj from the airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck from location ?loc-from in city ?city to location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from location ?loc-from to location ?loc-to.", "question": "What is the first inapplicable action in the next sequence of actions: \"(LOAD-TRUCK p1 t1 l1-0) (UNLOAD-TRUCK p0 t1 l1-1) (LOAD-TRUCK p0 t0 l0-1) (LOAD-TRUCK p2 t0 l0-1) (LOAD-AIRPLANE p3 a0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (UNLOAD-AIRPLANE p3 a0 l1-0) (LOAD-TRUCK p3 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (UNLOAD-TRUCK p1 t1 l1-1) (UNLOAD-TRUCK p3 t1 l1-1) (DRIVE-TRUCK t0 l0-1 l0-0 c0) (UNLOAD-TRUCK p0 t0 l0-0) (UNLOAD-TRUCK p2 t0 l0-0) (FLY-AIRPLANE a0 l1-0 l0-0) (LOAD-AIRPLANE p0 a0 l0-0) (LOAD-AIRPLANE p2 a0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (UNLOAD-AIRPLANE p0 a0 l1-0) (UNLOAD-AIRPLANE p2 a0 l1-0) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (LOAD-TRUCK p0 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (UNLOAD-TRUCK p0 t1 l1-1) (FLY-AIRPLANE a0 l1-0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (FLY-AIRPLANE a0 l1-0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (FLY-AIRPLANE a0 l1-0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (FLY-AIRPLANE a0 l1-0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (FLY-AIRPLANE a0 l1-0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (FLY-AIRPLANE a0 l1-0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (FLY-AIRPLANE a0 l1-0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (FLY-AIRPLANE a0 l1-0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (FLY-AIRPLANE a0 l1-0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (FLY-AIRPLANE a0 l1-0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0)\"?", "answer": 1, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s2-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l1-1 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p0 l0-1) (at p1 l1-0) (at p2 l0-1) (at p3 l0-0) (at t0 l0-0) (at t1 l1-0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l1-0 c1) (in-city l1-1 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-1)))\n)"}
{"id": 2555950298993445326, "group": "validation_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l3-6, l3-2, l3-0, l3-3, l3-8, l3-1, l3-4, l3-7, l3-9, and l3-5 are in c3; l4-9, l4-5, l4-7, l4-1, l4-3, l4-6, l4-0, l4-4, l4-2, and l4-8 are in c4; l1-0, l1-3, l1-5, l1-8, l1-6, l1-4, l1-1, l1-9, l1-7, and l1-2 are in c1; l0-6, l0-4, l0-1, l0-0, l0-2, l0-9, l0-8, l0-7, l0-5, and l0-3 are in c0; l2-8, l2-0, l2-5, l2-1, l2-3, l2-2, l2-9, l2-4, l2-6, and l2-7 are in c2. Currently, t3 and p2 are at l3-1, p0 is at l2-4, p3 is at l2-9, t4 is at l4-7, t0 is at l0-2, a0 is at l0-0, t2 is at l2-0, t1 is at l1-6, p1 is at l1-8. The goal is to reach a state where the following facts hold: p2 is at l4-8, p1 is at l2-7, p3 is at l1-1, and p0 is at l1-5. The available actions are: (load-truck ?obj ?truck ?loc) - load object ?obj into truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc into the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - unload object ?obj from truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload object ?obj from airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive the truck ?truck in city ?city from location ?loc-from to location ?loc-to, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from the airport ?loc-from to the airport ?loc-to.", "question": "What is the first inapplicable action in the next sequence of actions: \"(DRIVE-TRUCK t4 l4-7 l4-0 c4) (LOAD-TRUCK p2 t3 l3-1) (DRIVE-TRUCK t3 l3-1 l3-0 c3) (UNLOAD-TRUCK p2 t3 l3-0) (DRIVE-TRUCK t1 l1-6 l1-5 c1) (DRIVE-TRUCK t0 l0-2 l0-4 c0) (LOAD-TRUCK p1 t0 l0-3) (DRIVE-TRUCK t1 l1-5 l1-8 c1) (LOAD-TRUCK p1 t1 l1-8) (DRIVE-TRUCK t1 l1-8 l1-0 c1) (UNLOAD-TRUCK p1 t1 l1-0) (LOAD-AIRPLANE p1 a0 l1-0) (FLY-AIRPLANE a0 l1-0 l2-0) (UNLOAD-AIRPLANE p1 a0 l2-0) (LOAD-TRUCK p1 t2 l2-0) (DRIVE-TRUCK t2 l2-0 l2-9 c2) (LOAD-TRUCK p3 t2 l2-9) (DRIVE-TRUCK t2 l2-9 l2-7 c2) (UNLOAD-TRUCK p1 t2 l2-7) (DRIVE-TRUCK t2 l2-7 l2-4 c2) (LOAD-TRUCK p0 t2 l2-4) (FLY-AIRPLANE a0 l2-0 l3-0) (LOAD-AIRPLANE p2 a0 l3-0) (FLY-AIRPLANE a0 l3-0 l4-0) (UNLOAD-AIRPLANE p2 a0 l4-0) (LOAD-TRUCK p2 t4 l4-0) (DRIVE-TRUCK t4 l4-0 l4-8 c4) (UNLOAD-TRUCK p2 t4 l4-8) (DRIVE-TRUCK t2 l2-4 l2-0 c2) (UNLOAD-TRUCK p3 t2 l2-0) (UNLOAD-TRUCK p0 t2 l2-0) (FLY-AIRPLANE a0 l4-0 l1-0) (FLY-AIRPLANE a0 l1-0 l2-0) (LOAD-AIRPLANE p3 a0 l2-0) (LOAD-AIRPLANE p0 a0 l2-0) (FLY-AIRPLANE a0 l2-0 l1-0) (UNLOAD-AIRPLANE p3 a0 l1-0) (LOAD-TRUCK p3 t1 l1-0) (UNLOAD-AIRPLANE p0 a0 l1-0) (LOAD-TRUCK p0 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (UNLOAD-TRUCK p3 t1 l1-1) (DRIVE-TRUCK t1 l1-1 l1-5 c1) (UNLOAD-TRUCK p0 t1 l1-5)\"?", "answer": 6, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c5-s10-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 l3-0 l4-0 - airport c0 c1 c2 c3 c4 - city l0-1 l0-2 l0-3 l0-4 l0-5 l0-6 l0-7 l0-8 l0-9 l1-1 l1-2 l1-3 l1-4 l1-5 l1-6 l1-7 l1-8 l1-9 l2-1 l2-2 l2-3 l2-4 l2-5 l2-6 l2-7 l2-8 l2-9 l3-1 l3-2 l3-3 l3-4 l3-5 l3-6 l3-7 l3-8 l3-9 l4-1 l4-2 l4-3 l4-4 l4-5 l4-6 l4-7 l4-8 l4-9 - location p0 p1 p2 p3 - package t0 t1 t2 t3 t4 - truck)\n    (:init (at a0 l0-0) (at p0 l2-4) (at p1 l1-8) (at p2 l3-1) (at p3 l2-9) (at t0 l0-2) (at t1 l1-6) (at t2 l2-0) (at t3 l3-1) (at t4 l4-7) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l0-3 c0) (in-city l0-4 c0) (in-city l0-5 c0) (in-city l0-6 c0) (in-city l0-7 c0) (in-city l0-8 c0) (in-city l0-9 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l1-3 c1) (in-city l1-4 c1) (in-city l1-5 c1) (in-city l1-6 c1) (in-city l1-7 c1) (in-city l1-8 c1) (in-city l1-9 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2) (in-city l2-3 c2) (in-city l2-4 c2) (in-city l2-5 c2) (in-city l2-6 c2) (in-city l2-7 c2) (in-city l2-8 c2) (in-city l2-9 c2) (in-city l3-0 c3) (in-city l3-1 c3) (in-city l3-2 c3) (in-city l3-3 c3) (in-city l3-4 c3) (in-city l3-5 c3) (in-city l3-6 c3) (in-city l3-7 c3) (in-city l3-8 c3) (in-city l3-9 c3) (in-city l4-0 c4) (in-city l4-1 c4) (in-city l4-2 c4) (in-city l4-3 c4) (in-city l4-4 c4) (in-city l4-5 c4) (in-city l4-6 c4) (in-city l4-7 c4) (in-city l4-8 c4) (in-city l4-9 c4))\n    (:goal (and (at p0 l1-5) (at p1 l2-7) (at p2 l4-8) (at p3 l1-1)))\n)"}
{"id": 3667984576660996876, "group": "validation_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l1-0, l1-2, and l1-1 are in c1. Currently, a0 and p2 are at l1-0, p1 and p3 are at l0-2, p0 and t0 are at l0-0, t1 is at l1-1. The goal is to reach a state where the following facts hold: p0 is at l0-1, p2 is at l0-1, p3 is at l0-1, and p1 is at l0-1. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - place the object ?obj onto the airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - remove the object ?obj from the truck ?truck and place it on the location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload the object ?obj from the airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive truck ?truck from location ?loc-from in city ?city to location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - operate the airplane ?airplane from airport ?loc-from to airport ?loc-to.", "question": "What is the first inapplicable action in the next sequence of actions: \"(LOAD-AIRPLANE p2 a0 l1-0) (UNLOAD-TRUCK p0 t1 l1-0) (UNLOAD-AIRPLANE p2 a0 l0-0) (LOAD-TRUCK p2 t0 l0-0) (LOAD-TRUCK p0 t0 l0-0) (DRIVE-TRUCK t0 l0-0 l0-2 c0) (LOAD-TRUCK p3 t0 l0-2) (LOAD-TRUCK p1 t0 l0-2) (DRIVE-TRUCK t0 l0-2 l0-1 c0) (UNLOAD-TRUCK p2 t0 l0-1) (UNLOAD-TRUCK p0 t0 l0-1) (UNLOAD-TRUCK p3 t0 l0-1) (UNLOAD-TRUCK p1 t0 l0-1)\"?", "answer": 1, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l1-0) (at p0 l0-0) (at p1 l0-2) (at p2 l1-0) (at p3 l0-2) (at t0 l0-0) (at t1 l1-1) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l0-1) (at p1 l0-1) (at p2 l0-1) (at p3 l0-1)))\n)"}
{"id": 2636158875077918606, "group": "validation_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l2-1, l2-2, and l2-0 are in c2; l4-2, l4-1, and l4-0 are in c4; l1-0, l1-2, and l1-1 are in c1; l3-1, l3-2, and l3-0 are in c3. Currently, t1 is at l1-2, p1 is at l3-1, p2 is at l2-1, p0 and t2 are at l2-2, p3 is at l4-1, t0 is at l0-2, a0 is at l2-0, t4 is at l4-0, t3 is at l3-2. The goal is to reach a state where the following facts hold: p0 is at l3-0, p1 is at l3-2, p3 is at l3-2, and p2 is at l2-2. The available actions are: (load-truck ?obj ?truck ?loc) - place the object ?obj into the truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc onto the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - remove the object ?obj from the truck ?truck and place it on the location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload object ?obj from airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive truck ?truck from location ?loc-from in city ?city to location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from the airport ?loc-from to the airport ?loc-to.", "question": "What is the first inapplicable action in the next sequence of actions: \"(DRIVE-TRUCK t3 l3-2 l3-0 c3) (LOAD-TRUCK p0 t2 l2-2) (DRIVE-TRUCK t2 l2-2 l2-1 c2) (LOAD-TRUCK p2 t2 l2-1) (DRIVE-TRUCK t2 l2-1 l2-0 c2) (UNLOAD-TRUCK p0 t2 l2-0) (LOAD-AIRPLANE p0 a0 l2-0) (DRIVE-TRUCK t2 l2-0 l2-2 c2) (FLY-AIRPLANE a0 l2-0 l3-0) (FLY-AIRPLANE a0 l3-0 l4-0) (DRIVE-TRUCK t4 l4-0 l4-1 c4) (LOAD-TRUCK p3 t4 l4-1) (LOAD-AIRPLANE p3 a0 l0-0) (UNLOAD-TRUCK p3 t4 l4-0) (LOAD-AIRPLANE p3 a0 l4-0) (FLY-AIRPLANE a0 l4-0 l3-0) (UNLOAD-AIRPLANE p3 a0 l3-0) (LOAD-TRUCK p3 t3 l3-0) (DRIVE-TRUCK t3 l3-0 l3-1 c3) (LOAD-TRUCK p1 t3 l3-1) (DRIVE-TRUCK t3 l3-1 l3-2 c3) (UNLOAD-TRUCK p1 t3 l3-2) (UNLOAD-TRUCK p3 t3 l3-2) (UNLOAD-TRUCK p2 t2 l2-2) (UNLOAD-AIRPLANE p0 a0 l3-0) (DRIVE-TRUCK t1 l1-2 l1-1 c1)\"?", "answer": 12, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c5-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 l3-0 l4-0 - airport c0 c1 c2 c3 c4 - city l0-1 l0-2 l1-1 l1-2 l2-1 l2-2 l3-1 l3-2 l4-1 l4-2 - location p0 p1 p2 p3 - package t0 t1 t2 t3 t4 - truck)\n    (:init (at a0 l2-0) (at p0 l2-2) (at p1 l3-1) (at p2 l2-1) (at p3 l4-1) (at t0 l0-2) (at t1 l1-2) (at t2 l2-2) (at t3 l3-2) (at t4 l4-0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2) (in-city l3-0 c3) (in-city l3-1 c3) (in-city l3-2 c3) (in-city l4-0 c4) (in-city l4-1 c4) (in-city l4-2 c4))\n    (:goal (and (at p0 l3-0) (at p1 l3-2) (at p2 l2-2) (at p3 l3-2)))\n)"}
{"id": -3904020213374035960, "group": "validation_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l2-1, l2-2, and l2-0 are in c2; l4-2, l4-1, and l4-0 are in c4; l1-0, l1-2, and l1-1 are in c1; l3-1, l3-2, and l3-0 are in c3. Currently, t1 is at l1-2, p1 is at l3-1, p2 is at l2-1, p0 and t2 are at l2-2, p3 is at l4-1, t0 is at l0-2, a0 is at l2-0, t4 is at l4-0, t3 is at l3-2. The goal is to reach a state where the following facts hold: p0 is at l3-0, p1 is at l3-2, p3 is at l3-2, and p2 is at l2-2. The available actions are: (load-truck ?obj ?truck ?loc) - place the object ?obj into the truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - place the object ?obj onto the airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - offload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload object ?obj from airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive the truck ?truck in city ?city from location ?loc-from to location ?loc-to, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly airplane ?airplane from airport ?loc-from to airport ?loc-to.", "question": "What is the first inapplicable action in the next sequence of actions: \"(DRIVE-TRUCK t3 l3-2 l3-0 c3) (LOAD-TRUCK p0 t2 l2-2) (DRIVE-TRUCK t2 l2-2 l2-0 c2) (UNLOAD-TRUCK p0 t2 l2-0) (DRIVE-TRUCK t2 l2-0 l2-1 c2) (LOAD-AIRPLANE p0 a0 l2-0) (LOAD-TRUCK p2 t2 l2-1) (DRIVE-TRUCK t2 l2-1 l2-2 c2) (UNLOAD-TRUCK p2 t2 l2-2) (FLY-AIRPLANE a0 l2-0 l4-0) (DRIVE-TRUCK t4 l4-0 l4-1 c4) (LOAD-TRUCK p3 t4 l4-1) (DRIVE-TRUCK t4 l4-1 l4-0 c4) (UNLOAD-TRUCK p3 t4 l4-0) (DRIVE-TRUCK t4 l4-1 l4-1 c4) (FLY-AIRPLANE a0 l4-0 l3-0) (UNLOAD-AIRPLANE p0 a0 l3-0) (UNLOAD-AIRPLANE p3 a0 l3-0) (LOAD-TRUCK p3 t3 l3-0) (DRIVE-TRUCK t3 l3-0 l3-1 c3) (LOAD-TRUCK p1 t3 l3-1) (DRIVE-TRUCK t3 l3-1 l3-2 c3) (UNLOAD-TRUCK p3 t3 l3-2) (UNLOAD-TRUCK p1 t3 l3-2) (FLY-AIRPLANE a0 l3-0 l4-0)\"?", "answer": 14, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c5-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 l3-0 l4-0 - airport c0 c1 c2 c3 c4 - city l0-1 l0-2 l1-1 l1-2 l2-1 l2-2 l3-1 l3-2 l4-1 l4-2 - location p0 p1 p2 p3 - package t0 t1 t2 t3 t4 - truck)\n    (:init (at a0 l2-0) (at p0 l2-2) (at p1 l3-1) (at p2 l2-1) (at p3 l4-1) (at t0 l0-2) (at t1 l1-2) (at t2 l2-2) (at t3 l3-2) (at t4 l4-0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2) (in-city l3-0 c3) (in-city l3-1 c3) (in-city l3-2 c3) (in-city l4-0 c4) (in-city l4-1 c4) (in-city l4-2 c4))\n    (:goal (and (at p0 l3-0) (at p1 l3-2) (at p2 l2-2) (at p3 l3-2)))\n)"}
{"id": -4997166314147760784, "group": "validation_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-0 and l0-1 are in c0; l1-0 and l1-1 are in c1. Currently, t0, p3, and a0 are at l0-0, p0 and p2 are at l0-1, t1 and p1 are at l1-0. The goal is to reach a state where the following facts hold: p0 is at l1-1, p3 is at l1-1, p2 is at l1-0, and p1 is at l1-1. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc onto the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - unload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - offload the object ?obj from the airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck from its current location ?loc-from in city ?city to the new location ?loc-to within the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from the airport ?loc-from to the airport ?loc-to.", "question": "What is the first inapplicable action in the next sequence of actions: \"(LOAD-TRUCK p1 t1 l1-0) (DRIVE-TRUCK t0 l0-0 l0-1 c0) (LOAD-TRUCK p0 t0 l0-1) (LOAD-TRUCK p2 t0 l0-1) (LOAD-AIRPLANE p3 a0 l0-0) (DRIVE-TRUCK t0 l0-1 l0-0 c0) (UNLOAD-TRUCK p0 t0 l0-0) (UNLOAD-TRUCK p2 t0 l0-0) (LOAD-AIRPLANE p0 a0 l0-0) (LOAD-AIRPLANE p2 a0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (UNLOAD-AIRPLANE p0 a0 l1-0) (LOAD-TRUCK p0 t1 l1-0) (UNLOAD-AIRPLANE p2 a0 l1-0) (UNLOAD-AIRPLANE p3 a0 l1-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (UNLOAD-TRUCK p0 t1 l1-1) (UNLOAD-TRUCK p1 t1 l1-1) (LOAD-AIRPLANE p3 a0 l1-0) (UNLOAD-AIRPLANE p3 a0 l1-0) (LOAD-AIRPLANE p3 a0 l1-0) (UNLOAD-AIRPLANE p3 a0 l1-0) (LOAD-AIRPLANE p3 a0 l1-0) (LOAD-AIRPLANE p1 a0 l0-0) (LOAD-AIRPLANE p3 a0 l1-0) (UNLOAD-AIRPLANE p3 a0 l1-0) (LOAD-AIRPLANE p3 a0 l1-0) (UNLOAD-AIRPLANE p3 a0 l1-0) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (LOAD-TRUCK p3 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (UNLOAD-TRUCK p3 t1 l1-1)\"?", "answer": 23, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s2-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l1-1 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p0 l0-1) (at p1 l1-0) (at p2 l0-1) (at p3 l0-0) (at t0 l0-0) (at t1 l1-0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l1-0 c1) (in-city l1-1 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-1)))\n)"}
{"id": 3264153577772858774, "group": "validation_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. \nThe locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l1-0, l1-2, and l1-1 are in c1. \nCurrently, t1, p0, and p3 are at l1-2, p2 is at l1-0, p1 is at l1-1, t0 is at l0-2, a0 is at l0-0. The goal is to reach a state where the following facts hold: p3 is at l0-1, p2 is at l1-2, p0 is at l0-2, and p1 is at l1-2. The available actions are: (load-truck ?obj ?truck ?loc) - load object ?obj into truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc onto the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - unload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload the object ?obj from the airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive the truck ?truck in city ?city from location ?loc-from to location ?loc-to, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from airport ?loc-from to airport ?loc-to.", "question": "What is the first inapplicable action in the next sequence of actions: \"(LOAD-TRUCK p0 t1 l1-2) (LOAD-TRUCK p3 t1 l1-2) (LOAD-TRUCK p3 t1 l1-2) (UNLOAD-TRUCK p0 t1 l1-0) (LOAD-TRUCK p2 t1 l1-0) (UNLOAD-TRUCK p3 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (LOAD-TRUCK p1 t1 l1-1) (FLY-AIRPLANE a0 l0-0 l1-0) (LOAD-AIRPLANE p0 a0 l1-0) (LOAD-AIRPLANE p3 a0 l1-0) (DRIVE-TRUCK t1 l1-1 l1-2 c1) (UNLOAD-TRUCK p1 t1 l1-2) (UNLOAD-TRUCK p2 t1 l1-2) (FLY-AIRPLANE a0 l1-0 l0-0) (UNLOAD-AIRPLANE p0 a0 l0-0) (UNLOAD-AIRPLANE p3 a0 l0-0) (DRIVE-TRUCK t0 l0-2 l0-0 c0) (LOAD-TRUCK p0 t0 l0-0) (LOAD-TRUCK p3 t0 l0-0) (DRIVE-TRUCK t0 l0-0 l0-1 c0) (UNLOAD-TRUCK p3 t0 l0-1) (DRIVE-TRUCK t0 l0-1 l0-2 c0) (UNLOAD-TRUCK p0 t0 l0-2) (DRIVE-TRUCK t1 l1-2 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1)\"?", "answer": 2, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p0 l1-2) (at p1 l1-1) (at p2 l1-0) (at p3 l1-2) (at t0 l0-2) (at t1 l1-2) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l0-2) (at p1 l1-2) (at p2 l1-2) (at p3 l0-1)))\n)"}
{"id": 3429519911232464068, "group": "validation_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 3 trucks and 1 airplane, as well as 4 packages. There are 9 locations across 3 cities. \nThe locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l2-1, l2-2, and l2-0 are in c2; l1-0, l1-2, and l1-1 are in c1. \nCurrently, t1 and p1 are at l1-2, t2 is at l2-2, t0 is at l0-2, p0 and a0 are at l2-0, p3 is at l2-1, p2 is at l0-1. The goal is to reach a state where the following facts hold: p1 is at l2-0, p2 is at l2-2, p3 is at l1-2, and p0 is at l2-1. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc into the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - offload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload the object ?obj from the airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive the truck ?truck in city ?city from location ?loc-from to location ?loc-to, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from airport ?loc-from to airport ?loc-to.", "question": "What is the first inapplicable action in the next sequence of actions: \"(LOAD-TRUCK p1 t1 l1-2) (DRIVE-TRUCK t0 l0-2 l0-1 c0) (LOAD-TRUCK p2 t0 l0-1) (DRIVE-TRUCK t0 l0-1 l0-2 c0) (DRIVE-TRUCK t0 l0-2 l0-0 c0) (UNLOAD-TRUCK p2 t0 l0-0) (DRIVE-TRUCK t2 l2-2 l2-0 c2) (LOAD-TRUCK p0 t2 l2-0) (DRIVE-TRUCK t2 l2-0 l2-1 c2) (UNLOAD-TRUCK p0 t2 l2-1) (LOAD-TRUCK p3 t2 l2-1) (DRIVE-TRUCK t1 l1-2 l1-0 c1) (UNLOAD-TRUCK p1 t1 l1-0) (FLY-AIRPLANE a0 l2-0 l0-0) (LOAD-AIRPLANE p2 a0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (LOAD-AIRPLANE p1 a0 l1-0) (DRIVE-TRUCK t2 l2-1 l2-0 c2) (UNLOAD-TRUCK p3 t2 l2-0) (FLY-AIRPLANE a0 l1-0 l2-0) (UNLOAD-AIRPLANE p1 a0 l2-0) (LOAD-AIRPLANE p3 a0 l1-0) (LOAD-TRUCK p2 t2 l2-0) (LOAD-AIRPLANE p3 a0 l2-0) (DRIVE-TRUCK t2 l2-0 l2-2 c2) (UNLOAD-TRUCK p2 t2 l2-2) (FLY-AIRPLANE a0 l2-0 l1-0) (UNLOAD-AIRPLANE p3 a0 l1-0) (LOAD-TRUCK p3 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-2 c1) (UNLOAD-TRUCK p3 t1 l1-2)\"?", "answer": 21, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c3-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 - airport c0 c1 c2 - city l0-1 l0-2 l1-1 l1-2 l2-1 l2-2 - location p0 p1 p2 p3 - package t0 t1 t2 - truck)\n    (:init (at a0 l2-0) (at p0 l2-0) (at p1 l1-2) (at p2 l0-1) (at p3 l2-1) (at t0 l0-2) (at t1 l1-2) (at t2 l2-2) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2))\n    (:goal (and (at p0 l2-1) (at p1 l2-0) (at p2 l2-2) (at p3 l1-2)))\n)"}
{"id": 6085821344967771329, "group": "validation_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. \nThe locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l1-0, l1-2, and l1-1 are in c1. \nCurrently, t1 is at l1-2, p0 and a0 are at l0-0, p1 is at l1-1, t0 and p3 are at l0-2, p2 is at l0-1. The goal is to reach a state where the following facts hold: p0 is at l1-1, p2 is at l1-0, p3 is at l1-2, and p1 is at l1-1. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc onto the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - offload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - remove the object ?obj from the airplane ?airplane and place it on the location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive the truck ?truck in city ?city from location ?loc-from to location ?loc-to, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from the airport ?loc-from to the airport ?loc-to.", "question": "What is the first inapplicable action in the next sequence of actions: \"(DRIVE-TRUCK t1 l1-2 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (LOAD-TRUCK p3 t0 l0-2) (DRIVE-TRUCK t0 l0-2 l0-1 c0) (LOAD-TRUCK p2 t0 l0-1) (LOAD-AIRPLANE p0 a0 l0-0) (DRIVE-TRUCK t0 l0-1 l0-0 c0) (UNLOAD-TRUCK p3 t1 l1-1) (UNLOAD-TRUCK p2 t0 l0-0) (LOAD-AIRPLANE p2 a0 l0-0) (LOAD-AIRPLANE p3 a0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (UNLOAD-AIRPLANE p3 a0 l1-0) (UNLOAD-AIRPLANE p0 a0 l1-0) (LOAD-TRUCK p3 t1 l1-0) (LOAD-TRUCK p0 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-2 c1) (UNLOAD-TRUCK p3 t1 l1-2) (DRIVE-TRUCK t1 l1-2 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (UNLOAD-TRUCK p0 t1 l1-1) (UNLOAD-AIRPLANE p2 a0 l1-0)\"?", "answer": 7, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p0 l0-0) (at p1 l1-1) (at p2 l0-1) (at p3 l0-2) (at t0 l0-2) (at t1 l1-2) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-2)))\n)"}
{"id": -5837180489821515199, "group": "validation_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. \nThe locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l1-0, l1-2, and l1-1 are in c1. \nCurrently, t1, p0, and p3 are at l1-2, p2 is at l1-0, p1 is at l1-1, t0 is at l0-2, a0 is at l0-0. The goal is to reach a state where the following facts hold: p3 is at l0-1, p2 is at l1-2, p0 is at l0-2, and p1 is at l1-2. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc into the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - offload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload object ?obj from airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck from its current location ?loc-from in city ?city to the new location ?loc-to within the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from location ?loc-from to location ?loc-to.", "question": "What is the first inapplicable action in the next sequence of actions: \"(LOAD-TRUCK p0 t1 l1-2) (LOAD-TRUCK p3 t1 l1-2) (DRIVE-TRUCK t1 l1-2 l1-0 c1) (UNLOAD-TRUCK p0 t1 l1-0) (LOAD-TRUCK p2 t1 l1-0) (UNLOAD-TRUCK p3 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (LOAD-TRUCK p1 t1 l1-1) (FLY-AIRPLANE a0 l0-0 l1-0) (LOAD-AIRPLANE p0 a0 l1-0) (LOAD-AIRPLANE p3 a0 l1-0) (DRIVE-TRUCK t1 l1-1 l1-2 c1) (UNLOAD-TRUCK p1 t1 l1-2) (UNLOAD-TRUCK p2 t1 l1-2) (FLY-AIRPLANE a0 l1-0 l0-0) (UNLOAD-AIRPLANE p0 a0 l0-0) (UNLOAD-AIRPLANE p3 a0 l0-0) (DRIVE-TRUCK t0 l0-2 l0-0 c0) (LOAD-TRUCK p0 t0 l0-0) (LOAD-TRUCK p3 t0 l0-0) (DRIVE-TRUCK t0 l0-0 l0-1 c0) (UNLOAD-TRUCK p0 t0 l0-0) (DRIVE-TRUCK t0 l0-1 l0-2 c0) (UNLOAD-TRUCK p0 t0 l0-2) (DRIVE-TRUCK t1 l1-2 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t1 l1-0 l1-1 c1)\"?", "answer": 21, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p0 l1-2) (at p1 l1-1) (at p2 l1-0) (at p3 l1-2) (at t0 l0-2) (at t1 l1-2) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l0-2) (at p1 l1-2) (at p2 l1-2) (at p3 l0-1)))\n)"}
{"id": 6626038777387080313, "group": "validation_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. \nThe locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l1-0, l1-2, and l1-1 are in c1. \nCurrently, t1, p0, and p3 are at l1-2, p2 is at l1-0, p1 is at l1-1, t0 is at l0-2, a0 is at l0-0. The goal is to reach a state where the following facts hold: p3 is at l0-1, p2 is at l1-2, p0 is at l0-2, and p1 is at l1-2. The available actions are: (load-truck ?obj ?truck ?loc) - load object ?obj into truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc onto the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - offload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload object ?obj from airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck which is in location ?loc-from in city ?city to another location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from the airport ?loc-from to the airport ?loc-to.", "question": "What is the first inapplicable action in the next sequence of actions: \"(UNLOAD-TRUCK p0 t1 l1-1) (LOAD-TRUCK p3 t1 l1-2) (DRIVE-TRUCK t1 l1-2 l1-0 c1) (UNLOAD-TRUCK p0 t1 l1-0) (LOAD-TRUCK p2 t1 l1-0) (UNLOAD-TRUCK p3 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (LOAD-TRUCK p1 t1 l1-1) (FLY-AIRPLANE a0 l0-0 l1-0) (LOAD-AIRPLANE p0 a0 l1-0) (LOAD-AIRPLANE p3 a0 l1-0) (DRIVE-TRUCK t1 l1-1 l1-2 c1) (UNLOAD-TRUCK p1 t1 l1-2) (UNLOAD-TRUCK p2 t1 l1-2) (FLY-AIRPLANE a0 l1-0 l0-0) (UNLOAD-AIRPLANE p0 a0 l0-0) (UNLOAD-AIRPLANE p3 a0 l0-0) (DRIVE-TRUCK t0 l0-2 l0-0 c0) (LOAD-TRUCK p0 t0 l0-0) (LOAD-TRUCK p3 t0 l0-0) (DRIVE-TRUCK t0 l0-0 l0-1 c0) (UNLOAD-TRUCK p3 t0 l0-1) (DRIVE-TRUCK t0 l0-1 l0-2 c0) (UNLOAD-TRUCK p0 t0 l0-2) (LOAD-TRUCK p1 t1 l1-2) (UNLOAD-TRUCK p1 t1 l1-2) (LOAD-TRUCK p1 t1 l1-2) (UNLOAD-TRUCK p1 t1 l1-2) (LOAD-TRUCK p1 t1 l1-2) (UNLOAD-TRUCK p1 t1 l1-2) (LOAD-TRUCK p1 t1 l1-2) (UNLOAD-TRUCK p1 t1 l1-2) (LOAD-TRUCK p1 t1 l1-2) (UNLOAD-TRUCK p1 t1 l1-2)\"?", "answer": 0, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p0 l1-2) (at p1 l1-1) (at p2 l1-0) (at p3 l1-2) (at t0 l0-2) (at t1 l1-2) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l0-2) (at p1 l1-2) (at p2 l1-2) (at p3 l0-1)))\n)"}
