{"id": 6860131839813234206, "group": "reachable_atom_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and its arm is empty. The following locations have soft rock: f2-3f, f1-3f, f2-1f, f0-3f, and f2-2f. The gold is at f0-3f location. The laser is at f0-0f location.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The robot is holding a bomb and The robot is at position f0-0f. B. Hard rock at f2-3f. C. Hard rock at f2-3f. D. The robot is at position f0-1f and The robot is at position f1-2f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot is holding a bomb and The robot is at position f0-0f", "Hard rock at f2-3f", "Hard rock at f2-3f", "The robot is at position f0-1f and The robot is at position f1-2f"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 785464957730132074, "group": "reachable_atom_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-3f and its arm is empty. The following locations have hard rock: f1-4f, f1-1f, and f1-3f. The following locations have soft rock: f2-4f, f2-3f, f2-1f, f0-4f, and f2-2f. The gold is at f0-4f location. The laser is at f0-1f location.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The robot is at position f2-0f and The robot is at position f0-4f. B. Soft rock at f2-4f and Soft rock at f1-0f. C. The robot is at position f1-2f. D. The gold is at f1-2f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot is at position f2-0f and The robot is at position f0-4f", "Soft rock at f2-4f and Soft rock at f1-0f", "The robot is at position f1-2f", "The gold is at f1-2f location"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 3894057849770424628, "group": "reachable_atom_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and is holding a bomb. The following locations have hard rock: f3-3f, f1-2f, f1-1f, f1-3f, and f3-1f. The following locations have soft rock: f2-3f, f2-1f, f3-2f, f0-1f, f0-2f, f0-3f, and f2-2f. The gold is at f0-3f location. The laser is at f0-0f location.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The robot is at position f2-0f. B. Location(s) f3-0f is clear and Hard rock at f2-0f. C. The robot is at position f0-2f and The robot is at position f2-3f. D. The gold is at f1-1f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot is at position f2-0f", "Location(s) f3-0f is clear and Hard rock at f2-0f", "The robot is at position f0-2f and The robot is at position f2-3f", "The gold is at f1-1f location"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 3627310480467772438, "group": "reachable_atom_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and its arm is empty. The following locations have soft rock: f2-3f, f1-3f, f2-1f, f1-2f, and f2-2f. The gold is at f0-3f location. The laser is at f0-0f location.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The robot is at position f0-2f and The robot is at position f1-3f. B. The gold is at f0-1f location and Location(s) f0-1f is clear. C. The robot is at position f1-0f. D. Location(s) f0-2f is clear and Soft rock at f0-1f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot is at position f0-2f and The robot is at position f1-3f", "The gold is at f0-1f location and Location(s) f0-1f is clear", "The robot is at position f1-0f", "Location(s) f0-2f is clear and Soft rock at f0-1f"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -7235201258142496833, "group": "reachable_atom_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x3 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and its arm is empty. The following locations have hard rock: f1-2f. The following locations have soft rock: f0-2f, f1-1f, and f2-2f. The gold is at f0-2f location. The laser is at f0-0f location.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The gold is at f2-2f location and The robot is at position f0-0f. B. The robot is holding a laser. C. Hard rock at f1-1f. D. The robot is at position f0-0f and The robot is at position f0-2f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The gold is at f2-2f location and The robot is at position f0-0f", "The robot is holding a laser", "Hard rock at f1-1f", "The robot is at position f0-0f and The robot is at position f0-2f"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 4122394715649816648, "group": "reachable_atom_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-1f and is holding a laser. The following locations have hard rock: f3-3f, f1-2f, f1-3f, and f3-1f. The following locations have soft rock: f2-3f, f2-1f, f3-2f, and f2-2f. The gold is at f0-3f location.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The gold is at f1-2f location. B. The laser is at f2-2f location and The laser is at f1-1f location. C. The robot is at position f1-0f. D. Hard rock at f3-3f and The gold is at f1-1f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The gold is at f1-2f location", "The laser is at f2-2f location and The laser is at f1-1f location", "The robot is at position f1-0f", "Hard rock at f3-3f and The gold is at f1-1f location"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -3916892767882262448, "group": "reachable_atom_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and is holding gold. The following locations have hard rock: f1-4f, f1-1f, and f1-3f. The following locations have soft rock: f2-4f, f2-3f, f2-1f, f1-2f, and f2-2f. The gold is at f0-4f location. The laser is at f0-0f location.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Soft rock at f0-2f. B. Hard rock at f0-0f and The robot is holding gold. C. The robot is at position f2-0f and The robot is at position f1-1f. D. The robot is at position f0-2f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Soft rock at f0-2f", "Hard rock at f0-0f and The robot is holding gold", "The robot is at position f2-0f and The robot is at position f1-1f", "The robot is at position f0-2f"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -7730830926993374507, "group": "reachable_atom_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and is holding a laser. The following locations have soft rock: f2-3f, f1-3f, f2-1f, f0-3f, and f2-2f. The gold is at f0-3f location.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The robot's arm is empty and The laser is at f0-1f location. B. Soft rock at f0-0f. C. The gold is at f0-0f location. D. The robot is at position f1-3f and The robot is at position f2-2f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot's arm is empty and The laser is at f0-1f location", "Soft rock at f0-0f", "The gold is at f0-0f location", "The robot is at position f1-3f and The robot is at position f2-2f"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -7792959348656605519, "group": "reachable_atom_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x3 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-1f and its arm is empty. The following locations have hard rock: f1-2f. The following locations have soft rock: f2-1f and f2-2f. The gold is at f0-2f location. The laser is at f1-0f location.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Soft rock at f0-1f. B. Soft rock at f0-0f. C. The robot is at position f0-0f. D. The laser is at f2-0f location and The laser is at f0-1f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Soft rock at f0-1f", "Soft rock at f0-0f", "The robot is at position f0-0f", "The laser is at f2-0f location and The laser is at f0-1f location"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 3828496417764887999, "group": "reachable_atom_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and its arm is empty. The following locations have hard rock: f3-3f, f1-2f, f1-3f, and f3-1f. The following locations have soft rock: f2-3f, f2-1f, f3-2f, and f2-2f. The gold is at f0-3f location. The laser is at f0-1f location.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The robot is at position f0-2f. B. The laser is at f2-2f location and The robot is holding a laser. C. The gold is at f3-1f location. D. Hard rock at f2-1f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot is at position f0-2f", "The laser is at f2-2f location and The robot is holding a laser", "The gold is at f3-1f location", "Hard rock at f2-1f"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
