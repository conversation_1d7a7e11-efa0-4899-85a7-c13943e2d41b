{"id": 8165825906630188856, "group": "landmarks_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 2 trucks, 12 pallets, 10 depots, 12 hoists, numbered consecutively. Currently, pallet1, pallet7, pallet11, pallet5, pallet10, crate1, pallet9, pallet3, pallet4, pallet6, pallet8, and pallet0 are clear; hoist3, hoist1, hoist5, hoist4, hoist2, hoist7, hoist9, hoist10, hoist0, hoist11, and hoist6 are available; hoist5 is at depot5, truck1 is at depot4, pallet4 is at depot4, pallet6 is at depot6, hoist4 is at depot4, hoist2 is at depot2, pallet0 is at depot0, crate1 is at depot2, pallet1 is at depot1, hoist10 is at distributor0, pallet2 is at depot2, hoist8 is at depot8, pallet8 is at depot8, pallet9 is at depot9, hoist7 is at depot7, hoist0 is at depot0, pallet11 is at distributor1, hoist11 is at distributor1, pallet3 is at depot3, pallet5 is at depot5, pallet10 is at distributor0, pallet7 is at depot7, truck0 is at depot0, hoist6 is at depot6, hoist9 is at depot9, hoist1 is at depot1, and hoist3 is at depot3; crate1 is on pallet2; hoist8 is lifting crate0. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? hoist2 is lifting crate1", "answer": "yes"}
{"id": 2758005415289797972, "group": "landmarks_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 2 trucks, 5 pallets, 3 depots, 5 hoists, numbered consecutively. Currently, pallet2, pallet3, pallet4, crate0, and pallet0 are clear; hoist3, hoist1, hoist4, hoist2, and hoist0 are available; truck1 is at distributor0, hoist2 is at depot2, crate0 is at depot1, pallet0 is at depot0, pallet3 is at distributor0, pallet1 is at depot1, pallet2 is at depot2, hoist0 is at depot0, pallet4 is at distributor1, truck0 is at distributor0, hoist4 is at distributor1, hoist1 is at depot1, and hoist3 is at distributor0; crate0 is on pallet1; crate1 is in truck1. The goal is to reach a state where the following facts hold: crate1 is on pallet2 and crate0 is on pallet1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? crate0 is on crate1", "answer": "no"}
{"id": -2588131594349645260, "group": "landmarks_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 2 trucks, 9 pallets, 7 depots, 9 hoists, numbered consecutively. Currently, pallet2, pallet7, pallet5, crate1, pallet4, crate0, pallet6, pallet8, and pallet0 are clear; hoist3, hoist1, hoist8, hoist5, hoist4, hoist2, hoist7, hoist0, and hoist6 are available; hoist5 is at depot5, pallet4 is at depot4, pallet6 is at depot6, hoist4 is at depot4, hoist2 is at depot2, crate0 is at depot1, pallet0 is at depot0, hoist8 is at distributor1, hoist7 is at distributor0, pallet1 is at depot1, pallet2 is at depot2, pallet8 is at distributor1, hoist0 is at depot0, pallet3 is at depot3, pallet5 is at depot5, pallet7 is at distributor0, truck0 is at distributor0, hoist6 is at depot6, truck1 is at depot2, hoist1 is at depot1, crate1 is at depot3, and hoist3 is at depot3; crate0 is on pallet1 and crate1 is on pallet3. The goal is to reach a state where the following facts hold: crate1 is on pallet3 and crate0 is on pallet8.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? hoist8 is lifting crate0", "answer": "yes"}
{"id": -7868129010590336624, "group": "landmarks_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 2 trucks, 12 pallets, 10 depots, 12 hoists, numbered consecutively. Currently, pallet1, pallet2, pallet7, pallet11, pallet5, pallet10, pallet9, pallet3, pallet4, pallet6, pallet8, and pallet0 are clear; hoist3, hoist1, hoist5, hoist4, hoist2, hoist7, hoist9, hoist10, hoist0, hoist11, and hoist6 are available; hoist5 is at depot5, pallet4 is at depot4, pallet6 is at depot6, hoist4 is at depot4, hoist2 is at depot2, pallet0 is at depot0, pallet1 is at depot1, hoist10 is at distributor0, pallet2 is at depot2, hoist8 is at depot8, pallet8 is at depot8, pallet9 is at depot9, truck1 is at depot9, hoist7 is at depot7, hoist0 is at depot0, pallet11 is at distributor1, hoist11 is at distributor1, pallet3 is at depot3, pallet5 is at depot5, pallet10 is at distributor0, pallet7 is at depot7, truck0 is at depot0, hoist6 is at depot6, hoist9 is at depot9, hoist1 is at depot1, and hoist3 is at depot3; crate1 is in truck1; hoist8 is lifting crate0. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? truck0 is at depot5", "answer": "no"}
{"id": 588373741395195971, "group": "landmarks_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 2 trucks, 12 pallets, 10 depots, 12 hoists, numbered consecutively. Currently, pallet1, pallet7, pallet11, pallet5, pallet10, crate1, pallet3, pallet9, pallet4, crate0, pallet6, and pallet0 are clear; hoist3, hoist1, hoist8, hoist5, hoist4, hoist2, hoist7, hoist9, hoist10, hoist0, hoist11, and hoist6 are available; hoist5 is at depot5, pallet4 is at depot4, pallet6 is at depot6, hoist4 is at depot4, hoist2 is at depot2, pallet0 is at depot0, crate1 is at depot2, crate0 is at depot8, pallet1 is at depot1, hoist10 is at distributor0, pallet2 is at depot2, hoist8 is at depot8, pallet8 is at depot8, pallet9 is at depot9, hoist7 is at depot7, hoist0 is at depot0, pallet11 is at distributor1, hoist11 is at distributor1, truck1 is at depot7, pallet3 is at depot3, pallet5 is at depot5, pallet10 is at distributor0, pallet7 is at depot7, truck0 is at depot0, hoist6 is at depot6, hoist9 is at depot9, hoist1 is at depot1, and hoist3 is at depot3; crate0 is on pallet8 and crate1 is on pallet2. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? truck1 is at depot6", "answer": "no"}
{"id": 3023192143043428847, "group": "landmarks_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 2 trucks, 5 pallets, 3 depots, 5 hoists, numbered consecutively. Currently, pallet2, pallet3, pallet4, crate0, and pallet0 are clear; hoist3, hoist1, hoist4, hoist2, and hoist0 are available; truck1 is at depot1, hoist2 is at depot2, crate0 is at depot1, pallet0 is at depot0, pallet3 is at distributor0, pallet1 is at depot1, pallet2 is at depot2, hoist0 is at depot0, pallet4 is at distributor1, truck0 is at distributor0, hoist4 is at distributor1, hoist1 is at depot1, and hoist3 is at distributor0; crate0 is on pallet1; crate1 is in truck0. The goal is to reach a state where the following facts hold: crate1 is on pallet2 and crate0 is on pallet1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? crate0 is on pallet4", "answer": "no"}
{"id": 5174141819573240570, "group": "landmarks_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 2 trucks, 9 pallets, 7 depots, 9 hoists, numbered consecutively. Currently, pallet1, pallet2, pallet7, pallet5, crate1, pallet4, pallet6, pallet8, and pallet0 are clear; hoist3, hoist1, hoist5, hoist4, hoist2, hoist7, hoist0, and hoist6 are available; hoist5 is at depot5, pallet4 is at depot4, pallet6 is at depot6, hoist4 is at depot4, hoist2 is at depot2, pallet0 is at depot0, hoist8 is at distributor1, hoist7 is at distributor0, pallet1 is at depot1, pallet2 is at depot2, pallet8 is at distributor1, truck1 is at depot3, hoist0 is at depot0, pallet3 is at depot3, pallet5 is at depot5, pallet7 is at distributor0, hoist6 is at depot6, truck0 is at depot6, hoist1 is at depot1, crate1 is at depot3, and hoist3 is at depot3; crate1 is on pallet3; hoist8 is lifting crate0. The goal is to reach a state where the following facts hold: crate1 is on pallet3 and crate0 is on pallet8.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? crate0 is on pallet8", "answer": "yes"}
{"id": -7442729827958268158, "group": "landmarks_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 2 trucks, 4 pallets, 2 depots, 4 hoists, numbered consecutively. Currently, pallet1, pallet2, pallet3, and crate0 are clear; hoist3, hoist1, hoist2, and hoist0 are available; hoist2 is at distributor0, pallet0 is at depot0, crate0 is at depot0, pallet1 is at depot1, pallet2 is at distributor0, hoist0 is at depot0, truck1 is at distributor1, truck0 is at depot0, pallet3 is at distributor1, hoist1 is at depot1, and hoist3 is at distributor1; crate0 is on pallet0; crate1 is in truck0. The goal is to reach a state where the following facts hold: crate1 is on pallet0 and crate0 is on crate1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? hoist1 is lifting crate0", "answer": "no"}
{"id": 7526694284247197983, "group": "landmarks_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 2 trucks, 9 pallets, 7 depots, 9 hoists, numbered consecutively. Currently, pallet1, pallet2, pallet7, pallet5, crate1, pallet4, pallet6, pallet8, and pallet0 are clear; hoist3, hoist1, hoist8, hoist5, hoist4, hoist2, hoist7, hoist0, and hoist6 are available; hoist5 is at depot5, pallet4 is at depot4, pallet6 is at depot6, hoist4 is at depot4, hoist2 is at depot2, pallet0 is at depot0, hoist8 is at distributor1, hoist7 is at distributor0, pallet1 is at depot1, pallet2 is at depot2, pallet8 is at distributor1, hoist0 is at depot0, pallet3 is at depot3, pallet5 is at depot5, pallet7 is at distributor0, truck1 is at depot0, hoist6 is at depot6, hoist1 is at depot1, crate1 is at depot3, hoist3 is at depot3, and truck0 is at depot1; crate1 is on pallet3; crate0 is in truck0. The goal is to reach a state where the following facts hold: crate1 is on pallet3 and crate0 is on pallet8.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? hoist4 is lifting crate0", "answer": "no"}
{"id": -5415575682295523374, "group": "landmarks_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 2 trucks, 5 pallets, 3 depots, 5 hoists, numbered consecutively. Currently, pallet2, crate1, pallet3, pallet4, and pallet0 are clear; hoist3, hoist1, hoist4, hoist2, and hoist0 are available; crate1 is at depot1, hoist2 is at depot2, crate0 is at depot1, pallet0 is at depot0, pallet3 is at distributor0, pallet1 is at depot1, pallet2 is at depot2, hoist0 is at depot0, pallet4 is at distributor1, hoist4 is at distributor1, truck1 is at depot2, hoist1 is at depot1, hoist3 is at distributor0, and truck0 is at depot1; crate0 is on pallet1 and crate1 is on crate0. The goal is to reach a state where the following facts hold: crate1 is on pallet2 and crate0 is on pallet1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? crate1 is on crate1", "answer": "no"}
