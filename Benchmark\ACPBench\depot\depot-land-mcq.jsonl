{"id": -2538260229138553878, "group": "landmarks_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 6 pallets, 4 depots, 6 hoists, 2 trucks, 3 crates, numbered consecutively. Currently, pallet0, pallet2, crate0, pallet1, pallet5, and pallet4 are clear; hoist0, hoist2, hoist4, hoist5, and hoist3 are available; pallet4 is at distributor0, pallet1 is at depot1, truck0 is at depot3, hoist0 is at depot0, pallet5 is at distributor1, pallet0 is at depot0, pallet2 is at depot2, pallet3 is at depot3, hoist5 is at distributor1, hoist1 is at depot1, truck1 is at distributor1, hoist2 is at depot2, hoist3 is at depot3, crate0 is at depot3, and hoist4 is at distributor0; crate0 is on pallet3; crate1 is in truck0; hoist1 is lifting crate2. The goal is to reach a state where the following facts hold: crate1 is on crate2, crate2 is on pallet4, and crate0 is on pallet1.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. crate1 is at depot2. B. hoist1 is available. C. hoist2 is lifting crate2. D. hoist0 is lifting crate1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["crate1 is at depot2", "hoist1 is available", "hoist2 is lifting crate2", "hoist0 is lifting crate1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 4064811377540810573, "group": "landmarks_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 12 pallets, 10 depots, 12 hoists, 2 trucks, 2 crates, numbered consecutively. Currently, pallet0, pallet9, pallet2, crate0, pallet7, pallet1, pallet11, pallet5, pallet10, pallet6, pallet4, and pallet3 are clear; hoist10, hoist0, hoist7, hoist8, hoist11, hoist2, hoist4, hoist5, hoist6, hoist1, and hoist3 are available; pallet10 is at distributor0, hoist9 is at depot9, pallet7 is at depot7, pallet1 is at depot1, hoist0 is at depot0, crate0 is at depot8, pallet0 is at depot0, pallet8 is at depot8, hoist11 is at distributor1, pallet4 is at depot4, hoist8 is at depot8, pallet11 is at distributor1, pallet2 is at depot2, pallet3 is at depot3, hoist7 is at depot7, pallet5 is at depot5, hoist1 is at depot1, truck0 is at depot9, hoist5 is at depot5, hoist6 is at depot6, truck1 is at depot4, pallet9 is at depot9, hoist2 is at depot2, pallet6 is at depot6, hoist3 is at depot3, hoist4 is at depot4, and hoist10 is at distributor0; crate0 is on pallet8; hoist9 is lifting crate1. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. truck1 is at depot2. B. truck1 is at distributor0. C. crate0 is on pallet1. D. hoist8 is lifting crate0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["truck1 is at depot2", "truck1 is at distributor0", "crate0 is on pallet1", "hoist8 is lifting crate0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 6318226318585049424, "group": "landmarks_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 5 pallets, 3 depots, 5 hoists, 2 trucks, 2 crates, numbered consecutively. Currently, pallet0, pallet1, crate1, pallet4, and pallet3 are clear; hoist0, hoist2, hoist4, and hoist3 are available; pallet1 is at depot1, hoist0 is at depot0, pallet0 is at depot0, pallet4 is at distributor1, pallet2 is at depot2, crate1 is at depot2, truck0 is at depot0, hoist1 is at depot1, pallet3 is at distributor0, hoist4 is at distributor1, hoist2 is at depot2, truck1 is at depot0, and hoist3 is at distributor0; crate1 is on pallet2; hoist1 is lifting crate0. The goal is to reach a state where the following facts hold: crate1 is on pallet2 and crate0 is on pallet1.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. crate1 is in truck1. B. truck1 is at depot1. C. crate0 is on pallet1. D. hoist3 is lifting crate0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["crate1 is in truck1", "truck1 is at depot1", "crate0 is on pallet1", "hoist3 is lifting crate0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 2689151212590719852, "group": "landmarks_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 5 pallets, 3 depots, 5 hoists, 2 trucks, 2 crates, numbered consecutively. Currently, pallet0, pallet2, pallet1, pallet4, and pallet3 are clear; hoist0, hoist4, and hoist3 are available; pallet1 is at depot1, hoist0 is at depot0, pallet0 is at depot0, pallet4 is at distributor1, pallet2 is at depot2, hoist1 is at depot1, truck1 is at depot2, pallet3 is at distributor0, hoist4 is at distributor1, hoist2 is at depot2, hoist3 is at distributor0, and truck0 is at depot2; hoist2 is lifting crate1 and hoist1 is lifting crate0. The goal is to reach a state where the following facts hold: crate1 is on pallet2 and crate0 is on pallet1.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. crate1 is at depot0. B. truck0 is at depot0. C. hoist1 is lifting crate1. D. crate0 is on pallet1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["crate1 is at depot0", "truck0 is at depot0", "hoist1 is lifting crate1", "crate0 is on pallet1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 717344203269010308, "group": "landmarks_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 6 pallets, 4 depots, 6 hoists, 2 trucks, 3 crates, numbered consecutively. Currently, crate2, pallet0, pallet2, pallet1, pallet5, and pallet3 are clear; hoist0, hoist2, hoist5, and hoist3 are available; pallet4 is at distributor0, pallet1 is at depot1, hoist0 is at depot0, pallet5 is at distributor1, pallet0 is at depot0, truck1 is at distributor0, truck0 is at distributor1, pallet2 is at depot2, pallet3 is at depot3, hoist5 is at distributor1, hoist1 is at depot1, hoist2 is at depot2, crate2 is at distributor0, hoist3 is at depot3, and hoist4 is at distributor0; crate2 is on pallet4; hoist4 is lifting crate1 and hoist1 is lifting crate0. The goal is to reach a state where the following facts hold: crate1 is on crate2, crate2 is on pallet4, and crate0 is on pallet1.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. crate1 is on crate2. B. crate1 is on pallet4. C. truck1 is at depot2. D. crate0 is on pallet3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["crate1 is on crate2", "crate1 is on pallet4", "truck1 is at depot2", "crate0 is on pallet3"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 2926004180338008153, "group": "landmarks_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 12 pallets, 10 depots, 12 hoists, 2 trucks, 2 crates, numbered consecutively. Currently, pallet0, pallet9, pallet2, crate0, pallet7, pallet1, pallet11, pallet5, pallet10, pallet6, pallet4, and pallet3 are clear; hoist9, hoist10, hoist0, hoist7, hoist8, hoist11, hoist2, hoist4, hoist5, hoist6, hoist1, and hoist3 are available; pallet10 is at distributor0, hoist9 is at depot9, pallet7 is at depot7, pallet1 is at depot1, hoist0 is at depot0, crate0 is at depot8, pallet0 is at depot0, pallet8 is at depot8, hoist11 is at distributor1, pallet4 is at depot4, truck0 is at depot8, hoist8 is at depot8, pallet11 is at distributor1, pallet2 is at depot2, pallet3 is at depot3, hoist7 is at depot7, pallet5 is at depot5, hoist1 is at depot1, hoist5 is at depot5, hoist6 is at depot6, truck1 is at depot4, pallet9 is at depot9, hoist2 is at depot2, pallet6 is at depot6, hoist3 is at depot3, hoist4 is at depot4, and hoist10 is at distributor0; crate0 is on pallet8; crate1 is in truck0. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. crate1 is at depot7. B. crate1 is at distributor0. C. hoist11 is lifting crate1. D. hoist11 is lifting crate0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["crate1 is at depot7", "crate1 is at distributor0", "hoist11 is lifting crate1", "hoist11 is lifting crate0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 1729045778126097445, "group": "landmarks_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 9 pallets, 7 depots, 9 hoists, 2 trucks, 2 crates, numbered consecutively. Currently, pallet0, pallet2, pallet8, pallet7, pallet1, pallet5, pallet6, crate1, and pallet4 are clear; hoist0, hoist7, hoist8, hoist2, hoist4, hoist5, hoist6, hoist1, and hoist3 are available; pallet1 is at depot1, hoist0 is at depot0, pallet0 is at depot0, pallet4 is at depot4, pallet7 is at distributor0, pallet8 is at distributor1, hoist8 is at distributor1, pallet2 is at depot2, pallet3 is at depot3, crate1 is at depot3, pallet5 is at depot5, hoist1 is at depot1, truck0 is at distributor0, hoist5 is at depot5, hoist6 is at depot6, hoist2 is at depot2, pallet6 is at depot6, hoist3 is at depot3, hoist7 is at distributor0, truck1 is at depot0, and hoist4 is at depot4; crate1 is on pallet3; crate0 is in truck0. The goal is to reach a state where the following facts hold: crate1 is on pallet3 and crate0 is on pallet8.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. crate1 is at depot1. B. crate0 is on pallet2. C. crate0 is on pallet5. D. hoist8 is lifting crate0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["crate1 is at depot1", "crate0 is on pallet2", "crate0 is on pallet5", "hoist8 is lifting crate0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 1046894280134860432, "group": "landmarks_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 6 pallets, 4 depots, 6 hoists, 2 trucks, 3 crates, numbered consecutively. Currently, crate2, pallet0, pallet2, crate0, crate1, and pallet4 are clear; hoist0, hoist2, hoist4, hoist5, hoist1, and hoist3 are available; crate1 is at distributor1, pallet4 is at distributor0, pallet1 is at depot1, hoist0 is at depot0, pallet5 is at distributor1, pallet0 is at depot0, truck0 is at distributor1, pallet2 is at depot2, pallet3 is at depot3, hoist5 is at distributor1, hoist1 is at depot1, truck1 is at depot2, hoist2 is at depot2, hoist3 is at depot3, crate2 is at depot1, crate0 is at depot3, and hoist4 is at distributor0; crate1 is on pallet5, crate2 is on pallet1, and crate0 is on pallet3. The goal is to reach a state where the following facts hold: crate1 is on crate2, crate2 is on pallet4, and crate0 is on pallet1.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. crate2 is in truck1. B. truck1 is at depot0. C. truck1 is at depot3. D. hoist1 is lifting crate2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["crate2 is in truck1", "truck1 is at depot0", "truck1 is at depot3", "hoist1 is lifting crate2"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 2189971100438029835, "group": "landmarks_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 4 pallets, 2 depots, 4 hoists, 2 trucks, 2 crates, numbered consecutively. Currently, pallet2, crate0, crate1, and pallet3 are clear; hoist0, hoist2, hoist1, and hoist3 are available; pallet1 is at depot1, hoist0 is at depot0, pallet0 is at depot0, pallet3 is at distributor1, pallet2 is at distributor0, crate0 is at depot1, hoist2 is at distributor0, truck0 is at distributor0, hoist1 is at depot1, crate1 is at depot0, truck1 is at depot1, and hoist3 is at distributor1; crate0 is on pallet1 and crate1 is on pallet0. The goal is to reach a state where the following facts hold: crate0 is on crate1 and crate1 is on pallet0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. truck0 is at depot0. B. truck0 is at depot1. C. crate0 is on crate1. D. crate1 is on crate0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["truck0 is at depot0", "truck0 is at depot1", "crate0 is on crate1", "crate1 is on crate0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -1730737824790609949, "group": "landmarks_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 6 pallets, 4 depots, 6 hoists, 2 trucks, 3 crates, numbered consecutively. Currently, crate2, pallet0, pallet2, crate0, pallet5, and pallet3 are clear; hoist0, hoist2, hoist4, hoist5, hoist1, and hoist3 are available; pallet4 is at distributor0, pallet1 is at depot1, hoist0 is at depot0, pallet5 is at distributor1, pallet0 is at depot0, pallet2 is at depot2, pallet3 is at depot3, hoist5 is at distributor1, truck0 is at distributor0, hoist1 is at depot1, truck1 is at depot2, hoist2 is at depot2, crate2 is at distributor0, hoist3 is at depot3, crate0 is at depot1, and hoist4 is at distributor0; crate0 is on pallet1 and crate2 is on pallet4; crate1 is in truck0. The goal is to reach a state where the following facts hold: crate1 is on crate2, crate2 is on pallet4, and crate0 is on pallet1.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. crate2 is on crate0. B. crate1 is on crate2. C. crate1 is on pallet5. D. crate0 is on pallet0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["crate2 is on crate0", "crate1 is on crate2", "crate1 is on pallet5", "crate0 is on pallet0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
