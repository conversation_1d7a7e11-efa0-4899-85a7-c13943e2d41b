{"id": 3107273855579105690, "group": "action_justification_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l0-0, l0-1, and l0-2 are in c0; l1-1, l1-2, and l1-0 are in c1; l2-1, l2-0, and l2-2 are in c2. Currently, p0, t0, and a0 are at l0-0, t2 is at l2-1, p4 is at l1-0, p2 is at l0-1, p3 and p1 are at l2-0, t1 is at l1-1. The goal is to reach a state where the following facts hold: p3 is at l1-2, p0 is at l1-2, p1 is at l1-0, p2 is at l1-2, and p4 is at l0-0.", "question": "Given the plan: \"navigate the truck t1 which is in location l1-1 in city c1 to another location l1-0 in the same city, load the object p0 from location l0-0 into the airplane a0, navigate the truck t0 which is in location l0-0 in city c0 to another location l0-1 in the same city, load object p2 into truck t0 at location l0-1, fly the airplane a0 from location l0-0 to location l2-0, load the object p1 from location l2-0 into the airplane a0, load the object p3 from location l2-0 into the airplane a0, fly the airplane a0 from location l2-0 to location l1-0, remove the object p0 from the airplane a0 and place it on the location l1-0, load object p0 into truck t1 at location l1-0, remove the object p1 from the airplane a0 and place it on the location l1-0, remove the object p3 from the airplane a0 and place it on the location l1-0, load object p3 into truck t1 at location l1-0, load the object p4 from location l1-0 into the airplane a0, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-2 in the same city, unload the object p0 from the truck t1 at location l1-2, unload the object p3 from the truck t1 at location l1-2, navigate the truck t0 which is in location l0-1 in city c0 to another location l0-0 in the same city, unload the object p2 from the truck t0 at location l0-0, fly the airplane a0 from location l1-0 to location l0-0, load the object p2 from location l0-0 into the airplane a0, remove the object p4 from the airplane a0 and place it on the location l0-0, fly the airplane a0 from location l0-0 to location l1-0, remove the object p2 from the airplane a0 and place it on the location l1-0, navigate the truck t1 which is in location l1-2 in city c1 to another location l1-0 in the same city, load object p2 into truck t1 at location l1-0, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-2 in the same city, unload the object p2 from the truck t1 at location l1-2, load object p3 into truck t1 at location l1-2, unload the object p3 from the truck t1 at location l1-2\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. load object p3 into truck t1 at location l1-2 and unload the object p3 from the truck t1 at location l1-2. B. fly the airplane a0 from location l0-0 to location l1-0 and remove the object p2 from the airplane a0 and place it on the location l1-0. C. navigate the truck t0 which is in location l0-1 in city c0 to another location l0-0 in the same city and unload the object p2 from the truck t0 at location l0-0. D. navigate the truck t0 which is in location l0-0 in city c0 to another location l0-1 in the same city and load object p2 into truck t0 at location l0-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["load object p3 into truck t1 at location l1-2 and unload the object p3 from the truck t1 at location l1-2", "fly the airplane a0 from location l0-0 to location l1-0 and remove the object p2 from the airplane a0 and place it on the location l1-0", "navigate the truck t0 which is in location l0-1 in city c0 to another location l0-0 in the same city and unload the object p2 from the truck t0 at location l0-0", "navigate the truck t0 which is in location l0-0 in city c0 to another location l0-1 in the same city and load object p2 into truck t0 at location l0-1"]}, "query": "Given the plan: \"navigate the truck t1 which is in location l1-1 in city c1 to another location l1-0 in the same city, load the object p0 from location l0-0 into the airplane a0, navigate the truck t0 which is in location l0-0 in city c0 to another location l0-1 in the same city, load object p2 into truck t0 at location l0-1, fly the airplane a0 from location l0-0 to location l2-0, load the object p1 from location l2-0 into the airplane a0, load the object p3 from location l2-0 into the airplane a0, fly the airplane a0 from location l2-0 to location l1-0, remove the object p0 from the airplane a0 and place it on the location l1-0, load object p0 into truck t1 at location l1-0, remove the object p1 from the airplane a0 and place it on the location l1-0, remove the object p3 from the airplane a0 and place it on the location l1-0, load object p3 into truck t1 at location l1-0, load the object p4 from location l1-0 into the airplane a0, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-2 in the same city, unload the object p0 from the truck t1 at location l1-2, unload the object p3 from the truck t1 at location l1-2, navigate the truck t0 which is in location l0-1 in city c0 to another location l0-0 in the same city, unload the object p2 from the truck t0 at location l0-0, fly the airplane a0 from location l1-0 to location l0-0, load the object p2 from location l0-0 into the airplane a0, remove the object p4 from the airplane a0 and place it on the location l0-0, fly the airplane a0 from location l0-0 to location l1-0, remove the object p2 from the airplane a0 and place it on the location l1-0, navigate the truck t1 which is in location l1-2 in city c1 to another location l1-0 in the same city, load object p2 into truck t1 at location l1-0, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-2 in the same city, unload the object p2 from the truck t1 at location l1-2, load object p3 into truck t1 at location l1-2, unload the object p3 from the truck t1 at location l1-2\"; which pair of consecutive actions can be removed from this plan?", "answer": "A"}
{"id": 2190552462752854868, "group": "action_justification_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-0 and l1-1 are in c1; l0-1 and l0-0 are in c0. Currently, p0 and p2 are at l0-1, p3, t0, and a0 are at l0-0, p1 and t1 are at l1-0. The goal is to reach a state where the following facts hold: p1 is at l1-1, p3 is at l1-1, p2 is at l1-0, and p0 is at l1-1.", "question": "Given the plan: \"load the object p1 from location l1-0 into the truck t1, navigate the truck t0 from its current location l0-0 in city c0 to the new location l0-1 within the same city, load the object p0 from location l0-1 into the truck t0, load the object p2 from location l0-1 into the truck t0, place the object p3 onto the airplane a0 at location l0-0, navigate the truck t0 from its current location l0-1 in city c0 to the new location l0-0 within the same city, offload the object p0 from the truck t0 at location l0-0, offload the object p2 from the truck t0 at location l0-0, place the object p0 onto the airplane a0 at location l0-0, place the object p2 onto the airplane a0 at location l0-0, fly the airplane a0 from the airport l0-0 to the airport l1-0, unload the object p0 from the airplane a0 at location l1-0, load the object p0 from location l1-0 into the truck t1, unload the object p2 from the airplane a0 at location l1-0, unload the object p3 from the airplane a0 at location l1-0, offload the object p1 from the truck t1 at location l1-0, load the object p3 from location l1-0 into the truck t1, navigate the truck t1 from its current location l1-0 in city c1 to the new location l1-1 within the same city, offload the object p0 from the truck t1 at location l1-1, offload the object p3 from the truck t1 at location l1-1, place the object p1 onto the airplane a0 at location l1-0, unload the object p1 from the airplane a0 at location l1-0, place the object p1 onto the airplane a0 at location l1-0, unload the object p1 from the airplane a0 at location l1-0, place the object p1 onto the airplane a0 at location l1-0, unload the object p1 from the airplane a0 at location l1-0, place the object p1 onto the airplane a0 at location l1-0, unload the object p1 from the airplane a0 at location l1-0, place the object p1 onto the airplane a0 at location l1-0, unload the object p1 from the airplane a0 at location l1-0, place the object p1 onto the airplane a0 at location l1-0, unload the object p1 from the airplane a0 at location l1-0, place the object p1 onto the airplane a0 at location l1-0, unload the object p1 from the airplane a0 at location l1-0, navigate the truck t1 from its current location l1-1 in city c1 to the new location l1-0 within the same city, load the object p1 from location l1-0 into the truck t1, navigate the truck t1 from its current location l1-0 in city c1 to the new location l1-1 within the same city, offload the object p1 from the truck t1 at location l1-1\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. load the object p3 from location l1-0 into the truck t1 and navigate the truck t1 from its current location l1-0 in city c1 to the new location l1-1 within the same city. B. unload the object p0 from the airplane a0 at location l1-0 and load the object p0 from location l1-0 into the truck t1. C. place the object p0 onto the airplane a0 at location l0-0 and place the object p2 onto the airplane a0 at location l0-0. D. unload the object p1 from the airplane a0 at location l1-0 and place the object p1 onto the airplane a0 at location l1-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["load the object p3 from location l1-0 into the truck t1 and navigate the truck t1 from its current location l1-0 in city c1 to the new location l1-1 within the same city", "unload the object p0 from the airplane a0 at location l1-0 and load the object p0 from location l1-0 into the truck t1", "place the object p0 onto the airplane a0 at location l0-0 and place the object p2 onto the airplane a0 at location l0-0", "unload the object p1 from the airplane a0 at location l1-0 and place the object p1 onto the airplane a0 at location l1-0"]}, "query": "Given the plan: \"load the object p1 from location l1-0 into the truck t1, navigate the truck t0 from its current location l0-0 in city c0 to the new location l0-1 within the same city, load the object p0 from location l0-1 into the truck t0, load the object p2 from location l0-1 into the truck t0, place the object p3 onto the airplane a0 at location l0-0, navigate the truck t0 from its current location l0-1 in city c0 to the new location l0-0 within the same city, offload the object p0 from the truck t0 at location l0-0, offload the object p2 from the truck t0 at location l0-0, place the object p0 onto the airplane a0 at location l0-0, place the object p2 onto the airplane a0 at location l0-0, fly the airplane a0 from the airport l0-0 to the airport l1-0, unload the object p0 from the airplane a0 at location l1-0, load the object p0 from location l1-0 into the truck t1, unload the object p2 from the airplane a0 at location l1-0, unload the object p3 from the airplane a0 at location l1-0, offload the object p1 from the truck t1 at location l1-0, load the object p3 from location l1-0 into the truck t1, navigate the truck t1 from its current location l1-0 in city c1 to the new location l1-1 within the same city, offload the object p0 from the truck t1 at location l1-1, offload the object p3 from the truck t1 at location l1-1, place the object p1 onto the airplane a0 at location l1-0, unload the object p1 from the airplane a0 at location l1-0, place the object p1 onto the airplane a0 at location l1-0, unload the object p1 from the airplane a0 at location l1-0, place the object p1 onto the airplane a0 at location l1-0, unload the object p1 from the airplane a0 at location l1-0, place the object p1 onto the airplane a0 at location l1-0, unload the object p1 from the airplane a0 at location l1-0, place the object p1 onto the airplane a0 at location l1-0, unload the object p1 from the airplane a0 at location l1-0, place the object p1 onto the airplane a0 at location l1-0, unload the object p1 from the airplane a0 at location l1-0, place the object p1 onto the airplane a0 at location l1-0, unload the object p1 from the airplane a0 at location l1-0, navigate the truck t1 from its current location l1-1 in city c1 to the new location l1-0 within the same city, load the object p1 from location l1-0 into the truck t1, navigate the truck t1 from its current location l1-0 in city c1 to the new location l1-1 within the same city, offload the object p1 from the truck t1 at location l1-1\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
{"id": -8103106912445335811, "group": "action_justification_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l1-9, l1-2, l1-5, l1-4, l1-1, l1-8, l1-6, l1-7, l1-0, and l1-3 are in c1; l4-6, l4-9, l4-0, l4-2, l4-3, l4-5, l4-1, l4-8, l4-4, and l4-7 are in c4; l2-1, l2-2, l2-4, l2-0, l2-3, l2-6, l2-8, l2-9, l2-5, and l2-7 are in c2; l0-6, l0-2, l0-4, l0-0, l0-5, l0-1, l0-8, l0-7, l0-9, and l0-3 are in c0; l3-8, l3-9, l3-4, l3-7, l3-2, l3-1, l3-6, l3-0, l3-3, and l3-5 are in c3. Currently, p3 is at l2-9, p0 is at l2-4, p1 is at l1-8, t1 is at l1-6, t4 is at l4-7, t3 and p2 are at l3-1, t0 is at l0-2, a0 is at l0-0, t2 is at l2-0. The goal is to reach a state where the following facts hold: p1 is at l2-7, p3 is at l1-1, p0 is at l1-5, and p2 is at l4-8.", "question": "Given the plan: \"navigate the truck t4 which is in location l4-7 in city c4 to another location l4-0 in the same city, load the object p2 from location l3-1 into the truck t3, navigate the truck t3 which is in location l3-1 in city c3 to another location l3-0 in the same city, unload object p2 from truck t3 at location l3-0, navigate the truck t1 which is in location l1-6 in city c1 to another location l1-5 in the same city, navigate the truck t0 which is in location l0-2 in city c0 to another location l0-4 in the same city, fly the airplane a0 from location l0-0 to location l1-0, navigate the truck t1 which is in location l1-5 in city c1 to another location l1-8 in the same city, load the object p1 from location l1-8 into the truck t1, navigate the truck t1 which is in location l1-8 in city c1 to another location l1-0 in the same city, unload object p1 from truck t1 at location l1-0, place the object p1 onto the airplane a0 at location l1-0, fly the airplane a0 from location l1-0 to location l2-0, offload the object p1 from the airplane a0 at location l2-0, load the object p1 from location l2-0 into the truck t2, navigate the truck t2 which is in location l2-0 in city c2 to another location l2-9 in the same city, load the object p3 from location l2-9 into the truck t2, navigate the truck t2 which is in location l2-9 in city c2 to another location l2-7 in the same city, unload object p1 from truck t2 at location l2-7, navigate the truck t2 which is in location l2-7 in city c2 to another location l2-4 in the same city, load the object p0 from location l2-4 into the truck t2, fly the airplane a0 from location l2-0 to location l3-0, place the object p2 onto the airplane a0 at location l3-0, fly the airplane a0 from location l3-0 to location l4-0, offload the object p2 from the airplane a0 at location l4-0, load the object p2 from location l4-0 into the truck t4, navigate the truck t4 which is in location l4-0 in city c4 to another location l4-8 in the same city, unload object p2 from truck t4 at location l4-8, navigate the truck t2 which is in location l2-4 in city c2 to another location l2-0 in the same city, unload object p3 from truck t2 at location l2-0, unload object p0 from truck t2 at location l2-0, fly the airplane a0 from location l4-0 to location l1-0, fly the airplane a0 from location l1-0 to location l2-0, place the object p3 onto the airplane a0 at location l2-0, place the object p0 onto the airplane a0 at location l2-0, fly the airplane a0 from location l2-0 to location l1-0, offload the object p3 from the airplane a0 at location l1-0, load the object p3 from location l1-0 into the truck t1, offload the object p0 from the airplane a0 at location l1-0, load the object p0 from location l1-0 into the truck t1, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-1 in the same city, unload object p3 from truck t1 at location l1-1, navigate the truck t1 which is in location l1-1 in city c1 to another location l1-5 in the same city, unload object p0 from truck t1 at location l1-5\"; which of the following actions can be removed from this plan and still have a valid plan? A. offload the object p0 from the airplane a0 at location l1-0. B. fly the airplane a0 from location l0-0 to location l1-0. C. navigate the truck t0 which is in location l0-2 in city c0 to another location l0-4 in the same city. D. place the object p2 onto the airplane a0 at location l3-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["offload the object p0 from the airplane a0 at location l1-0", "fly the airplane a0 from location l0-0 to location l1-0", "navigate the truck t0 which is in location l0-2 in city c0 to another location l0-4 in the same city", "place the object p2 onto the airplane a0 at location l3-0"]}, "query": "Given the plan: \"navigate the truck t4 which is in location l4-7 in city c4 to another location l4-0 in the same city, load the object p2 from location l3-1 into the truck t3, navigate the truck t3 which is in location l3-1 in city c3 to another location l3-0 in the same city, unload object p2 from truck t3 at location l3-0, navigate the truck t1 which is in location l1-6 in city c1 to another location l1-5 in the same city, navigate the truck t0 which is in location l0-2 in city c0 to another location l0-4 in the same city, fly the airplane a0 from location l0-0 to location l1-0, navigate the truck t1 which is in location l1-5 in city c1 to another location l1-8 in the same city, load the object p1 from location l1-8 into the truck t1, navigate the truck t1 which is in location l1-8 in city c1 to another location l1-0 in the same city, unload object p1 from truck t1 at location l1-0, place the object p1 onto the airplane a0 at location l1-0, fly the airplane a0 from location l1-0 to location l2-0, offload the object p1 from the airplane a0 at location l2-0, load the object p1 from location l2-0 into the truck t2, navigate the truck t2 which is in location l2-0 in city c2 to another location l2-9 in the same city, load the object p3 from location l2-9 into the truck t2, navigate the truck t2 which is in location l2-9 in city c2 to another location l2-7 in the same city, unload object p1 from truck t2 at location l2-7, navigate the truck t2 which is in location l2-7 in city c2 to another location l2-4 in the same city, load the object p0 from location l2-4 into the truck t2, fly the airplane a0 from location l2-0 to location l3-0, place the object p2 onto the airplane a0 at location l3-0, fly the airplane a0 from location l3-0 to location l4-0, offload the object p2 from the airplane a0 at location l4-0, load the object p2 from location l4-0 into the truck t4, navigate the truck t4 which is in location l4-0 in city c4 to another location l4-8 in the same city, unload object p2 from truck t4 at location l4-8, navigate the truck t2 which is in location l2-4 in city c2 to another location l2-0 in the same city, unload object p3 from truck t2 at location l2-0, unload object p0 from truck t2 at location l2-0, fly the airplane a0 from location l4-0 to location l1-0, fly the airplane a0 from location l1-0 to location l2-0, place the object p3 onto the airplane a0 at location l2-0, place the object p0 onto the airplane a0 at location l2-0, fly the airplane a0 from location l2-0 to location l1-0, offload the object p3 from the airplane a0 at location l1-0, load the object p3 from location l1-0 into the truck t1, offload the object p0 from the airplane a0 at location l1-0, load the object p0 from location l1-0 into the truck t1, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-1 in the same city, unload object p3 from truck t1 at location l1-1, navigate the truck t1 which is in location l1-1 in city c1 to another location l1-5 in the same city, unload object p0 from truck t1 at location l1-5\"; which action can be removed from this plan?", "answer": "C"}
{"id": -5012149140740367640, "group": "action_justification_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-0 and l1-1 are in c1; l0-1 and l0-0 are in c0. Currently, p0 and p2 are at l0-1, p3, t0, and a0 are at l0-0, p1 and t1 are at l1-0. The goal is to reach a state where the following facts hold: p1 is at l1-1, p3 is at l1-1, p2 is at l1-0, and p0 is at l1-1.", "question": "Given the plan: \"place the object p1 into the truck t1 at location l1-0, drive the truck t0 in city c0 from location l0-0 to location l0-1, place the object p0 into the truck t0 at location l0-1, place the object p2 into the truck t0 at location l0-1, load the object p3 from location l0-0 into the airplane a0, fly the airplane a0 from airport l0-0 to airport l1-0, unload object p3 from airplane a0 at location l1-0, place the object p3 into the truck t1 at location l1-0, drive the truck t1 in city c1 from location l1-0 to location l1-1, unload the object p1 from the truck t1 at location l1-1, unload the object p3 from the truck t1 at location l1-1, drive the truck t0 in city c0 from location l0-1 to location l0-0, unload the object p0 from the truck t0 at location l0-0, unload the object p2 from the truck t0 at location l0-0, fly the airplane a0 from airport l1-0 to airport l0-0, load the object p0 from location l0-0 into the airplane a0, load the object p2 from location l0-0 into the airplane a0, fly the airplane a0 from airport l0-0 to airport l1-0, unload object p0 from airplane a0 at location l1-0, unload object p2 from airplane a0 at location l1-0, drive the truck t1 in city c1 from location l1-1 to location l1-0, place the object p0 into the truck t1 at location l1-0, drive the truck t1 in city c1 from location l1-0 to location l1-1, unload the object p0 from the truck t1 at location l1-1, drive the truck t1 in city c1 from location l1-1 to location l1-0\"; which of the following actions can be removed from this plan and still have a valid plan? A. place the object p0 into the truck t1 at location l1-0. B. drive the truck t1 in city c1 from location l1-1 to location l1-0. C. fly the airplane a0 from airport l0-0 to airport l1-0. D. drive the truck t0 in city c0 from location l0-0 to location l0-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["place the object p0 into the truck t1 at location l1-0", "drive the truck t1 in city c1 from location l1-1 to location l1-0", "fly the airplane a0 from airport l0-0 to airport l1-0", "drive the truck t0 in city c0 from location l0-0 to location l0-1"]}, "query": "Given the plan: \"place the object p1 into the truck t1 at location l1-0, drive the truck t0 in city c0 from location l0-0 to location l0-1, place the object p0 into the truck t0 at location l0-1, place the object p2 into the truck t0 at location l0-1, load the object p3 from location l0-0 into the airplane a0, fly the airplane a0 from airport l0-0 to airport l1-0, unload object p3 from airplane a0 at location l1-0, place the object p3 into the truck t1 at location l1-0, drive the truck t1 in city c1 from location l1-0 to location l1-1, unload the object p1 from the truck t1 at location l1-1, unload the object p3 from the truck t1 at location l1-1, drive the truck t0 in city c0 from location l0-1 to location l0-0, unload the object p0 from the truck t0 at location l0-0, unload the object p2 from the truck t0 at location l0-0, fly the airplane a0 from airport l1-0 to airport l0-0, load the object p0 from location l0-0 into the airplane a0, load the object p2 from location l0-0 into the airplane a0, fly the airplane a0 from airport l0-0 to airport l1-0, unload object p0 from airplane a0 at location l1-0, unload object p2 from airplane a0 at location l1-0, drive the truck t1 in city c1 from location l1-1 to location l1-0, place the object p0 into the truck t1 at location l1-0, drive the truck t1 in city c1 from location l1-0 to location l1-1, unload the object p0 from the truck t1 at location l1-1, drive the truck t1 in city c1 from location l1-1 to location l1-0\"; which action can be removed from this plan?", "answer": "B"}
{"id": -3743068810717782536, "group": "action_justification_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-0, l0-1, and l0-2 are in c0; l1-1, l1-2, and l1-0 are in c1. Currently, p0 and t0 are at l0-0, a0 and p2 are at l1-0, p3 and p1 are at l0-2, t1 is at l1-1. The goal is to reach a state where the following facts hold: p1 is at l0-1, p2 is at l0-1, p0 is at l0-1, and p3 is at l0-1.", "question": "Given the plan: \"navigate the truck t1 which is in location l1-1 in city c1 to another location l1-2 in the same city, place the object p2 onto the airplane a0 at location l1-0, operate the airplane a0 from airport l1-0 to airport l0-0, unload object p2 from airplane a0 at location l0-0, place the object p2 into the truck t0 at location l0-0, place the object p0 into the truck t0 at location l0-0, navigate the truck t0 which is in location l0-0 in city c0 to another location l0-1 in the same city, unload object p2 from truck t0 at location l0-1, navigate the truck t0 which is in location l0-1 in city c0 to another location l0-2 in the same city, place the object p3 into the truck t0 at location l0-2, place the object p1 into the truck t0 at location l0-2, navigate the truck t0 which is in location l0-2 in city c0 to another location l0-1 in the same city, unload object p1 from truck t0 at location l0-1, unload object p0 from truck t0 at location l0-1, unload object p3 from truck t0 at location l0-1, navigate the truck t0 which is in location l0-1 in city c0 to another location l0-0 in the same city\"; which of the following actions can be removed from this plan and still have a valid plan? A. navigate the truck t0 which is in location l0-2 in city c0 to another location l0-1 in the same city. B. unload object p2 from airplane a0 at location l0-0. C. navigate the truck t1 which is in location l1-1 in city c1 to another location l1-2 in the same city. D. place the object p0 into the truck t0 at location l0-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["navigate the truck t0 which is in location l0-2 in city c0 to another location l0-1 in the same city", "unload object p2 from airplane a0 at location l0-0", "navigate the truck t1 which is in location l1-1 in city c1 to another location l1-2 in the same city", "place the object p0 into the truck t0 at location l0-0"]}, "query": "Given the plan: \"navigate the truck t1 which is in location l1-1 in city c1 to another location l1-2 in the same city, place the object p2 onto the airplane a0 at location l1-0, operate the airplane a0 from airport l1-0 to airport l0-0, unload object p2 from airplane a0 at location l0-0, place the object p2 into the truck t0 at location l0-0, place the object p0 into the truck t0 at location l0-0, navigate the truck t0 which is in location l0-0 in city c0 to another location l0-1 in the same city, unload object p2 from truck t0 at location l0-1, navigate the truck t0 which is in location l0-1 in city c0 to another location l0-2 in the same city, place the object p3 into the truck t0 at location l0-2, place the object p1 into the truck t0 at location l0-2, navigate the truck t0 which is in location l0-2 in city c0 to another location l0-1 in the same city, unload object p1 from truck t0 at location l0-1, unload object p0 from truck t0 at location l0-1, unload object p3 from truck t0 at location l0-1, navigate the truck t0 which is in location l0-1 in city c0 to another location l0-0 in the same city\"; which action can be removed from this plan?", "answer": "C"}
{"id": 7984995658118569681, "group": "action_justification_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l1-9, l1-2, l1-5, l1-4, l1-1, l1-8, l1-6, l1-7, l1-0, and l1-3 are in c1; l4-6, l4-9, l4-0, l4-2, l4-3, l4-5, l4-1, l4-8, l4-4, and l4-7 are in c4; l2-1, l2-2, l2-4, l2-0, l2-3, l2-6, l2-8, l2-9, l2-5, and l2-7 are in c2; l0-6, l0-2, l0-4, l0-0, l0-5, l0-1, l0-8, l0-7, l0-9, and l0-3 are in c0; l3-8, l3-9, l3-4, l3-7, l3-2, l3-1, l3-6, l3-0, l3-3, and l3-5 are in c3. Currently, p3 is at l2-9, p0 is at l2-4, p1 is at l1-8, t1 is at l1-6, t4 is at l4-7, t3 and p2 are at l3-1, t0 is at l0-2, a0 is at l0-0, t2 is at l2-0. The goal is to reach a state where the following facts hold: p1 is at l2-7, p3 is at l1-1, p0 is at l1-5, and p2 is at l4-8.", "question": "Given the plan: \"navigate the truck t4 which is in location l4-7 in city c4 to another location l4-2 in the same city, navigate the truck t4 which is in location l4-2 in city c4 to another location l4-7 in the same city, navigate the truck t4 which is in location l4-7 in city c4 to another location l4-0 in the same city, load the object p2 from location l3-1 into the truck t3, navigate the truck t3 which is in location l3-1 in city c3 to another location l3-0 in the same city, unload object p2 from truck t3 at location l3-0, navigate the truck t1 which is in location l1-6 in city c1 to another location l1-1 in the same city, fly the airplane a0 from the airport l0-0 to the airport l1-0, navigate the truck t2 which is in location l2-0 in city c2 to another location l2-9 in the same city, load the object p3 from location l2-9 into the truck t2, navigate the truck t2 which is in location l2-9 in city c2 to another location l2-7 in the same city, navigate the truck t2 which is in location l2-7 in city c2 to another location l2-4 in the same city, load the object p0 from location l2-4 into the truck t2, navigate the truck t1 which is in location l1-1 in city c1 to another location l1-8 in the same city, load the object p1 from location l1-8 into the truck t1, navigate the truck t1 which is in location l1-8 in city c1 to another location l1-0 in the same city, unload object p1 from truck t1 at location l1-0, load the object p1 from location l1-0 into the airplane a0, fly the airplane a0 from the airport l1-0 to the airport l3-0, load the object p2 from location l3-0 into the airplane a0, fly the airplane a0 from the airport l3-0 to the airport l4-0, remove the object p2 from the airplane a0 and place it on the location l4-0, load the object p2 from location l4-0 into the truck t4, navigate the truck t4 which is in location l4-0 in city c4 to another location l4-1 in the same city, navigate the truck t4 which is in location l4-1 in city c4 to another location l4-8 in the same city, unload object p2 from truck t4 at location l4-8, fly the airplane a0 from the airport l4-0 to the airport l2-0, remove the object p1 from the airplane a0 and place it on the location l2-0, navigate the truck t2 which is in location l2-4 in city c2 to another location l2-0 in the same city, unload object p0 from truck t2 at location l2-0, load the object p1 from location l2-0 into the truck t2, unload object p3 from truck t2 at location l2-0, load the object p0 from location l2-0 into the airplane a0, load the object p3 from location l2-0 into the airplane a0, navigate the truck t2 which is in location l2-0 in city c2 to another location l2-7 in the same city, unload object p1 from truck t2 at location l2-7, fly the airplane a0 from the airport l2-0 to the airport l1-0, remove the object p0 from the airplane a0 and place it on the location l1-0, load the object p0 from location l1-0 into the truck t1, remove the object p3 from the airplane a0 and place it on the location l1-0, load the object p3 from location l1-0 into the truck t1, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-5 in the same city, unload object p0 from truck t1 at location l1-5, navigate the truck t1 which is in location l1-5 in city c1 to another location l1-1 in the same city, unload object p3 from truck t1 at location l1-1\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. navigate the truck t1 which is in location l1-5 in city c1 to another location l1-1 in the same city and unload object p3 from truck t1 at location l1-1. B. navigate the truck t4 which is in location l4-7 in city c4 to another location l4-2 in the same city and navigate the truck t4 which is in location l4-2 in city c4 to another location l4-7 in the same city. C. unload object p1 from truck t1 at location l1-0 and load the object p1 from location l1-0 into the airplane a0. D. load the object p1 from location l2-0 into the truck t2 and unload object p3 from truck t2 at location l2-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["navigate the truck t1 which is in location l1-5 in city c1 to another location l1-1 in the same city and unload object p3 from truck t1 at location l1-1", "navigate the truck t4 which is in location l4-7 in city c4 to another location l4-2 in the same city and navigate the truck t4 which is in location l4-2 in city c4 to another location l4-7 in the same city", "unload object p1 from truck t1 at location l1-0 and load the object p1 from location l1-0 into the airplane a0", "load the object p1 from location l2-0 into the truck t2 and unload object p3 from truck t2 at location l2-0"]}, "query": "Given the plan: \"navigate the truck t4 which is in location l4-7 in city c4 to another location l4-2 in the same city, navigate the truck t4 which is in location l4-2 in city c4 to another location l4-7 in the same city, navigate the truck t4 which is in location l4-7 in city c4 to another location l4-0 in the same city, load the object p2 from location l3-1 into the truck t3, navigate the truck t3 which is in location l3-1 in city c3 to another location l3-0 in the same city, unload object p2 from truck t3 at location l3-0, navigate the truck t1 which is in location l1-6 in city c1 to another location l1-1 in the same city, fly the airplane a0 from the airport l0-0 to the airport l1-0, navigate the truck t2 which is in location l2-0 in city c2 to another location l2-9 in the same city, load the object p3 from location l2-9 into the truck t2, navigate the truck t2 which is in location l2-9 in city c2 to another location l2-7 in the same city, navigate the truck t2 which is in location l2-7 in city c2 to another location l2-4 in the same city, load the object p0 from location l2-4 into the truck t2, navigate the truck t1 which is in location l1-1 in city c1 to another location l1-8 in the same city, load the object p1 from location l1-8 into the truck t1, navigate the truck t1 which is in location l1-8 in city c1 to another location l1-0 in the same city, unload object p1 from truck t1 at location l1-0, load the object p1 from location l1-0 into the airplane a0, fly the airplane a0 from the airport l1-0 to the airport l3-0, load the object p2 from location l3-0 into the airplane a0, fly the airplane a0 from the airport l3-0 to the airport l4-0, remove the object p2 from the airplane a0 and place it on the location l4-0, load the object p2 from location l4-0 into the truck t4, navigate the truck t4 which is in location l4-0 in city c4 to another location l4-1 in the same city, navigate the truck t4 which is in location l4-1 in city c4 to another location l4-8 in the same city, unload object p2 from truck t4 at location l4-8, fly the airplane a0 from the airport l4-0 to the airport l2-0, remove the object p1 from the airplane a0 and place it on the location l2-0, navigate the truck t2 which is in location l2-4 in city c2 to another location l2-0 in the same city, unload object p0 from truck t2 at location l2-0, load the object p1 from location l2-0 into the truck t2, unload object p3 from truck t2 at location l2-0, load the object p0 from location l2-0 into the airplane a0, load the object p3 from location l2-0 into the airplane a0, navigate the truck t2 which is in location l2-0 in city c2 to another location l2-7 in the same city, unload object p1 from truck t2 at location l2-7, fly the airplane a0 from the airport l2-0 to the airport l1-0, remove the object p0 from the airplane a0 and place it on the location l1-0, load the object p0 from location l1-0 into the truck t1, remove the object p3 from the airplane a0 and place it on the location l1-0, load the object p3 from location l1-0 into the truck t1, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-5 in the same city, unload object p0 from truck t1 at location l1-5, navigate the truck t1 which is in location l1-5 in city c1 to another location l1-1 in the same city, unload object p3 from truck t1 at location l1-1\"; which pair of consecutive actions can be removed from this plan?", "answer": "B"}
{"id": -6475928601048198393, "group": "action_justification_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l1-9, l1-2, l1-5, l1-4, l1-1, l1-8, l1-6, l1-7, l1-0, and l1-3 are in c1; l4-6, l4-9, l4-0, l4-2, l4-3, l4-5, l4-1, l4-8, l4-4, and l4-7 are in c4; l2-1, l2-2, l2-4, l2-0, l2-3, l2-6, l2-8, l2-9, l2-5, and l2-7 are in c2; l0-6, l0-2, l0-4, l0-0, l0-5, l0-1, l0-8, l0-7, l0-9, and l0-3 are in c0; l3-8, l3-9, l3-4, l3-7, l3-2, l3-1, l3-6, l3-0, l3-3, and l3-5 are in c3. Currently, p3 is at l2-9, p0 is at l2-4, p1 is at l1-8, t1 is at l1-6, t4 is at l4-7, t3 and p2 are at l3-1, t0 is at l0-2, a0 is at l0-0, t2 is at l2-0. The goal is to reach a state where the following facts hold: p1 is at l2-7, p3 is at l1-1, p0 is at l1-5, and p2 is at l4-8.", "question": "Given the plan: \"navigate the truck t4 from location l4-7 in city c4 to location l4-8 in the same city, load the object p2 from location l3-1 into the truck t3, navigate the truck t3 from location l3-1 in city c3 to location l3-0 in the same city, unload object p2 from truck t3 at location l3-0, navigate the truck t1 from location l1-6 in city c1 to location l1-8 in the same city, load the object p1 from location l1-8 into the truck t1, navigate the truck t1 from location l1-8 in city c1 to location l1-0 in the same city, unload object p1 from truck t1 at location l1-0, fly airplane a0 from airport l0-0 to airport l4-0, navigate the truck t2 from location l2-0 in city c2 to location l2-9 in the same city, load the object p3 from location l2-9 into the truck t2, navigate the truck t2 from location l2-9 in city c2 to location l2-4 in the same city, load the object p0 from location l2-4 into the truck t2, navigate the truck t2 from location l2-4 in city c2 to location l2-7 in the same city, fly airplane a0 from airport l4-0 to airport l3-0, load the object p2 from location l3-0 onto the airplane a0, fly airplane a0 from airport l3-0 to airport l1-0, load the object p1 from location l1-0 onto the airplane a0, fly airplane a0 from airport l1-0 to airport l2-0, unload the object p1 from the airplane a0 at location l2-0, navigate the truck t2 from location l2-7 in city c2 to location l2-0 in the same city, unload object p3 from truck t2 at location l2-0, load the object p1 from location l2-0 into the truck t2, unload object p0 from truck t2 at location l2-0, load the object p3 from location l2-0 onto the airplane a0, load the object p0 from location l2-0 onto the airplane a0, navigate the truck t2 from location l2-0 in city c2 to location l2-7 in the same city, unload object p1 from truck t2 at location l2-7, fly airplane a0 from airport l2-0 to airport l1-0, unload the object p3 from the airplane a0 at location l1-0, load the object p3 from location l1-0 into the truck t1, unload the object p0 from the airplane a0 at location l1-0, load the object p0 from location l1-0 into the truck t1, navigate the truck t1 from location l1-0 in city c1 to location l1-1 in the same city, unload object p3 from truck t1 at location l1-1, navigate the truck t1 from location l1-1 in city c1 to location l1-5 in the same city, unload object p0 from truck t1 at location l1-5, navigate the truck t2 from location l2-7 in city c2 to location l2-3 in the same city, navigate the truck t2 from location l2-3 in city c2 to location l2-1 in the same city, fly airplane a0 from airport l1-0 to airport l4-0, unload the object p2 from the airplane a0 at location l4-0, navigate the truck t4 from location l4-8 in city c4 to location l4-0 in the same city, load the object p2 from location l4-0 into the truck t4, navigate the truck t4 from location l4-0 in city c4 to location l4-8 in the same city, unload object p2 from truck t4 at location l4-8\"; which of the following actions can be removed from this plan and still have a valid plan? A. load the object p3 from location l2-9 into the truck t2. B. load the object p0 from location l2-0 onto the airplane a0. C. navigate the truck t2 from location l2-3 in city c2 to location l2-1 in the same city. D. unload object p1 from truck t2 at location l2-7.", "choices": {"label": ["A", "B", "C", "D"], "text": ["load the object p3 from location l2-9 into the truck t2", "load the object p0 from location l2-0 onto the airplane a0", "navigate the truck t2 from location l2-3 in city c2 to location l2-1 in the same city", "unload object p1 from truck t2 at location l2-7"]}, "query": "Given the plan: \"navigate the truck t4 from location l4-7 in city c4 to location l4-8 in the same city, load the object p2 from location l3-1 into the truck t3, navigate the truck t3 from location l3-1 in city c3 to location l3-0 in the same city, unload object p2 from truck t3 at location l3-0, navigate the truck t1 from location l1-6 in city c1 to location l1-8 in the same city, load the object p1 from location l1-8 into the truck t1, navigate the truck t1 from location l1-8 in city c1 to location l1-0 in the same city, unload object p1 from truck t1 at location l1-0, fly airplane a0 from airport l0-0 to airport l4-0, navigate the truck t2 from location l2-0 in city c2 to location l2-9 in the same city, load the object p3 from location l2-9 into the truck t2, navigate the truck t2 from location l2-9 in city c2 to location l2-4 in the same city, load the object p0 from location l2-4 into the truck t2, navigate the truck t2 from location l2-4 in city c2 to location l2-7 in the same city, fly airplane a0 from airport l4-0 to airport l3-0, load the object p2 from location l3-0 onto the airplane a0, fly airplane a0 from airport l3-0 to airport l1-0, load the object p1 from location l1-0 onto the airplane a0, fly airplane a0 from airport l1-0 to airport l2-0, unload the object p1 from the airplane a0 at location l2-0, navigate the truck t2 from location l2-7 in city c2 to location l2-0 in the same city, unload object p3 from truck t2 at location l2-0, load the object p1 from location l2-0 into the truck t2, unload object p0 from truck t2 at location l2-0, load the object p3 from location l2-0 onto the airplane a0, load the object p0 from location l2-0 onto the airplane a0, navigate the truck t2 from location l2-0 in city c2 to location l2-7 in the same city, unload object p1 from truck t2 at location l2-7, fly airplane a0 from airport l2-0 to airport l1-0, unload the object p3 from the airplane a0 at location l1-0, load the object p3 from location l1-0 into the truck t1, unload the object p0 from the airplane a0 at location l1-0, load the object p0 from location l1-0 into the truck t1, navigate the truck t1 from location l1-0 in city c1 to location l1-1 in the same city, unload object p3 from truck t1 at location l1-1, navigate the truck t1 from location l1-1 in city c1 to location l1-5 in the same city, unload object p0 from truck t1 at location l1-5, navigate the truck t2 from location l2-7 in city c2 to location l2-3 in the same city, navigate the truck t2 from location l2-3 in city c2 to location l2-1 in the same city, fly airplane a0 from airport l1-0 to airport l4-0, unload the object p2 from the airplane a0 at location l4-0, navigate the truck t4 from location l4-8 in city c4 to location l4-0 in the same city, load the object p2 from location l4-0 into the truck t4, navigate the truck t4 from location l4-0 in city c4 to location l4-8 in the same city, unload object p2 from truck t4 at location l4-8\"; which action can be removed from this plan?", "answer": "C"}
{"id": 9170219379197585199, "group": "action_justification_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l1-9, l1-2, l1-5, l1-4, l1-1, l1-8, l1-6, l1-7, l1-0, and l1-3 are in c1; l4-6, l4-9, l4-0, l4-2, l4-3, l4-5, l4-1, l4-8, l4-4, and l4-7 are in c4; l2-1, l2-2, l2-4, l2-0, l2-3, l2-6, l2-8, l2-9, l2-5, and l2-7 are in c2; l0-6, l0-2, l0-4, l0-0, l0-5, l0-1, l0-8, l0-7, l0-9, and l0-3 are in c0; l3-8, l3-9, l3-4, l3-7, l3-2, l3-1, l3-6, l3-0, l3-3, and l3-5 are in c3. Currently, p3 is at l2-9, p0 is at l2-4, p1 is at l1-8, t1 is at l1-6, t4 is at l4-7, t3 and p2 are at l3-1, t0 is at l0-2, a0 is at l0-0, t2 is at l2-0. The goal is to reach a state where the following facts hold: p1 is at l2-7, p3 is at l1-1, p0 is at l1-5, and p2 is at l4-8.", "question": "Given the plan: \"navigate the truck t4 which is in location l4-7 in city c4 to another location l4-8 in the same city, load the object p2 from location l3-1 into the truck t3, navigate the truck t3 which is in location l3-1 in city c3 to another location l3-0 in the same city, unload the object p2 from the truck t3 at location l3-0, navigate the truck t1 which is in location l1-6 in city c1 to another location l1-8 in the same city, load the object p1 from location l1-8 into the truck t1, navigate the truck t1 which is in location l1-8 in city c1 to another location l1-0 in the same city, unload the object p1 from the truck t1 at location l1-0, fly the airplane a0 from location l0-0 to location l4-0, navigate the truck t2 which is in location l2-0 in city c2 to another location l2-4 in the same city, load the object p0 from location l2-4 into the truck t2, navigate the truck t2 which is in location l2-4 in city c2 to another location l2-9 in the same city, load the object p3 from location l2-9 into the truck t2, navigate the truck t2 which is in location l2-9 in city c2 to another location l2-7 in the same city, fly the airplane a0 from location l4-0 to location l3-0, load the object p2 from location l3-0 onto the airplane a0, fly the airplane a0 from location l3-0 to location l1-0, load the object p1 from location l1-0 onto the airplane a0, fly the airplane a0 from location l1-0 to location l2-0, unload object p1 from airplane a0 at location l2-0, navigate the truck t2 which is in location l2-7 in city c2 to another location l2-0 in the same city, unload the object p0 from the truck t2 at location l2-0, load the object p1 from location l2-0 into the truck t2, unload the object p3 from the truck t2 at location l2-0, load the object p0 from location l2-0 onto the airplane a0, load the object p3 from location l2-0 onto the airplane a0, navigate the truck t2 which is in location l2-0 in city c2 to another location l2-7 in the same city, unload the object p1 from the truck t2 at location l2-7, fly the airplane a0 from location l2-0 to location l1-0, unload object p0 from airplane a0 at location l1-0, load the object p0 from location l1-0 into the truck t1, unload object p3 from airplane a0 at location l1-0, load the object p3 from location l1-0 into the truck t1, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-5 in the same city, unload the object p0 from the truck t1 at location l1-5, navigate the truck t1 which is in location l1-5 in city c1 to another location l1-1 in the same city, unload the object p3 from the truck t1 at location l1-1, fly the airplane a0 from location l1-0 to location l4-0, unload object p2 from airplane a0 at location l4-0, navigate the truck t4 which is in location l4-8 in city c4 to another location l4-0 in the same city, load the object p2 from location l4-0 into the truck t4, navigate the truck t4 which is in location l4-0 in city c4 to another location l4-8 in the same city, unload the object p2 from the truck t4 at location l4-8, navigate the truck t0 which is in location l0-2 in city c0 to another location l0-8 in the same city, navigate the truck t0 which is in location l0-8 in city c0 to another location l0-3 in the same city\"; which of the following actions can be removed from this plan and still have a valid plan? A. navigate the truck t2 which is in location l2-0 in city c2 to another location l2-4 in the same city. B. navigate the truck t0 which is in location l0-8 in city c0 to another location l0-3 in the same city. C. unload object p2 from airplane a0 at location l4-0. D. navigate the truck t1 which is in location l1-8 in city c1 to another location l1-0 in the same city.", "choices": {"label": ["A", "B", "C", "D"], "text": ["navigate the truck t2 which is in location l2-0 in city c2 to another location l2-4 in the same city", "navigate the truck t0 which is in location l0-8 in city c0 to another location l0-3 in the same city", "unload object p2 from airplane a0 at location l4-0", "navigate the truck t1 which is in location l1-8 in city c1 to another location l1-0 in the same city"]}, "query": "Given the plan: \"navigate the truck t4 which is in location l4-7 in city c4 to another location l4-8 in the same city, load the object p2 from location l3-1 into the truck t3, navigate the truck t3 which is in location l3-1 in city c3 to another location l3-0 in the same city, unload the object p2 from the truck t3 at location l3-0, navigate the truck t1 which is in location l1-6 in city c1 to another location l1-8 in the same city, load the object p1 from location l1-8 into the truck t1, navigate the truck t1 which is in location l1-8 in city c1 to another location l1-0 in the same city, unload the object p1 from the truck t1 at location l1-0, fly the airplane a0 from location l0-0 to location l4-0, navigate the truck t2 which is in location l2-0 in city c2 to another location l2-4 in the same city, load the object p0 from location l2-4 into the truck t2, navigate the truck t2 which is in location l2-4 in city c2 to another location l2-9 in the same city, load the object p3 from location l2-9 into the truck t2, navigate the truck t2 which is in location l2-9 in city c2 to another location l2-7 in the same city, fly the airplane a0 from location l4-0 to location l3-0, load the object p2 from location l3-0 onto the airplane a0, fly the airplane a0 from location l3-0 to location l1-0, load the object p1 from location l1-0 onto the airplane a0, fly the airplane a0 from location l1-0 to location l2-0, unload object p1 from airplane a0 at location l2-0, navigate the truck t2 which is in location l2-7 in city c2 to another location l2-0 in the same city, unload the object p0 from the truck t2 at location l2-0, load the object p1 from location l2-0 into the truck t2, unload the object p3 from the truck t2 at location l2-0, load the object p0 from location l2-0 onto the airplane a0, load the object p3 from location l2-0 onto the airplane a0, navigate the truck t2 which is in location l2-0 in city c2 to another location l2-7 in the same city, unload the object p1 from the truck t2 at location l2-7, fly the airplane a0 from location l2-0 to location l1-0, unload object p0 from airplane a0 at location l1-0, load the object p0 from location l1-0 into the truck t1, unload object p3 from airplane a0 at location l1-0, load the object p3 from location l1-0 into the truck t1, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-5 in the same city, unload the object p0 from the truck t1 at location l1-5, navigate the truck t1 which is in location l1-5 in city c1 to another location l1-1 in the same city, unload the object p3 from the truck t1 at location l1-1, fly the airplane a0 from location l1-0 to location l4-0, unload object p2 from airplane a0 at location l4-0, navigate the truck t4 which is in location l4-8 in city c4 to another location l4-0 in the same city, load the object p2 from location l4-0 into the truck t4, navigate the truck t4 which is in location l4-0 in city c4 to another location l4-8 in the same city, unload the object p2 from the truck t4 at location l4-8, navigate the truck t0 which is in location l0-2 in city c0 to another location l0-8 in the same city, navigate the truck t0 which is in location l0-8 in city c0 to another location l0-3 in the same city\"; which action can be removed from this plan?", "answer": "B"}
{"id": -5861868597177411569, "group": "action_justification_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-0 and l1-1 are in c1; l0-1 and l0-0 are in c0. Currently, p0 and p2 are at l0-1, p3, t0, and a0 are at l0-0, p1 and t1 are at l1-0. The goal is to reach a state where the following facts hold: p1 is at l1-1, p3 is at l1-1, p2 is at l1-0, and p0 is at l1-1.", "question": "Given the plan: \"load the object p1 from location l1-0 into the truck t1, load the object p3 from location l0-0 into the airplane a0, drive the truck t0 in city c0 from location l0-0 to location l0-1, load the object p0 from location l0-1 into the truck t0, load the object p2 from location l0-1 into the truck t0, operate the airplane a0 from airport l0-0 to airport l1-0, unload the object p3 from the airplane a0 at location l1-0, load the object p3 from location l1-0 into the truck t1, drive the truck t1 in city c1 from location l1-0 to location l1-1, offload the object p1 from the truck t1 at location l1-1, offload the object p3 from the truck t1 at location l1-1, drive the truck t0 in city c0 from location l0-1 to location l0-0, offload the object p0 from the truck t0 at location l0-0, offload the object p2 from the truck t0 at location l0-0, drive the truck t0 in city c0 from location l0-0 to location l0-1, operate the airplane a0 from airport l1-0 to airport l0-0, load the object p0 from location l0-0 into the airplane a0, load the object p2 from location l0-0 into the airplane a0, operate the airplane a0 from airport l0-0 to airport l1-0, unload the object p0 from the airplane a0 at location l1-0, unload the object p2 from the airplane a0 at location l1-0, drive the truck t1 in city c1 from location l1-1 to location l1-0, load the object p0 from location l1-0 into the truck t1, drive the truck t1 in city c1 from location l1-0 to location l1-1, offload the object p0 from the truck t1 at location l1-1\"; which of the following actions can be removed from this plan and still have a valid plan? A. offload the object p0 from the truck t1 at location l1-1. B. load the object p0 from location l1-0 into the truck t1. C. drive the truck t0 in city c0 from location l0-1 to location l0-0. D. drive the truck t0 in city c0 from location l0-0 to location l0-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["offload the object p0 from the truck t1 at location l1-1", "load the object p0 from location l1-0 into the truck t1", "drive the truck t0 in city c0 from location l0-1 to location l0-0", "drive the truck t0 in city c0 from location l0-0 to location l0-1"]}, "query": "Given the plan: \"load the object p1 from location l1-0 into the truck t1, load the object p3 from location l0-0 into the airplane a0, drive the truck t0 in city c0 from location l0-0 to location l0-1, load the object p0 from location l0-1 into the truck t0, load the object p2 from location l0-1 into the truck t0, operate the airplane a0 from airport l0-0 to airport l1-0, unload the object p3 from the airplane a0 at location l1-0, load the object p3 from location l1-0 into the truck t1, drive the truck t1 in city c1 from location l1-0 to location l1-1, offload the object p1 from the truck t1 at location l1-1, offload the object p3 from the truck t1 at location l1-1, drive the truck t0 in city c0 from location l0-1 to location l0-0, offload the object p0 from the truck t0 at location l0-0, offload the object p2 from the truck t0 at location l0-0, drive the truck t0 in city c0 from location l0-0 to location l0-1, operate the airplane a0 from airport l1-0 to airport l0-0, load the object p0 from location l0-0 into the airplane a0, load the object p2 from location l0-0 into the airplane a0, operate the airplane a0 from airport l0-0 to airport l1-0, unload the object p0 from the airplane a0 at location l1-0, unload the object p2 from the airplane a0 at location l1-0, drive the truck t1 in city c1 from location l1-1 to location l1-0, load the object p0 from location l1-0 into the truck t1, drive the truck t1 in city c1 from location l1-0 to location l1-1, offload the object p0 from the truck t1 at location l1-1\"; which action can be removed from this plan?", "answer": "D"}
{"id": 8553517495177137007, "group": "action_justification_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l1-9, l1-2, l1-5, l1-4, l1-1, l1-8, l1-6, l1-7, l1-0, and l1-3 are in c1; l4-6, l4-9, l4-0, l4-2, l4-3, l4-5, l4-1, l4-8, l4-4, and l4-7 are in c4; l2-1, l2-2, l2-4, l2-0, l2-3, l2-6, l2-8, l2-9, l2-5, and l2-7 are in c2; l0-6, l0-2, l0-4, l0-0, l0-5, l0-1, l0-8, l0-7, l0-9, and l0-3 are in c0; l3-8, l3-9, l3-4, l3-7, l3-2, l3-1, l3-6, l3-0, l3-3, and l3-5 are in c3. Currently, p3 is at l2-9, p0 is at l2-4, p1 is at l1-8, t1 is at l1-6, t4 is at l4-7, t3 and p2 are at l3-1, t0 is at l0-2, a0 is at l0-0, t2 is at l2-0. The goal is to reach a state where the following facts hold: p1 is at l2-7, p3 is at l1-1, p0 is at l1-5, and p2 is at l4-8.", "question": "Given the plan: \"navigate the truck t4 which is in location l4-7 in city c4 to another location l4-2 in the same city, navigate the truck t4 which is in location l4-2 in city c4 to another location l4-7 in the same city, navigate the truck t4 which is in location l4-7 in city c4 to another location l4-0 in the same city, load the object p2 from location l3-1 into the truck t3, navigate the truck t3 which is in location l3-1 in city c3 to another location l3-0 in the same city, unload object p2 from truck t3 at location l3-0, navigate the truck t1 which is in location l1-6 in city c1 to another location l1-1 in the same city, operate the airplane a0 from airport l0-0 to airport l1-0, navigate the truck t2 which is in location l2-0 in city c2 to another location l2-3 in the same city, navigate the truck t2 which is in location l2-3 in city c2 to another location l2-7 in the same city, navigate the truck t2 which is in location l2-7 in city c2 to another location l2-4 in the same city, load the object p0 from location l2-4 into the truck t2, navigate the truck t2 which is in location l2-4 in city c2 to another location l2-9 in the same city, load the object p3 from location l2-9 into the truck t2, navigate the truck t1 which is in location l1-1 in city c1 to another location l1-8 in the same city, load the object p1 from location l1-8 into the truck t1, navigate the truck t1 which is in location l1-8 in city c1 to another location l1-0 in the same city, unload object p1 from truck t1 at location l1-0, load the object p1 from location l1-0 onto the airplane a0, operate the airplane a0 from airport l1-0 to airport l2-0, remove the object p1 from the airplane a0 and place it on the location l2-0, operate the airplane a0 from airport l2-0 to airport l3-0, load the object p2 from location l3-0 onto the airplane a0, operate the airplane a0 from airport l3-0 to airport l4-0, remove the object p2 from the airplane a0 and place it on the location l4-0, load the object p2 from location l4-0 into the truck t4, navigate the truck t4 which is in location l4-0 in city c4 to another location l4-8 in the same city, unload object p2 from truck t4 at location l4-8, navigate the truck t2 which is in location l2-9 in city c2 to another location l2-0 in the same city, unload object p0 from truck t2 at location l2-0, load the object p1 from location l2-0 into the truck t2, unload object p3 from truck t2 at location l2-0, navigate the truck t2 which is in location l2-0 in city c2 to another location l2-7 in the same city, unload object p1 from truck t2 at location l2-7, operate the airplane a0 from airport l4-0 to airport l1-0, operate the airplane a0 from airport l1-0 to airport l2-0, load the object p0 from location l2-0 onto the airplane a0, load the object p3 from location l2-0 onto the airplane a0, operate the airplane a0 from airport l2-0 to airport l1-0, remove the object p0 from the airplane a0 and place it on the location l1-0, load the object p0 from location l1-0 into the truck t1, remove the object p3 from the airplane a0 and place it on the location l1-0, load the object p3 from location l1-0 into the truck t1, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-5 in the same city, unload object p0 from truck t1 at location l1-5, navigate the truck t1 which is in location l1-5 in city c1 to another location l1-1 in the same city, unload object p3 from truck t1 at location l1-1\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. operate the airplane a0 from airport l3-0 to airport l4-0 and remove the object p2 from the airplane a0 and place it on the location l4-0. B. navigate the truck t4 which is in location l4-7 in city c4 to another location l4-2 in the same city and navigate the truck t4 which is in location l4-2 in city c4 to another location l4-7 in the same city. C. remove the object p1 from the airplane a0 and place it on the location l2-0 and operate the airplane a0 from airport l2-0 to airport l3-0. D. navigate the truck t4 which is in location l4-7 in city c4 to another location l4-0 in the same city and load the object p2 from location l3-1 into the truck t3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["operate the airplane a0 from airport l3-0 to airport l4-0 and remove the object p2 from the airplane a0 and place it on the location l4-0", "navigate the truck t4 which is in location l4-7 in city c4 to another location l4-2 in the same city and navigate the truck t4 which is in location l4-2 in city c4 to another location l4-7 in the same city", "remove the object p1 from the airplane a0 and place it on the location l2-0 and operate the airplane a0 from airport l2-0 to airport l3-0", "navigate the truck t4 which is in location l4-7 in city c4 to another location l4-0 in the same city and load the object p2 from location l3-1 into the truck t3"]}, "query": "Given the plan: \"navigate the truck t4 which is in location l4-7 in city c4 to another location l4-2 in the same city, navigate the truck t4 which is in location l4-2 in city c4 to another location l4-7 in the same city, navigate the truck t4 which is in location l4-7 in city c4 to another location l4-0 in the same city, load the object p2 from location l3-1 into the truck t3, navigate the truck t3 which is in location l3-1 in city c3 to another location l3-0 in the same city, unload object p2 from truck t3 at location l3-0, navigate the truck t1 which is in location l1-6 in city c1 to another location l1-1 in the same city, operate the airplane a0 from airport l0-0 to airport l1-0, navigate the truck t2 which is in location l2-0 in city c2 to another location l2-3 in the same city, navigate the truck t2 which is in location l2-3 in city c2 to another location l2-7 in the same city, navigate the truck t2 which is in location l2-7 in city c2 to another location l2-4 in the same city, load the object p0 from location l2-4 into the truck t2, navigate the truck t2 which is in location l2-4 in city c2 to another location l2-9 in the same city, load the object p3 from location l2-9 into the truck t2, navigate the truck t1 which is in location l1-1 in city c1 to another location l1-8 in the same city, load the object p1 from location l1-8 into the truck t1, navigate the truck t1 which is in location l1-8 in city c1 to another location l1-0 in the same city, unload object p1 from truck t1 at location l1-0, load the object p1 from location l1-0 onto the airplane a0, operate the airplane a0 from airport l1-0 to airport l2-0, remove the object p1 from the airplane a0 and place it on the location l2-0, operate the airplane a0 from airport l2-0 to airport l3-0, load the object p2 from location l3-0 onto the airplane a0, operate the airplane a0 from airport l3-0 to airport l4-0, remove the object p2 from the airplane a0 and place it on the location l4-0, load the object p2 from location l4-0 into the truck t4, navigate the truck t4 which is in location l4-0 in city c4 to another location l4-8 in the same city, unload object p2 from truck t4 at location l4-8, navigate the truck t2 which is in location l2-9 in city c2 to another location l2-0 in the same city, unload object p0 from truck t2 at location l2-0, load the object p1 from location l2-0 into the truck t2, unload object p3 from truck t2 at location l2-0, navigate the truck t2 which is in location l2-0 in city c2 to another location l2-7 in the same city, unload object p1 from truck t2 at location l2-7, operate the airplane a0 from airport l4-0 to airport l1-0, operate the airplane a0 from airport l1-0 to airport l2-0, load the object p0 from location l2-0 onto the airplane a0, load the object p3 from location l2-0 onto the airplane a0, operate the airplane a0 from airport l2-0 to airport l1-0, remove the object p0 from the airplane a0 and place it on the location l1-0, load the object p0 from location l1-0 into the truck t1, remove the object p3 from the airplane a0 and place it on the location l1-0, load the object p3 from location l1-0 into the truck t1, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-5 in the same city, unload object p0 from truck t1 at location l1-5, navigate the truck t1 which is in location l1-5 in city c1 to another location l1-1 in the same city, unload object p3 from truck t1 at location l1-1\"; which pair of consecutive actions can be removed from this plan?", "answer": "B"}
{"id": -8020230305126874419, "group": "action_justification_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-0, l0-1, and l0-2 are in c0; l1-1, l1-2, and l1-0 are in c1. Currently, p3, t1, and p0 are at l1-2, p1 is at l1-1, t0 is at l0-2, a0 is at l0-0, p2 is at l1-0. The goal is to reach a state where the following facts hold: p1 is at l1-2, p0 is at l0-2, p3 is at l0-1, and p2 is at l1-2.", "question": "Given the plan: \"place the object p0 into the truck t1 at location l1-2, place the object p3 into the truck t1 at location l1-2, navigate the truck t1 from location l1-2 in city c1 to location l1-0 in the same city, offload the object p0 from the truck t1 at location l1-0, place the object p2 into the truck t1 at location l1-0, offload the object p3 from the truck t1 at location l1-0, navigate the truck t1 from location l1-0 in city c1 to location l1-1 in the same city, place the object p1 into the truck t1 at location l1-1, fly the airplane a0 from the airport l0-0 to the airport l1-0, load object p0 into airplane a0 at location l1-0, load object p3 into airplane a0 at location l1-0, fly the airplane a0 from the airport l1-0 to the airport l0-0, remove the object p0 from the airplane a0 and place it on the location l0-0, remove the object p3 from the airplane a0 and place it on the location l0-0, navigate the truck t0 from location l0-2 in city c0 to location l0-0 in the same city, place the object p0 into the truck t0 at location l0-0, place the object p3 into the truck t0 at location l0-0, navigate the truck t0 from location l0-0 in city c0 to location l0-1 in the same city, offload the object p3 from the truck t0 at location l0-1, navigate the truck t0 from location l0-1 in city c0 to location l0-2 in the same city, offload the object p0 from the truck t0 at location l0-2, offload the object p1 from the truck t1 at location l1-1, place the object p1 into the truck t1 at location l1-1, navigate the truck t1 from location l1-1 in city c1 to location l1-2 in the same city, offload the object p2 from the truck t1 at location l1-2, navigate the truck t1 from location l1-2 in city c1 to location l1-1 in the same city, offload the object p1 from the truck t1 at location l1-1, place the object p1 into the truck t1 at location l1-1, navigate the truck t1 from location l1-1 in city c1 to location l1-2 in the same city, offload the object p1 from the truck t1 at location l1-2\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. offload the object p1 from the truck t1 at location l1-1 and place the object p1 into the truck t1 at location l1-1. B. fly the airplane a0 from the airport l1-0 to the airport l0-0 and remove the object p0 from the airplane a0 and place it on the location l0-0. C. offload the object p3 from the truck t0 at location l0-1 and navigate the truck t0 from location l0-1 in city c0 to location l0-2 in the same city. D. offload the object p0 from the truck t0 at location l0-2 and offload the object p1 from the truck t1 at location l1-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["offload the object p1 from the truck t1 at location l1-1 and place the object p1 into the truck t1 at location l1-1", "fly the airplane a0 from the airport l1-0 to the airport l0-0 and remove the object p0 from the airplane a0 and place it on the location l0-0", "offload the object p3 from the truck t0 at location l0-1 and navigate the truck t0 from location l0-1 in city c0 to location l0-2 in the same city", "offload the object p0 from the truck t0 at location l0-2 and offload the object p1 from the truck t1 at location l1-1"]}, "query": "Given the plan: \"place the object p0 into the truck t1 at location l1-2, place the object p3 into the truck t1 at location l1-2, navigate the truck t1 from location l1-2 in city c1 to location l1-0 in the same city, offload the object p0 from the truck t1 at location l1-0, place the object p2 into the truck t1 at location l1-0, offload the object p3 from the truck t1 at location l1-0, navigate the truck t1 from location l1-0 in city c1 to location l1-1 in the same city, place the object p1 into the truck t1 at location l1-1, fly the airplane a0 from the airport l0-0 to the airport l1-0, load object p0 into airplane a0 at location l1-0, load object p3 into airplane a0 at location l1-0, fly the airplane a0 from the airport l1-0 to the airport l0-0, remove the object p0 from the airplane a0 and place it on the location l0-0, remove the object p3 from the airplane a0 and place it on the location l0-0, navigate the truck t0 from location l0-2 in city c0 to location l0-0 in the same city, place the object p0 into the truck t0 at location l0-0, place the object p3 into the truck t0 at location l0-0, navigate the truck t0 from location l0-0 in city c0 to location l0-1 in the same city, offload the object p3 from the truck t0 at location l0-1, navigate the truck t0 from location l0-1 in city c0 to location l0-2 in the same city, offload the object p0 from the truck t0 at location l0-2, offload the object p1 from the truck t1 at location l1-1, place the object p1 into the truck t1 at location l1-1, navigate the truck t1 from location l1-1 in city c1 to location l1-2 in the same city, offload the object p2 from the truck t1 at location l1-2, navigate the truck t1 from location l1-2 in city c1 to location l1-1 in the same city, offload the object p1 from the truck t1 at location l1-1, place the object p1 into the truck t1 at location l1-1, navigate the truck t1 from location l1-1 in city c1 to location l1-2 in the same city, offload the object p1 from the truck t1 at location l1-2\"; which pair of consecutive actions can be removed from this plan?", "answer": "A"}
{"id": 3987370885247159070, "group": "action_justification_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-0 and l1-1 are in c1; l0-1 and l0-0 are in c0. Currently, p0 and t1 are at l1-1, p3, p1, and p2 are at l1-0, t0 is at l0-1, a0 is at l0-0. The goal is to reach a state where the following facts hold: p3 is at l0-1, p0 is at l0-0, p1 is at l1-0, and p2 is at l1-0.", "question": "Given the plan: \"drive truck t0 from location l0-1 in city c0 to location l0-0 in the same city, fly the airplane a0 from airport l0-0 to airport l1-0, load the object p3 from location l1-0 into the airplane a0, load the object p0 from location l1-1 into the truck t1, drive truck t1 from location l1-1 in city c1 to location l1-0 in the same city, unload the object p0 from the truck t1 at location l1-0, load the object p0 from location l1-0 into the airplane a0, fly the airplane a0 from airport l1-0 to airport l0-0, remove the object p0 from the airplane a0 and place it on the location l0-0, remove the object p3 from the airplane a0 and place it on the location l0-0, load the object p3 from location l0-0 into the truck t0, drive truck t0 from location l0-0 in city c0 to location l0-1 in the same city, unload the object p3 from the truck t0 at location l0-1, fly the airplane a0 from airport l0-0 to airport l1-0\"; which of the following actions can be removed from this plan and still have a valid plan? A. fly the airplane a0 from airport l0-0 to airport l1-0. B. fly the airplane a0 from airport l1-0 to airport l0-0. C. remove the object p0 from the airplane a0 and place it on the location l0-0. D. load the object p3 from location l0-0 into the truck t0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["fly the airplane a0 from airport l0-0 to airport l1-0", "fly the airplane a0 from airport l1-0 to airport l0-0", "remove the object p0 from the airplane a0 and place it on the location l0-0", "load the object p3 from location l0-0 into the truck t0"]}, "query": "Given the plan: \"drive truck t0 from location l0-1 in city c0 to location l0-0 in the same city, fly the airplane a0 from airport l0-0 to airport l1-0, load the object p3 from location l1-0 into the airplane a0, load the object p0 from location l1-1 into the truck t1, drive truck t1 from location l1-1 in city c1 to location l1-0 in the same city, unload the object p0 from the truck t1 at location l1-0, load the object p0 from location l1-0 into the airplane a0, fly the airplane a0 from airport l1-0 to airport l0-0, remove the object p0 from the airplane a0 and place it on the location l0-0, remove the object p3 from the airplane a0 and place it on the location l0-0, load the object p3 from location l0-0 into the truck t0, drive truck t0 from location l0-0 in city c0 to location l0-1 in the same city, unload the object p3 from the truck t0 at location l0-1, fly the airplane a0 from airport l0-0 to airport l1-0\"; which action can be removed from this plan?", "answer": "A"}
{"id": 5692758605880000032, "group": "action_justification_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-0, l0-1, and l0-2 are in c0; l1-1, l1-2, and l1-0 are in c1. Currently, p0 and a0 are at l0-0, t1 is at l1-2, p1 is at l1-1, p2 is at l0-1, p3 and t0 are at l0-2. The goal is to reach a state where the following facts hold: p1 is at l1-1, p2 is at l1-0, p3 is at l1-2, and p0 is at l1-1.", "question": "Given the plan: \"navigate the truck t1 which is in location l1-2 in city c1 to another location l1-0 in the same city, place the object p3 into the truck t0 at location l0-2, navigate the truck t0 which is in location l0-2 in city c0 to another location l0-1 in the same city, place the object p2 into the truck t0 at location l0-1, navigate the truck t0 which is in location l0-1 in city c0 to another location l0-0 in the same city, unload object p3 from truck t0 at location l0-0, unload object p2 from truck t0 at location l0-0, place the object p2 onto the airplane a0 at location l0-0, place the object p0 into the truck t0 at location l0-0, unload object p0 from truck t0 at location l0-0, place the object p0 onto the airplane a0 at location l0-0, place the object p3 onto the airplane a0 at location l0-0, fly the airplane a0 from the airport l0-0 to the airport l1-0, offload the object p0 from the airplane a0 at location l1-0, offload the object p3 from the airplane a0 at location l1-0, place the object p0 into the truck t1 at location l1-0, offload the object p2 from the airplane a0 at location l1-0, place the object p3 into the truck t1 at location l1-0, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-1 in the same city, unload object p0 from truck t1 at location l1-1, navigate the truck t1 which is in location l1-1 in city c1 to another location l1-2 in the same city, unload object p3 from truck t1 at location l1-2\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. place the object p3 onto the airplane a0 at location l0-0 and fly the airplane a0 from the airport l0-0 to the airport l1-0. B. place the object p3 into the truck t0 at location l0-2 and navigate the truck t0 which is in location l0-2 in city c0 to another location l0-1 in the same city. C. place the object p0 into the truck t0 at location l0-0 and unload object p0 from truck t0 at location l0-0. D. navigate the truck t0 which is in location l0-2 in city c0 to another location l0-1 in the same city and place the object p2 into the truck t0 at location l0-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["place the object p3 onto the airplane a0 at location l0-0 and fly the airplane a0 from the airport l0-0 to the airport l1-0", "place the object p3 into the truck t0 at location l0-2 and navigate the truck t0 which is in location l0-2 in city c0 to another location l0-1 in the same city", "place the object p0 into the truck t0 at location l0-0 and unload object p0 from truck t0 at location l0-0", "navigate the truck t0 which is in location l0-2 in city c0 to another location l0-1 in the same city and place the object p2 into the truck t0 at location l0-1"]}, "query": "Given the plan: \"navigate the truck t1 which is in location l1-2 in city c1 to another location l1-0 in the same city, place the object p3 into the truck t0 at location l0-2, navigate the truck t0 which is in location l0-2 in city c0 to another location l0-1 in the same city, place the object p2 into the truck t0 at location l0-1, navigate the truck t0 which is in location l0-1 in city c0 to another location l0-0 in the same city, unload object p3 from truck t0 at location l0-0, unload object p2 from truck t0 at location l0-0, place the object p2 onto the airplane a0 at location l0-0, place the object p0 into the truck t0 at location l0-0, unload object p0 from truck t0 at location l0-0, place the object p0 onto the airplane a0 at location l0-0, place the object p3 onto the airplane a0 at location l0-0, fly the airplane a0 from the airport l0-0 to the airport l1-0, offload the object p0 from the airplane a0 at location l1-0, offload the object p3 from the airplane a0 at location l1-0, place the object p0 into the truck t1 at location l1-0, offload the object p2 from the airplane a0 at location l1-0, place the object p3 into the truck t1 at location l1-0, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-1 in the same city, unload object p0 from truck t1 at location l1-1, navigate the truck t1 which is in location l1-1 in city c1 to another location l1-2 in the same city, unload object p3 from truck t1 at location l1-2\"; which pair of consecutive actions can be removed from this plan?", "answer": "C"}
{"id": -8083289540892591022, "group": "action_justification_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-0 and l1-1 are in c1; l0-1 and l0-0 are in c0. Currently, p0, p3, and t0 are at l0-0, p2 is at l1-1, a0 and t1 are at l1-0, p1 is at l0-1. The goal is to reach a state where the following facts hold: p1 is at l0-1, p0 is at l0-1, p3 is at l0-0, and p2 is at l0-0.", "question": "Given the plan: \"drive the truck t1 in city c1 from location l1-0 to location l1-1, load object p2 into truck t1 at location l1-1, drive the truck t1 in city c1 from location l1-1 to location l1-0, remove the object p2 from the truck t1 and place it on the location l1-0, load object p2 into airplane a0 at location l1-0, unload object p2 from airplane a0 at location l1-0, load object p2 into airplane a0 at location l1-0, load object p0 into truck t0 at location l0-0, operate the airplane a0 from airport l1-0 to airport l0-0, unload object p2 from airplane a0 at location l0-0, load object p2 into truck t0 at location l0-0, remove the object p2 from the truck t0 and place it on the location l0-0, drive the truck t0 in city c0 from location l0-0 to location l0-1, remove the object p0 from the truck t0 and place it on the location l0-1\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. load object p2 into airplane a0 at location l1-0 and unload object p2 from airplane a0 at location l1-0. B. unload object p2 from airplane a0 at location l0-0 and load object p2 into truck t0 at location l0-0. C. drive the truck t1 in city c1 from location l1-0 to location l1-1 and load object p2 into truck t1 at location l1-1. D. load object p0 into truck t0 at location l0-0 and operate the airplane a0 from airport l1-0 to airport l0-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["load object p2 into airplane a0 at location l1-0 and unload object p2 from airplane a0 at location l1-0", "unload object p2 from airplane a0 at location l0-0 and load object p2 into truck t0 at location l0-0", "drive the truck t1 in city c1 from location l1-0 to location l1-1 and load object p2 into truck t1 at location l1-1", "load object p0 into truck t0 at location l0-0 and operate the airplane a0 from airport l1-0 to airport l0-0"]}, "query": "Given the plan: \"drive the truck t1 in city c1 from location l1-0 to location l1-1, load object p2 into truck t1 at location l1-1, drive the truck t1 in city c1 from location l1-1 to location l1-0, remove the object p2 from the truck t1 and place it on the location l1-0, load object p2 into airplane a0 at location l1-0, unload object p2 from airplane a0 at location l1-0, load object p2 into airplane a0 at location l1-0, load object p0 into truck t0 at location l0-0, operate the airplane a0 from airport l1-0 to airport l0-0, unload object p2 from airplane a0 at location l0-0, load object p2 into truck t0 at location l0-0, remove the object p2 from the truck t0 and place it on the location l0-0, drive the truck t0 in city c0 from location l0-0 to location l0-1, remove the object p0 from the truck t0 and place it on the location l0-1\"; which pair of consecutive actions can be removed from this plan?", "answer": "A"}
{"id": -1594068980366314944, "group": "action_justification_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-0, l0-1, and l0-2 are in c0; l1-1, l1-2, and l1-0 are in c1. Currently, p3, t1, and p0 are at l1-2, p1 is at l1-1, t0 is at l0-2, a0 is at l0-0, p2 is at l1-0. The goal is to reach a state where the following facts hold: p1 is at l1-2, p0 is at l0-2, p3 is at l0-1, and p2 is at l1-2.", "question": "Given the plan: \"load the object p3 from location l1-2 into the truck t1, offload the object p3 from the truck t1 at location l1-2, drive the truck t0 in city c0 from location l0-2 to location l0-0, fly the airplane a0 from airport l0-0 to airport l1-0, load the object p3 from location l1-2 into the truck t1, load the object p0 from location l1-2 into the truck t1, drive the truck t1 in city c1 from location l1-2 to location l1-1, load the object p1 from location l1-1 into the truck t1, drive the truck t1 in city c1 from location l1-1 to location l1-0, load the object p2 from location l1-0 into the truck t1, offload the object p3 from the truck t1 at location l1-0, load the object p3 from location l1-0 into the airplane a0, offload the object p0 from the truck t1 at location l1-0, load the object p0 from location l1-0 into the airplane a0, fly the airplane a0 from airport l1-0 to airport l0-0, remove the object p3 from the airplane a0 and place it on the location l0-0, remove the object p0 from the airplane a0 and place it on the location l0-0, load the object p0 from location l0-0 into the truck t0, load the object p3 from location l0-0 into the truck t0, drive the truck t0 in city c0 from location l0-0 to location l0-2, offload the object p0 from the truck t0 at location l0-2, drive the truck t0 in city c0 from location l0-2 to location l0-1, offload the object p3 from the truck t0 at location l0-1, drive the truck t1 in city c1 from location l1-0 to location l1-2, offload the object p1 from the truck t1 at location l1-2, offload the object p2 from the truck t1 at location l1-2\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. load the object p0 from location l0-0 into the truck t0 and load the object p3 from location l0-0 into the truck t0. B. load the object p3 from location l1-2 into the truck t1 and offload the object p3 from the truck t1 at location l1-2. C. drive the truck t0 in city c0 from location l0-0 to location l0-2 and offload the object p0 from the truck t0 at location l0-2. D. load the object p3 from location l1-2 into the truck t1 and load the object p0 from location l1-2 into the truck t1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["load the object p0 from location l0-0 into the truck t0 and load the object p3 from location l0-0 into the truck t0", "load the object p3 from location l1-2 into the truck t1 and offload the object p3 from the truck t1 at location l1-2", "drive the truck t0 in city c0 from location l0-0 to location l0-2 and offload the object p0 from the truck t0 at location l0-2", "load the object p3 from location l1-2 into the truck t1 and load the object p0 from location l1-2 into the truck t1"]}, "query": "Given the plan: \"load the object p3 from location l1-2 into the truck t1, offload the object p3 from the truck t1 at location l1-2, drive the truck t0 in city c0 from location l0-2 to location l0-0, fly the airplane a0 from airport l0-0 to airport l1-0, load the object p3 from location l1-2 into the truck t1, load the object p0 from location l1-2 into the truck t1, drive the truck t1 in city c1 from location l1-2 to location l1-1, load the object p1 from location l1-1 into the truck t1, drive the truck t1 in city c1 from location l1-1 to location l1-0, load the object p2 from location l1-0 into the truck t1, offload the object p3 from the truck t1 at location l1-0, load the object p3 from location l1-0 into the airplane a0, offload the object p0 from the truck t1 at location l1-0, load the object p0 from location l1-0 into the airplane a0, fly the airplane a0 from airport l1-0 to airport l0-0, remove the object p3 from the airplane a0 and place it on the location l0-0, remove the object p0 from the airplane a0 and place it on the location l0-0, load the object p0 from location l0-0 into the truck t0, load the object p3 from location l0-0 into the truck t0, drive the truck t0 in city c0 from location l0-0 to location l0-2, offload the object p0 from the truck t0 at location l0-2, drive the truck t0 in city c0 from location l0-2 to location l0-1, offload the object p3 from the truck t0 at location l0-1, drive the truck t1 in city c1 from location l1-0 to location l1-2, offload the object p1 from the truck t1 at location l1-2, offload the object p2 from the truck t1 at location l1-2\"; which pair of consecutive actions can be removed from this plan?", "answer": "B"}
