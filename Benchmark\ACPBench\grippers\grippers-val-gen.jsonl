{"id": -260528699474132273, "group": "validation_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball11, ball1, ball9, ball10, ball4, ball2, and ball14 are at room1, ball12, ball15, ball6, and ball13 are at room2, ball5, ball3, ball8, and ball7 are at room3. The goal is to reach a state where the following facts hold: Ball ball9 is at room3 location, Ball ball1 is at room2 location, Ball ball8 is at room1 location, Ball ball12 is at room2 location, Ball ball6 is at room1 location, Ball ball7 is at room3 location, Ball ball3 is at room2 location, Ball ball4 is in room room1, Ball ball2 is in room room1, Ball ball11 is in room room3, Ball ball13 is at room2 location, Ball ball15 is in room room1, Ball ball5 is in room room1, Ball ball14 is at room3 location, and Ball ball10 is in room room3. The available actions are: (move ?r ?from ?to) - move the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - grasp the object ?obj from room ?room with the ?g gripper of robot ?r, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room using the robot ?r with ?g gripper.", "question": "What is the first inapplicable action in the next sequence of actions: \"(pick robot1 ball3 room3 left1) (move robot1 room3 room2) (drop robot1 ball3 room2 left1) (pick robot1 ball15 room2 left1) (move robot1 room2 room1) (pick robot1 ball8 room2 left1) (pick robot1 ball1 room1 left1) (pick robot1 ball10 room1 right1) (move robot1 room1 room2) (drop robot1 ball1 room2 left1) (pick robot1 ball6 room2 left1) (move robot1 room2 room1) (drop robot1 ball6 room1 left1) (pick robot1 ball14 room1 left1) (move robot1 room1 room3) (drop robot1 ball14 room3 left1) (pick robot1 ball8 room3 left1) (drop robot1 ball10 room3 right1) (drop robot1 ball8 room3 left1) (pick robot1 ball5 room3 left1) (pick robot1 ball8 room3 right1) (move robot1 room3 room1) (drop robot1 ball5 room1 left1) (drop robot1 ball8 room1 right1) (pick robot1 ball11 room1 left1) (pick robot1 ball9 room1 right1) (move robot1 room1 room3) (drop robot1 ball11 room3 left1) (drop robot1 ball9 room3 right1)\"?", "answer": 5, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-15)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball10 ball11 ball12 ball13 ball14 ball15 ball2 ball3 ball4 ball5 ball6 ball7 ball8 ball9 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room1) (at ball10 room1) (at ball11 room1) (at ball12 room2) (at ball13 room2) (at ball14 room1) (at ball15 room2) (at ball2 room1) (at ball3 room3) (at ball4 room1) (at ball5 room3) (at ball6 room2) (at ball7 room3) (at ball8 room3) (at ball9 room1) (at-robby robot1 room3) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room2) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room1) (at ball6 room1) (at ball7 room3) (at ball8 room1) (at ball9 room3) (at ball10 room3) (at ball11 room3) (at ball12 room2) (at ball13 room2) (at ball14 room3) (at ball15 room1)))\n)"}
{"id": 1379417208082166804, "group": "validation_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball11, ball1, ball9, ball10, ball4, ball2, and ball14 are at room1, ball12, ball15, ball6, and ball13 are at room2, ball5, ball3, ball8, and ball7 are at room3. The goal is to reach a state where the following facts hold: Ball ball9 is at room3 location, Ball ball1 is at room2 location, Ball ball8 is in room room1, Ball ball12 is at room2 location, Ball ball6 is at room1 location, Ball ball7 is at room3 location, Ball ball3 is in room room2, Ball ball4 is at room1 location, Ball ball2 is in room room1, Ball ball11 is in room room3, Ball ball13 is at room2 location, Ball ball15 is at room1 location, Ball ball5 is at room1 location, Ball ball14 is at room3 location, and Ball ball10 is at room3 location. The available actions are: (move ?r ?from ?to) - move robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - drop object ?obj in room ?room using ?g gripper of robot ?r.", "question": "What is the first inapplicable action in the next sequence of actions: \"(pick robot1 ball3 room3 right1) (move robot1 room3 room2) (drop robot1 ball1 room3 left1) (pick robot1 ball15 room2 right1) (move robot1 room2 room1) (drop robot1 ball15 room1 right1) (pick robot1 ball1 room1 right1) (pick robot1 ball10 room1 left1) (move robot1 room1 room2) (drop robot1 ball1 room2 right1) (pick robot1 ball6 room2 right1) (move robot1 room2 room1) (drop robot1 ball6 room1 right1) (pick robot1 ball14 room1 right1) (move robot1 room1 room3) (drop robot1 ball14 room3 right1) (pick robot1 ball8 room3 right1) (drop robot1 ball8 room3 right1) (pick robot1 ball8 room3 right1) (drop robot1 ball10 room3 left1) (pick robot1 ball5 room3 left1) (move robot1 room3 room1) (drop robot1 ball8 room1 right1) (drop robot1 ball5 room1 left1) (pick robot1 ball11 room1 right1) (pick robot1 ball9 room1 left1) (move robot1 room1 room3) (drop robot1 ball11 room3 right1) (drop robot1 ball9 room3 left1)\"?", "answer": 2, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-15)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball10 ball11 ball12 ball13 ball14 ball15 ball2 ball3 ball4 ball5 ball6 ball7 ball8 ball9 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room1) (at ball10 room1) (at ball11 room1) (at ball12 room2) (at ball13 room2) (at ball14 room1) (at ball15 room2) (at ball2 room1) (at ball3 room3) (at ball4 room1) (at ball5 room3) (at ball6 room2) (at ball7 room3) (at ball8 room3) (at ball9 room1) (at-robby robot1 room3) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room2) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room1) (at ball6 room1) (at ball7 room3) (at ball8 room1) (at ball9 room3) (at ball10 room3) (at ball11 room3) (at ball12 room2) (at ball13 room2) (at ball14 room3) (at ball15 room1)))\n)"}
{"id": 995105623402330722, "group": "validation_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball11, ball1, ball9, ball10, ball4, ball2, and ball14 are at room1, ball12, ball15, ball6, and ball13 are at room2, ball5, ball3, ball8, and ball7 are at room3. The goal is to reach a state where the following facts hold: Ball ball9 is in room room3, Ball ball1 is in room room2, Ball ball8 is in room room1, Ball ball12 is in room room2, Ball ball6 is in room room1, Ball ball7 is at room3 location, Ball ball3 is at room2 location, Ball ball4 is at room1 location, Ball ball2 is in room room1, Ball ball11 is at room3 location, Ball ball13 is at room2 location, Ball ball15 is in room room1, Ball ball5 is in room room1, Ball ball14 is at room3 location, and Ball ball10 is in room room3. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - use the ?g gripper of robot ?r to pick up the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - drop the object ?obj in room ?room using robot ?r with the ?g gripper.", "question": "What is the first inapplicable action in the next sequence of actions: \"(pick robot1 ball8 room3 left1) (pick robot1 ball3 room3 right1) (move robot1 room3 room1) (drop robot1 ball8 room1 left1) (pick robot1 ball1 room1 left1) (move robot1 room1 room2) (drop robot1 ball1 room2 left1) (pick robot1 ball15 room2 left1) (drop robot1 ball3 room2 right1) (pick robot1 ball6 room2 right1) (move robot1 room2 room1) (drop robot1 ball15 room1 left1) (pick robot1 ball10 room1 left1) (drop robot1 ball6 room1 right1) (pick robot1 ball11 room1 right1) (move robot1 room1 room3) (drop robot1 ball11 room3 right1) (drop robot1 ball10 room3 left1) (pick robot1 ball5 room3 right1) (move robot1 room3 room1) (drop robot1 ball1 room1 right1) (pick robot1 ball14 room1 right1) (pick robot1 ball9 room1 left1) (move robot1 room1 room3) (drop robot1 ball9 room3 left1) (drop robot1 ball14 room3 right1)\"?", "answer": 20, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-15)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball10 ball11 ball12 ball13 ball14 ball15 ball2 ball3 ball4 ball5 ball6 ball7 ball8 ball9 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room1) (at ball10 room1) (at ball11 room1) (at ball12 room2) (at ball13 room2) (at ball14 room1) (at ball15 room2) (at ball2 room1) (at ball3 room3) (at ball4 room1) (at ball5 room3) (at ball6 room2) (at ball7 room3) (at ball8 room3) (at ball9 room1) (at-robby robot1 room3) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room2) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room1) (at ball6 room1) (at ball7 room3) (at ball8 room1) (at ball9 room3) (at ball10 room3) (at ball11 room3) (at ball12 room2) (at ball13 room2) (at ball14 room3) (at ball15 room1)))\n)"}
{"id": 5661997200651863105, "group": "validation_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2 and both grippers are free. Additionally, ball1, ball4, and ball2 are at room1, ball3 is at room2. The goal is to reach a state where the following facts hold: Ball ball4 is in room room2, Ball ball3 is in room room2, Ball ball1 is in room room2, and Ball ball2 is in room room2. The available actions are: (move ?r ?from ?to) - move robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with the robot ?r using the ?g gripper from the room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room using the robot ?r with ?g gripper.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move robot1 room2 room1) (drop robot1 ball2 room1 left1) (pick robot1 ball4 room1 right1) (move robot1 room1 room2) (drop robot1 ball4 room2 right1) (pick robot1 ball4 room2 right1) (drop robot1 ball1 room2 left1) (drop robot1 ball4 room2 right1) (move robot1 room2 room1) (pick robot1 ball2 room1 left1) (move robot1 room1 room2) (drop robot1 ball2 room2 left1)\"?", "answer": 1, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-2-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at-robby robot1 room2) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room2) (at ball2 room2) (at ball3 room2) (at ball4 room2)))\n)"}
{"id": 2683343596100087162, "group": "validation_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball5, ball3, and ball7 are at room3, ball1, ball4, and ball2 are at room1, ball6 is at room2. The goal is to reach a state where the following facts hold: Ball ball5 is in room room2, Ball ball1 is in room room3, Ball ball3 is in room room1, Ball ball6 is at room2 location, Ball ball4 is at room1 location, Ball ball2 is at room1 location, and Ball ball7 is at room1 location. The available actions are: (move ?r ?from ?to) - move the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room from the ?g gripper of the robot ?r.", "question": "What is the first inapplicable action in the next sequence of actions: \"(pick robot1 ball3 room3 left1) (pick robot1 ball7 room3 right1) (move robot1 room3 room1) (pick robot1 ball6 room3 right1) (drop robot1 ball3 room1 left1) (pick robot1 ball2 room1 right1) (drop robot1 ball2 room1 right1) (pick robot1 ball1 room1 right1) (move robot1 room1 room3) (drop robot1 ball1 room3 right1) (pick robot1 ball5 room3 right1) (move robot1 room3 room2) (drop robot1 ball5 room2 right1)\"?", "answer": 3, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-7)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 ball5 ball6 ball7 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball3 room3) (at ball4 room1) (at ball5 room3) (at ball6 room2) (at ball7 room3) (at-robby robot1 room3) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room2) (at ball6 room2) (at ball7 room1)))\n)"}
{"id": -5727166729104095590, "group": "validation_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room8 and both grippers are free. Additionally, ball3 is at room10, ball4 and ball2 are at room2, ball1 is at room4. The goal is to reach a state where the following facts hold: Ball ball3 is in room room8, Ball ball2 is at room6 location, Ball ball1 is in room room8, and Ball ball4 is at room9 location. The available actions are: (move ?r ?from ?to) - move the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with the robot ?r using the ?g gripper from the room ?room, and (drop ?r ?obj ?room ?g) - use robot ?r with ?g gripper to place the object ?obj in room ?room.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move robot1 room8 room2) (pick robot1 ball2 room2 right1) (pick robot1 ball4 room2 left1) (move robot1 room2 room6) (drop robot1 ball2 room6 right1) (move robot1 room6 room9) (drop robot1 ball4 room9 left1) (move robot1 room9 room10) (pick robot1 ball3 room10 right1) (move robot1 room10 room7) (move robot1 room7 room4) (pick robot1 ball1 room4 left1) (move robot1 room9 room8) (drop robot1 ball1 room8 left1) (drop robot1 ball3 room8 right1)\"?", "answer": 12, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-10-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room10 room2 room3 room4 room5 room6 room7 room8 room9 - room)\n    (:init (at ball1 room4) (at ball2 room2) (at ball3 room10) (at ball4 room2) (at-robby robot1 room8) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room8) (at ball2 room6) (at ball3 room8) (at ball4 room9)))\n)"}
{"id": -182303282449723258, "group": "validation_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball5, ball3, and ball7 are at room3, ball1, ball4, and ball2 are at room1, ball6 is at room2. The goal is to reach a state where the following facts hold: Ball ball5 is at room2 location, Ball ball1 is at room3 location, Ball ball3 is at room1 location, Ball ball6 is in room room2, Ball ball4 is at room1 location, Ball ball2 is in room room1, and Ball ball7 is in room room1. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room from the ?g gripper of the robot ?r.", "question": "What is the first inapplicable action in the next sequence of actions: \"(pick robot1 ball3 room3 left1) (pick robot1 ball7 room3 right1) (move robot1 room3 room1) (drop robot1 ball3 room1 left1) (drop robot1 ball7 room1 right1) (pick robot1 ball3 room1 left1) (drop robot1 ball3 room1 left1) (pick robot1 ball1 room1 right1) (move robot1 room1 room3) (drop robot1 ball1 room3 right1) (pick robot1 ball5 room3 left1) (move robot1 room3 room2) (drop robot1 ball6 room3 right1)\"?", "answer": 12, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-7)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 ball5 ball6 ball7 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball3 room3) (at ball4 room1) (at ball5 room3) (at ball6 room2) (at ball7 room3) (at-robby robot1 room3) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room2) (at ball6 room2) (at ball7 room1)))\n)"}
{"id": 4715484763633812028, "group": "validation_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room8 and both grippers are free. Additionally, ball3 is at room10, ball4 and ball2 are at room2, ball1 is at room4. The goal is to reach a state where the following facts hold: Ball ball3 is in room room8, Ball ball2 is at room6 location, Ball ball1 is at room8 location, and Ball ball4 is in room room9. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with the robot ?r using the ?g gripper from the room ?room, and (drop ?r ?obj ?room ?g) - use robot ?r with ?g gripper to place the object ?obj in room ?room.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move robot1 room8 room10) (drop robot1 ball2 room4 left1) (move robot1 room10 room9) (move robot1 room9 room4) (pick robot1 ball1 room4 right1) (move robot1 room4 room8) (drop robot1 ball1 room8 right1) (drop robot1 ball3 room8 left1) (move robot1 room8 room2) (pick robot1 ball4 room2 left1) (pick robot1 ball2 room2 right1) (move robot1 room2 room6) (drop robot1 ball2 room6 right1) (move robot1 room6 room9) (drop robot1 ball4 room9 left1)\"?", "answer": 1, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-10-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room10 room2 room3 room4 room5 room6 room7 room8 room9 - room)\n    (:init (at ball1 room4) (at ball2 room2) (at ball3 room10) (at ball4 room2) (at-robby robot1 room8) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room8) (at ball2 room6) (at ball3 room8) (at ball4 room9)))\n)"}
{"id": -2530857857796772486, "group": "validation_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball5, ball3, and ball7 are at room3, ball1, ball4, and ball2 are at room1, ball6 is at room2. The goal is to reach a state where the following facts hold: Ball ball5 is in room room2, Ball ball1 is at room3 location, Ball ball3 is in room room1, Ball ball6 is at room2 location, Ball ball4 is at room1 location, Ball ball2 is in room room1, and Ball ball7 is in room room1. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - use the ?g gripper of robot ?r to drop the object ?obj in room ?room.", "question": "What is the first inapplicable action in the next sequence of actions: \"(pick robot1 ball3 room3 left1) (pick robot1 ball5 room3 right1) (drop robot1 ball5 room3 right1) (pick robot1 ball7 room3 right1) (drop robot1 ball1 room2 left1) (drop robot1 ball3 room1 left1) (drop robot1 ball7 room1 right1) (pick robot1 ball1 room1 right1) (move robot1 room1 room3) (drop robot1 ball1 room3 right1) (pick robot1 ball5 room3 right1) (move robot1 room3 room2) (drop robot1 ball5 room2 right1)\"?", "answer": 4, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-7)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 ball5 ball6 ball7 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball3 room3) (at ball4 room1) (at ball5 room3) (at ball6 room2) (at ball7 room3) (at-robby robot1 room3) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room2) (at ball6 room2) (at ball7 room1)))\n)"}
{"id": -143540826479745835, "group": "validation_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room8 and both grippers are free. Additionally, ball3 is at room10, ball4 and ball2 are at room2, ball1 is at room4. The goal is to reach a state where the following facts hold: Ball ball3 is at room8 location, Ball ball2 is at room6 location, Ball ball1 is in room room8, and Ball ball4 is in room room9. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - use the ?g gripper of robot ?r to pick up the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - drop object ?obj in room ?room using ?g gripper of robot ?r.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move robot1 room8 room2) (move robot1 room2 room4) (pick robot1 ball1 room4 left1) (move robot1 room4 room10) (pick robot1 ball3 room10 right1) (move robot1 room10 room8) (drop robot1 ball1 room8 left1) (drop robot1 ball3 room8 right1) (move robot1 room8 room2) (pick robot1 ball2 room2 right1) (pick robot1 ball4 room2 left1) (move robot1 room2 room9) (drop robot1 ball4 room9 left1) (move robot1 room9 room6) (move robot1 room1 room8)\"?", "answer": 14, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-10-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room10 room2 room3 room4 room5 room6 room7 room8 room9 - room)\n    (:init (at ball1 room4) (at ball2 room2) (at ball3 room10) (at ball4 room2) (at-robby robot1 room8) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room8) (at ball2 room6) (at ball3 room8) (at ball4 room9)))\n)"}
{"id": 5070745387097520946, "group": "validation_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 2 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room1 and both grippers are free. Additionally, ball4 and ball3 are at room2, ball1 and ball2 are at room1. The goal is to reach a state where the following facts hold: Ball ball4 is at room1 location, Ball ball1 is at room1 location, Ball ball2 is at room1 location, and Ball ball3 is in room room1. The available actions are: (move ?r ?from ?to) - move robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with the robot ?r using the ?g gripper from the room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room from the ?g gripper of the robot ?r.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move robot1 room1 room2) (pick robot1 ball3 room2 left1) (drop robot1 ball3 room2 left1) (pick robot1 ball4 room2 right1) (pick robot1 ball3 room2 left1) (move robot1 room2 room1) (drop robot1 ball3 room1 left1) (drop robot1 ball4 room1 right1) (pick robot1 ball4 room2 left1)\"?", "answer": 8, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-2-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room2) (at-robby robot1 room1) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room1) (at ball3 room1) (at ball4 room1)))\n)"}
{"id": -5291605621537774136, "group": "validation_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 2 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room1 and both grippers are free. Additionally, ball4 and ball3 are at room2, ball1 and ball2 are at room1. The goal is to reach a state where the following facts hold: Ball ball4 is in room room1, Ball ball1 is in room room1, Ball ball2 is at room1 location, and Ball ball3 is in room room1. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room using the robot ?r with ?g gripper.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move robot1 room1 room2) (pick robot1 ball3 room2 left1) (pick robot1 ball4 room2 right1) (pick robot1 ball4 room2 left1) (drop robot1 ball3 room1 left1) (drop robot1 ball4 room1 right1) (move robot1 room1 room2) (move robot1 room2 room1) (move robot1 room1 room2)\"?", "answer": 3, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-2-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room2) (at-robby robot1 room1) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room1) (at ball3 room1) (at ball4 room1)))\n)"}
{"id": -8978321776887898105, "group": "validation_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 3 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room3 and both grippers are free. Additionally, ball3 is at room3, ball1, ball4, and ball2 are at room1. The goal is to reach a state where the following facts hold: Ball ball4 is in room room3, Ball ball1 is at room3 location, Ball ball2 is in room room2, and Ball ball3 is in room room3. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room from the ?g gripper of the robot ?r.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move robot1 room3 room1) (pick robot1 ball1 room1 left1) (pick robot1 ball2 room1 right1) (move robot1 room1 room2) (drop robot1 ball2 room2 right1) (move robot1 room2 room1) (pick robot1 ball4 room1 right1) (drop robot1 ball2 room1 right1) (move robot1 room2 room3) (drop robot1 ball1 room3 left1) (drop robot1 ball4 room3 right1)\"?", "answer": 7, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball3 room3) (at ball4 room1) (at-robby robot1 room3) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room3) (at ball2 room2) (at ball3 room3) (at ball4 room3)))\n)"}
{"id": 299556045967411851, "group": "validation_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 3 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room2 and both grippers are free. Additionally, ball4 is at room3, ball1 and ball3 are at room2, ball2 is at room1. The goal is to reach a state where the following facts hold: Ball ball3 is at room2 location, Ball ball4 is in room room1, Ball ball1 is at room1 location, and Ball ball2 is in room room1. The available actions are: (move ?r ?from ?to) - move robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with the robot ?r using the ?g gripper from the room ?room, and (drop ?r ?obj ?room ?g) - drop object ?obj in room ?room using ?g gripper of robot ?r.", "question": "What is the first inapplicable action in the next sequence of actions: \"(pick robot1 ball1 room2 right1) (move robot1 room2 room3) (pick robot1 ball4 room3 left1) (move robot1 room3 room1) (pick robot1 ball2 room1 left1) (pick robot1 ball4 room1 left1) (drop robot1 ball4 room1 left1) (drop robot1 ball1 room1 right1) (move robot1 room1 room2)\"?", "answer": 4, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room2) (at ball2 room1) (at ball3 room2) (at ball4 room3) (at-robby robot1 room2) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1)))\n)"}
{"id": 1964512194049385639, "group": "validation_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 7 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room3 and both grippers are free. Additionally, ball1 and ball2 are at room3, ball4 is at room6, ball3 is at room5. The goal is to reach a state where the following facts hold: Ball ball4 is in room room1, Ball ball1 is in room room1, Ball ball2 is at room2 location, and Ball ball3 is at room3 location. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - drop the object ?obj in room ?room using robot ?r with the ?g gripper.", "question": "What is the first inapplicable action in the next sequence of actions: \"(pick robot1 ball1 room3 left1) (pick robot1 ball2 room3 right1) (move robot1 room3 room4) (move robot1 room4 room2) (drop robot1 ball2 room2 right1) (move robot1 room2 room6) (move robot1 room5 room2) (move robot1 room6 room1) (drop robot1 ball1 room1 left1) (drop robot1 ball4 room1 right1) (move robot1 room1 room5) (pick robot1 ball3 room5 right1) (move robot1 room5 room3) (drop robot1 ball3 room3 right1)\"?", "answer": 6, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-7-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 room6 room7 - room)\n    (:init (at ball1 room3) (at ball2 room3) (at ball3 room5) (at ball4 room6) (at-robby robot1 room3) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
