{"id": -2864396975167255850, "group": "reachable_atom_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l4, with the car c47 on board. The cars are at locations as follows: c35, c3, c40, c22, c43, c39, c26, and c21 are at l2; c42, c44, c4, c24, c45, c48, c10, c36, c1, c17, c18, c7, and c16 are at l0; c27, c38, c14, c29, c46, c30, c41, c8, c0, c9, and c28 are at l4; c32, c33, c11, c2, c15, c49, and c19 are at l1; c34, c12, c5, c25, c13, c6, c20, c31, c37, and c23 are at l3. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Car ?c is on board the ferry.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l4) (at c1 l0) (at c10 l0) (at c11 l1) (at c12 l3) (at c13 l3) (at c14 l4) (at c15 l1) (at c16 l0) (at c17 l0) (at c18 l0) (at c19 l1) (at c2 l1) (at c20 l3) (at c21 l2) (at c22 l2) (at c23 l3) (at c24 l0) (at c25 l3) (at c26 l2) (at c27 l4) (at c28 l4) (at c29 l4) (at c3 l2) (at c30 l4) (at c31 l3) (at c32 l1) (at c33 l1) (at c34 l3) (at c35 l2) (at c36 l0) (at c37 l3) (at c38 l4) (at c39 l2) (at c4 l0) (at c40 l2) (at c41 l4) (at c42 l0) (at c43 l2) (at c44 l0) (at c45 l0) (at c46 l4) (at c48 l0) (at c49 l1) (at c5 l3) (at c6 l3) (at c7 l0) (at c8 l4) (at c9 l4) (at-ferry l4) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3) (on c47))\n    (:goal (and (at c0 l2) (at c1 l0) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l4) (at c6 l3) (at c7 l0) (at c8 l4) (at c9 l4) (at c10 l0) (at c11 l1) (at c12 l3) (at c13 l3) (at c14 l4) (at c15 l1) (at c16 l0) (at c17 l0) (at c18 l0) (at c19 l1) (at c20 l3) (at c21 l2) (at c22 l2) (at c23 l3) (at c24 l0) (at c25 l3) (at c26 l2) (at c27 l4) (at c28 l4) (at c29 l4) (at c30 l4) (at c31 l3) (at c32 l1) (at c33 l1) (at c34 l3) (at c35 l2) (at c36 l0) (at c37 l3) (at c38 l4) (at c39 l2) (at c40 l2) (at c41 l4) (at c42 l0) (at c43 l2) (at c44 l0) (at c45 l0) (at c46 l4) (at c47 l0) (at c48 l0) (at c49 l1)))\n)"}
{"id": -5647963875687030899, "group": "reachable_atom_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l4 location and it is empty. The cars are at locations as follows: c1 is at l3; c2 and c0 are at l4. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Ferry has car ?c on board.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c3)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l4) (at c1 l3) (at c2 l4) (at-ferry l4) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3))\n    (:goal (and (at c0 l3) (at c1 l3) (at c2 l3)))\n)"}
{"id": -1131379865192887020, "group": "reachable_atom_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l3, with the car c0 on board. The cars are at locations as follows: c2 is at l4; c1 is at l3. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Car ?c is on board the ferry.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c3)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c1 l3) (at c2 l4) (at-ferry l3) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3) (on c0))\n    (:goal (and (at c0 l3) (at c1 l3) (at c2 l3)))\n)"}
{"id": 6937316253935150380, "group": "reachable_atom_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l1, with the car c2 on board. The cars are at locations as follows: c1, c0, c5, c8, and c4 are at l1; c9, c6, and c7 are at l0; c3 is at l2. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Ferry has car ?c on board.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l3-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 l2 - location)\n    (:init (at c0 l1) (at c1 l1) (at c3 l2) (at c4 l1) (at c5 l1) (at c6 l0) (at c7 l0) (at c8 l1) (at c9 l0) (at-ferry l1) (not-eq l0 l1) (not-eq l0 l2) (not-eq l1 l0) (not-eq l1 l2) (not-eq l2 l0) (not-eq l2 l1) (on c2))\n    (:goal (and (at c0 l2) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l2) (at c6 l2) (at c7 l0) (at c8 l2) (at c9 l0)))\n)"}
{"id": -698259761663885698, "group": "reachable_atom_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c25 on board. The cars are at locations as follows: c12, c8, c22, and c30 are at l2; c42, c29, c49, c39, c5, c15, c13, c7, c3, c31, c43, c37, c23, and c4 are at l3; c27, c6, c41, c1, c17, c18, c40, c9, c16, and c11 are at l0; c26, c38, c14, c47, c35, c36, c20, c0, c10, c45, c28, and c2 are at l4; c32, c33, c46, c34, c44, c48, c21, c24, and c19 are at l1. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Car ?c is on the ferry.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l4) (at c1 l0) (at c10 l4) (at c11 l0) (at c12 l2) (at c13 l3) (at c14 l4) (at c15 l3) (at c16 l0) (at c17 l0) (at c18 l0) (at c19 l1) (at c2 l4) (at c20 l4) (at c21 l1) (at c22 l2) (at c23 l3) (at c24 l1) (at c26 l4) (at c27 l0) (at c28 l4) (at c29 l3) (at c3 l3) (at c30 l2) (at c31 l3) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l4) (at c36 l4) (at c37 l3) (at c38 l4) (at c39 l3) (at c4 l3) (at c40 l0) (at c41 l0) (at c42 l3) (at c43 l3) (at c44 l1) (at c45 l4) (at c46 l1) (at c47 l4) (at c48 l1) (at c49 l3) (at c5 l3) (at c6 l0) (at c7 l3) (at c8 l2) (at c9 l0) (at-ferry l0) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3) (on c25))\n    (:goal (and (at c0 l2) (at c1 l0) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l4) (at c6 l3) (at c7 l0) (at c8 l4) (at c9 l4) (at c10 l0) (at c11 l1) (at c12 l3) (at c13 l3) (at c14 l4) (at c15 l1) (at c16 l0) (at c17 l0) (at c18 l0) (at c19 l1) (at c20 l3) (at c21 l2) (at c22 l2) (at c23 l3) (at c24 l0) (at c25 l3) (at c26 l2) (at c27 l4) (at c28 l4) (at c29 l4) (at c30 l4) (at c31 l3) (at c32 l1) (at c33 l1) (at c34 l3) (at c35 l2) (at c36 l0) (at c37 l3) (at c38 l4) (at c39 l2) (at c40 l2) (at c41 l4) (at c42 l0) (at c43 l2) (at c44 l0) (at c45 l0) (at c46 l4) (at c47 l0) (at c48 l0) (at c49 l1)))\n)"}
{"id": 1088204001465786034, "group": "reachable_atom_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c1 is at l2; c0 and c2 are at l3. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Car ?c is on board the ferry.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c3)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l3) (at c1 l2) (at c2 l3) (at-ferry l2) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3))\n    (:goal (and (at c0 l3) (at c1 l3) (at c2 l3)))\n)"}
{"id": 5886848006365778327, "group": "reachable_atom_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c36 on board. The cars are at locations as follows: c39, c47, c17, c31, c32, c37, c10, c1, c28, c5, c41, c40, c45, c42, c15, c38, c27, c16, c46, c34, c4, c48, c43, c13, c18, c24, c49, and c7 are at l1; c21, c44, c35, c26, c22, c6, c20, c12, c8, c33, c25, c3, c23, c0, c30, c14, c9, c29, c2, c19, and c11 are at l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Car ?c is on the ferry.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l1) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l1) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c2 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l0) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c3 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l0) (at c34 l1) (at c35 l0) (at c37 l1) (at c38 l1) (at c39 l1) (at c4 l1) (at c40 l1) (at c41 l1) (at c42 l1) (at c43 l1) (at c44 l0) (at c45 l1) (at c46 l1) (at c47 l1) (at c48 l1) (at c49 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l0) (not-eq l0 l1) (not-eq l1 l0) (on c36))\n    (:goal (and (at c0 l0) (at c1 l1) (at c2 l0) (at c3 l1) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l0) (at c9 l0) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l1) (at c36 l0) (at c37 l0) (at c38 l0) (at c39 l1) (at c40 l0) (at c41 l1) (at c42 l1) (at c43 l0) (at c44 l0) (at c45 l0) (at c46 l0) (at c47 l1) (at c48 l1) (at c49 l1)))\n)"}
{"id": 2862334935069292606, "group": "reachable_atom_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c23, c39, c47, c17, c31, c32, c37, c10, c3, c1, c28, c5, c41, c40, c45, c42, c15, c38, c27, c16, c46, c34, c4, c48, c43, c18, c24, c49, and c7 are at l1; c21, c44, c35, c26, c22, c6, c20, c36, c12, c8, c33, c25, c0, c30, c14, c13, c9, c29, c2, c19, and c11 are at l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Car ?c is on board the ferry.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l1) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c2 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c3 l1) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l0) (at c34 l1) (at c35 l0) (at c36 l0) (at c37 l1) (at c38 l1) (at c39 l1) (at c4 l1) (at c40 l1) (at c41 l1) (at c42 l1) (at c43 l1) (at c44 l0) (at c45 l1) (at c46 l1) (at c47 l1) (at c48 l1) (at c49 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l1) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l0) (at c1 l1) (at c2 l0) (at c3 l1) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l0) (at c9 l0) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l1) (at c36 l0) (at c37 l0) (at c38 l0) (at c39 l1) (at c40 l0) (at c41 l1) (at c42 l1) (at c43 l0) (at c44 l0) (at c45 l0) (at c46 l0) (at c47 l1) (at c48 l1) (at c49 l1)))\n)"}
{"id": -2471722473374551561, "group": "reachable_atom_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2, with the car c3 on board. The cars are at locations as follows: c1, c0, c5, c8, and c7 are at l1; c9, c2, and c4 are at l0; c6 is at l2. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Car ?c is on board the ferry.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l3-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 l2 - location)\n    (:init (at c0 l1) (at c1 l1) (at c2 l0) (at c4 l0) (at c5 l1) (at c6 l2) (at c7 l1) (at c8 l1) (at c9 l0) (at-ferry l2) (not-eq l0 l1) (not-eq l0 l2) (not-eq l1 l0) (not-eq l1 l2) (not-eq l2 l0) (not-eq l2 l1) (on c3))\n    (:goal (and (at c0 l2) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l2) (at c6 l2) (at c7 l0) (at c8 l2) (at c9 l0)))\n)"}
{"id": 4097158681046824302, "group": "reachable_atom_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c1, c5, c7, and c2 are at l1; c9, c6, and c4 are at l0; c8, c3, and c0 are at l2. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Ferry has car ?c on board.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l3-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 l2 - location)\n    (:init (at c0 l2) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l2) (at c9 l0) (at-ferry l1) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l1 l0) (not-eq l1 l2) (not-eq l2 l0) (not-eq l2 l1))\n    (:goal (and (at c0 l2) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l2) (at c6 l2) (at c7 l0) (at c8 l2) (at c9 l0)))\n)"}
{"id": 6900855040701022305, "group": "reachable_atom_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 5 cars, numbered consecutively. \nCurrently, the ferry is at l0, with the car c2 on board. The cars are at locations as follows: c3, c0, and c1 are at l0; c4 is at l1. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Ferry has car ?c on board.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c5)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c3 l0) (at c4 l1) (at-ferry l0) (not-eq l0 l1) (not-eq l1 l0) (on c2))\n    (:goal (and (at c0 l1) (at c1 l0) (at c2 l1) (at c3 l0) (at c4 l0)))\n)"}
{"id": 8157519345490314959, "group": "reachable_atom_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 10 cars, numbered consecutively. \nCurrently, the ferry is at l0, with the car c0 on board. The cars are at locations as follows: c9, c1, c6, c4, c8, and c5 are at l0; c7, c3, and c2 are at l1. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Car ?c is on board the ferry.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l0) (at c5 l0) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l0) (not-eq l0 l1) (not-eq l1 l0) (on c0))\n    (:goal (and (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l0) (at c5 l0) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l1)))\n)"}
{"id": -3661081222919015750, "group": "reachable_atom_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 10 cars, numbered consecutively. \nCurrently, the ferry is at l1, with the car c4 on board. The cars are at locations as follows: c9, c3, and c2 are at l1; c0, c1, c6, c7, c8, and c5 are at l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Ferry has car ?c on board.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l1) (at c5 l0) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l1) (at-ferry l1) (not-eq l0 l1) (not-eq l1 l0) (on c4))\n    (:goal (and (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l0) (at c5 l0) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l1)))\n)"}
{"id": -8254532353890980213, "group": "reachable_atom_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 10 cars, numbered consecutively. \nCurrently, the ferry is at l0, with the car c8 on board. The cars are at locations as follows: c9, c3, and c2 are at l1; c0, c1, c6, c7, c4, and c5 are at l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Car ?c is on the ferry.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l0) (at c5 l0) (at c6 l0) (at c7 l0) (at c9 l1) (at-ferry l0) (not-eq l0 l1) (not-eq l1 l0) (on c8))\n    (:goal (and (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l0) (at c5 l0) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l1)))\n)"}
{"id": -7238402034302168409, "group": "reachable_atom_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 10 cars, numbered consecutively. \nCurrently, the ferry is at l0, with the car c4 on board. The cars are at locations as follows: c9, c3, and c2 are at l1; c0, c1, c6, c7, c8, and c5 are at l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Ferry has car ?c on board.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l1) (at c5 l0) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l1) (at-ferry l0) (not-eq l0 l1) (not-eq l1 l0) (on c4))\n    (:goal (and (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l0) (at c5 l0) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l1)))\n)"}
