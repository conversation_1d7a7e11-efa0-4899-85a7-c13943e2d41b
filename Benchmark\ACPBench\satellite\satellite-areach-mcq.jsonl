{"id": -7817342779877117274, "group": "reachable_action_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation3, star2, phenomenon6, star0, planet5, groundstation4, groundstation1. There are 3 image mode(s): infrared1, thermograph2, spectrograph0. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite2 has following instruments onboard: instrument4. Satellite satellite1 has following instruments onboard: instrument3. Instrument instrument4 supports image of mode infrared1 and its calibration target is star2. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to groundstation3. Satellite satellite2 is pointing to planet5. Satellite satellite1 is pointing to groundstation3. Power is available on the following satellite(s): satellite1. Following instruments are powered on: instrument2, instrument4. Following instruments are calibrated: instrument4. A spectrograph0 mode image of target phenomenon6 is available. ", "question": "Which of the following actions can eventually be applied? A. turn instrument instrument2 on satellite satellite0 in direction star0. B. move cargo on satellite satellite2 from star2 to groundstation1. C. turn the satellite satellite1 to the direction star0 from the direction groundstation4. D. move cargo on satellite satellite0 from phenomenon6 to star2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["turn instrument instrument2 on satellite satellite0 in direction star0", "move cargo on satellite satellite2 from star2 to groundstation1", "turn the satellite satellite1 to the direction star0 from the direction groundstation4", "move cargo on satellite satellite0 from phenomenon6 to star2"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 7511944160542481652, "group": "reachable_action_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation3, groundstation0, star4, planet5, star2, planet6, groundstation1. There are 3 image mode(s): infrared2, thermograph0, image1. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode infrared2 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation3. Following instruments are powered on: instrument0. Following instruments are calibrated: instrument0. A thermograph0 mode image of target planet6 is available. A infrared2 mode image of target planet6 is available. ", "question": "Which of the following actions can eventually be applied? A. transfer weight on satellite satellite0 from section planet5 to section groundstation0. B. transfer weight on satellite satellite0 from section star4 to section star2. C. capture an image in direction star4 in mode infrared2 using the instrument instrument0 on the satellite satellite0. D. turn instrument instrument0 on satellite satellite0 in direction star2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["transfer weight on satellite satellite0 from section planet5 to section groundstation0", "transfer weight on satellite satellite0 from section star4 to section star2", "capture an image in direction star4 in mode infrared2 using the instrument instrument0 on the satellite satellite0", "turn instrument instrument0 on satellite satellite0 in direction star2"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 8394254463183242922, "group": "reachable_action_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation3, planet7, star10, planet8, star9, groundstation1, groundstation4, groundstation6, star0, star2, groundstation5. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 11 instrument(s), numbered consecutively.  Satellite satellite5 has following instruments onboard: instrument10, instrument9. Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite2 has following instruments onboard: instrument5, instrument4, instrument3. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument2. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument4 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument9 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument6 supports image of mode infrared1 and its calibration target is star0. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument2 supports image of mode infrared1 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to star10. Satellite satellite4 is pointing to star10. Satellite satellite1 is pointing to planet7. Satellite satellite5 is pointing to star9. Satellite satellite3 is pointing to star9. Satellite satellite2 is pointing to star0. Power is available on the following satellite(s): satellite3, satellite4, satellite1, satellite2, satellite5. Following instruments are powered on: instrument0. ", "question": "Which of the following actions can eventually be applied? A. change the direction of the satellite satellite0 from star0 to groundstation5. B. transfer weight on satellite satellite0 from section planet8 to section star0. C. transfer weight on satellite satellite4 from section groundstation5 to section planet7. D. transfer weight on satellite satellite0 from section groundstation3 to section star10.", "choices": {"label": ["A", "B", "C", "D"], "text": ["change the direction of the satellite satellite0 from star0 to groundstation5", "transfer weight on satellite satellite0 from section planet8 to section star0", "transfer weight on satellite satellite4 from section groundstation5 to section planet7", "transfer weight on satellite satellite0 from section groundstation3 to section star10"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -41648535468020841, "group": "reachable_action_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, star6, phenomenon5, star3, star1, groundstation4, groundstation2. There are 3 image mode(s): image2, image0, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument4, instrument5, instrument3. Satellite satellite6 has following instruments onboard: instrument15, instrument17, instrument16. Satellite satellite4 has following instruments onboard: instrument9, instrument11, instrument10. Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite5 has following instruments onboard: instrument14, instrument13, instrument12. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument11 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument3 supports image of mode image2 and its calibration target is star3. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument2 supports image of mode image2 and its calibration target is star1. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument5 supports image of mode image2 and its calibration target is star3. Instrument instrument10 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite6 is pointing to groundstation0. Satellite satellite5 is pointing to groundstation0. Satellite satellite2 is pointing to star6. Satellite satellite0 is pointing to groundstation2. Satellite satellite3 is pointing to star6. Satellite satellite1 is pointing to phenomenon5. Satellite satellite4 is pointing to star1. Power is available on the following satellite(s): satellite0, satellite6, satellite3, satellite4, satellite1, satellite5. Following instruments are powered on: instrument6. Following instruments are calibrated: instrument6. A image0 mode image of target phenomenon5 is available. ", "question": "Which of the following actions can eventually be applied? A. move cargo on satellite satellite5 from star6 to groundstation2. B. move cargo on satellite satellite1 from groundstation0 to star6. C. turn instrument instrument6 on satellite satellite2 in direction star6. D. direct the satellite satellite2 to point in the direction star3 instead of star1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move cargo on satellite satellite5 from star6 to groundstation2", "move cargo on satellite satellite1 from groundstation0 to star6", "turn instrument instrument6 on satellite satellite2 in direction star6", "direct the satellite satellite2 to point in the direction star3 instead of star1"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -2096029908515956907, "group": "reachable_action_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation3, planet7, star10, planet8, star9, groundstation1, groundstation4, groundstation6, star0, star2, groundstation5. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 11 instrument(s), numbered consecutively.  Satellite satellite5 has following instruments onboard: instrument10, instrument9. Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite2 has following instruments onboard: instrument5, instrument4, instrument3. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument2. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument4 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument9 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument6 supports image of mode infrared1 and its calibration target is star0. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument2 supports image of mode infrared1 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to star10. Satellite satellite4 is pointing to groundstation5. Satellite satellite1 is pointing to planet8. Satellite satellite3 is pointing to star9. Satellite satellite2 is pointing to star0. Satellite satellite5 is pointing to groundstation4. Power is available on the following satellite(s): satellite0, satellite3, satellite4, satellite2, satellite5. Following instruments are powered on: instrument2. Following instruments are calibrated: instrument2. A infrared1 mode image of target star10 is available. A infrared1 mode image of target planet8 is available. ", "question": "Which of the following actions can eventually be applied? A. point the satellite satellite3 to direction star0 instead of star10. B. transfer weight on satellite satellite0 from section groundstation3 to section groundstation5. C. install the camera instrument0 on the satellite satellite0 to face direction star0. D. transfer weight on satellite satellite4 from section planet8 to section groundstation1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["point the satellite satellite3 to direction star0 instead of star10", "transfer weight on satellite satellite0 from section groundstation3 to section groundstation5", "install the camera instrument0 on the satellite satellite0 to face direction star0", "transfer weight on satellite satellite4 from section planet8 to section groundstation1"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 8796916095734866221, "group": "reachable_action_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation3, groundstation0, star4, planet5, star2, planet6, groundstation1. There are 3 image mode(s): infrared2, thermograph0, image1. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode infrared2 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to planet5. Following instruments are powered on: instrument0. Following instruments are calibrated: instrument0. A thermograph0 mode image of target planet5 is available. A thermograph0 mode image of target groundstation3 is available. ", "question": "Which of the following actions can eventually be applied? A. change the direction of the satellite satellite0 from star4 to star2. B. move cargo on satellite satellite0 from planet6 to planet5. C. fire laser in direction groundstation3 in mode thermograph0 using the instrument instrument0 on satellite satellite0. D. move cargo on satellite satellite0 from star4 to groundstation0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["change the direction of the satellite satellite0 from star4 to star2", "move cargo on satellite satellite0 from planet6 to planet5", "fire laser in direction groundstation3 in mode thermograph0 using the instrument instrument0 on satellite satellite0", "move cargo on satellite satellite0 from star4 to groundstation0"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -5345401931712996754, "group": "reachable_action_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, star6, phenomenon5, star3, star1, groundstation4, groundstation2. There are 3 image mode(s): image2, image0, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument4, instrument5, instrument3. Satellite satellite6 has following instruments onboard: instrument15, instrument17, instrument16. Satellite satellite4 has following instruments onboard: instrument9, instrument11, instrument10. Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite5 has following instruments onboard: instrument14, instrument13, instrument12. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument11 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument3 supports image of mode image2 and its calibration target is star3. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument2 supports image of mode image2 and its calibration target is star1. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument5 supports image of mode image2 and its calibration target is star3. Instrument instrument10 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite5 is pointing to groundstation0. Satellite satellite3 is pointing to star6. Satellite satellite0 is pointing to groundstation2. Satellite satellite2 is pointing to groundstation2. Satellite satellite1 is pointing to phenomenon5. Satellite satellite4 is pointing to phenomenon5. Satellite satellite6 is pointing to groundstation0. Power is available on the following satellite(s): satellite0, satellite6, satellite3, satellite4, satellite1, satellite5. Following instruments are powered on: instrument6. Following instruments are calibrated: instrument6. ", "question": "Which of the following actions can eventually be applied? A. turn instrument instrument10 on satellite satellite4 in direction groundstation0. B. turn the satellite satellite3 to the direction groundstation0 from the direction phenomenon5. C. turn instrument instrument2 on satellite satellite0 in direction star3. D. turn instrument instrument16 on satellite satellite6 in direction groundstation0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["turn instrument instrument10 on satellite satellite4 in direction groundstation0", "turn the satellite satellite3 to the direction groundstation0 from the direction phenomenon5", "turn instrument instrument2 on satellite satellite0 in direction star3", "turn instrument instrument16 on satellite satellite6 in direction groundstation0"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -3455087545663791697, "group": "reachable_action_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation3, planet7, star10, planet8, star9, groundstation1, groundstation4, groundstation6, star0, star2, groundstation5. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 11 instrument(s), numbered consecutively.  Satellite satellite5 has following instruments onboard: instrument10, instrument9. Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite2 has following instruments onboard: instrument5, instrument4, instrument3. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument2. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument4 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument9 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument6 supports image of mode infrared1 and its calibration target is star0. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument2 supports image of mode infrared1 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to star10. Satellite satellite4 is pointing to star10. Satellite satellite1 is pointing to groundstation1. Satellite satellite5 is pointing to groundstation5. Satellite satellite3 is pointing to star9. Satellite satellite2 is pointing to star0. Power is available on the following satellite(s): satellite0, satellite4, satellite1, satellite2, satellite5. Following instruments are powered on: instrument6. ", "question": "Which of the following actions can eventually be applied? A. move cargo on satellite satellite5 from star9 to star2. B. move cargo on satellite satellite3 from planet8 to star0. C. turn instrument instrument5 on satellite satellite2 in direction planet8. D. turn the satellite satellite5 from direction star2 to direction star0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move cargo on satellite satellite5 from star9 to star2", "move cargo on satellite satellite3 from planet8 to star0", "turn instrument instrument5 on satellite satellite2 in direction planet8", "turn the satellite satellite5 from direction star2 to direction star0"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 4055420724985207862, "group": "reachable_action_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, star6, phenomenon5, star3, star1, groundstation4, groundstation2. There are 3 image mode(s): image2, image0, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument4, instrument5, instrument3. Satellite satellite6 has following instruments onboard: instrument15, instrument17, instrument16. Satellite satellite4 has following instruments onboard: instrument9, instrument11, instrument10. Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite5 has following instruments onboard: instrument14, instrument13, instrument12. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument11 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument3 supports image of mode image2 and its calibration target is star3. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument2 supports image of mode image2 and its calibration target is star1. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument5 supports image of mode image2 and its calibration target is star3. Instrument instrument10 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite6 is pointing to groundstation0. Satellite satellite2 is pointing to phenomenon5. Satellite satellite5 is pointing to phenomenon5. Satellite satellite3 is pointing to star6. Satellite satellite0 is pointing to groundstation2. Satellite satellite1 is pointing to phenomenon5. Satellite satellite4 is pointing to star1. Power is available on the following satellite(s): satellite0, satellite6, satellite3, satellite4, satellite5. Following instruments are powered on: instrument5, instrument6. Following instruments are calibrated: instrument6. ", "question": "Which of the following actions can eventually be applied? A. turn the satellite satellite6 to the direction phenomenon5 from the direction star3. B. transfer weight on satellite satellite3 from section phenomenon5 to section star1. C. transfer weight on satellite satellite0 from section star1 to section groundstation0. D. transfer weight on satellite satellite5 from section star6 to section star6.", "choices": {"label": ["A", "B", "C", "D"], "text": ["turn the satellite satellite6 to the direction phenomenon5 from the direction star3", "transfer weight on satellite satellite3 from section phenomenon5 to section star1", "transfer weight on satellite satellite0 from section star1 to section groundstation0", "transfer weight on satellite satellite5 from section star6 to section star6"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 4778183966846948507, "group": "reachable_action_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, star6, phenomenon5, star3, star1, groundstation4, groundstation2. There are 3 image mode(s): image2, image0, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument4, instrument5, instrument3. Satellite satellite6 has following instruments onboard: instrument15, instrument17, instrument16. Satellite satellite4 has following instruments onboard: instrument9, instrument11, instrument10. Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite5 has following instruments onboard: instrument14, instrument13, instrument12. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument11 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument3 supports image of mode image2 and its calibration target is star3. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument2 supports image of mode image2 and its calibration target is star1. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument5 supports image of mode image2 and its calibration target is star3. Instrument instrument10 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite5 is pointing to groundstation0. Satellite satellite4 is pointing to groundstation0. Satellite satellite0 is pointing to groundstation2. Satellite satellite2 is pointing to groundstation2. Satellite satellite1 is pointing to phenomenon5. Satellite satellite3 is pointing to groundstation0. Satellite satellite6 is pointing to star6. Power is available on the following satellite(s): satellite0, satellite3, satellite1, satellite2, satellite4, satellite5. Following instruments are powered on: instrument16. Following instruments are calibrated: instrument16. A image1 mode image of target star6 is available. ", "question": "Which of the following actions can eventually be applied? A. fire laser in direction groundstation4 in mode image1 using the instrument instrument17 on satellite satellite6. B. fire laser in direction star3 in mode image2 using the instrument instrument15 on satellite satellite6. C. transfer weight on satellite satellite5 from section groundstation2 to section groundstation4. D. turn satellite satellite0 to point from groundstation4 direction to star3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["fire laser in direction groundstation4 in mode image1 using the instrument instrument17 on satellite satellite6", "fire laser in direction star3 in mode image2 using the instrument instrument15 on satellite satellite6", "transfer weight on satellite satellite5 from section groundstation2 to section groundstation4", "turn satellite satellite0 to point from groundstation4 direction to star3"]}, "query": "Which action is reachable from this state?", "answer": "D"}
