{"id": -285455876356794953, "group": "reachable_atom_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x3-y1.The following places have been visited: loc-x3-y2, loc-x0-y3, loc-x0-y0, loc-x1-y0, loc-x1-y2, loc-x3-y1, loc-x3-y0, loc-x0-y1, loc-x1-y3, loc-x0-y2, loc-x2-y2, loc-x3-y3, and loc-x2-y3. The available propositions are: (at-robot ?x) - The robot is in place ?x and (visited ?x) - Place ?x is visited.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-0-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x3-y1) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": 4680192896960286016, "group": "reachable_atom_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y3, loc-x3-y1, loc-x1-y0, and loc-x0-y2. Currently, the robot is in place loc-x2-y3.The following places have been visited: loc-x3-y2, loc-x2-y0, loc-x3-y0, loc-x2-y2, loc-x1-y3, loc-x2-y3, and loc-x2-y1. The available propositions are: (at-robot ?x) - The robot is in place ?x and (visited ?x) - Place ?x is visited.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-4-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y3 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 - place)\n    (:init (at-robot loc-x2-y3) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y3) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2)))\n)"}
{"id": -5647598152817857187, "group": "reachable_atom_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x1-y2.The following places have been visited: loc-x1-y1, loc-x3-y2, loc-x2-y0, loc-x0-y0, loc-x1-y0, loc-x1-y2, loc-x3-y0, loc-x0-y1, loc-x2-y2, loc-x3-y1, loc-x3-y3, loc-x2-y3, and loc-x2-y1. The available propositions are: (at-robot ?x) - The robot is at ?x and (visited ?x) - Place ?x is visited.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-0-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x1-y2) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": 2007577425164113017, "group": "reachable_atom_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x3-y2.The following places have been visited: loc-x3-y2, loc-x2-y0, loc-x3-y0, loc-x2-y2, loc-x2-y3, and loc-x2-y1. The available propositions are: (at-robot ?x) - The robot is at ?x and (visited ?x) - Place ?x is visited.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-2-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x3-y2) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": -1602058418226314297, "group": "reachable_atom_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x2-y3.The following places have been visited: loc-x3-y2, loc-x2-y0, loc-x1-y2, loc-x3-y0, loc-x1-y3, loc-x2-y2, loc-x2-y3, and loc-x2-y1. The available propositions are: (at-robot ?x) - The robot is in place ?x and (visited ?x) - Place ?x is visited.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-2-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x2-y3) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": -927313541282204058, "group": "reachable_atom_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x2-y0.The following places have been visited: loc-x1-y1, loc-x3-y2, loc-x2-y0, loc-x0-y3, loc-x0-y0, loc-x3-y0, loc-x0-y1, loc-x1-y3, loc-x0-y2, loc-x2-y2, loc-x3-y3, loc-x2-y3, and loc-x2-y1. The available propositions are: (at-robot ?x) - The robot is at ?x and (visited ?x) - Place ?x is visited.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-2-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x2-y0) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y1) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2) (visited loc-x3-y3))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": -2409174130482315954, "group": "reachable_atom_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x2-y0.The following places have been visited: loc-x1-y0 and loc-x2-y0. The available propositions are: (at-robot ?x) - The robot is in place ?x and (visited ?x) - Place ?x is visited.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-0-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x2-y0) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x1-y0) (visited loc-x2-y0))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": -4856367921259659009, "group": "reachable_atom_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x3-y0.The following places have been visited: loc-x3-y2, loc-x0-y3, loc-x0-y0, loc-x1-y0, loc-x3-y0, loc-x0-y1, loc-x1-y3, loc-x0-y2, loc-x3-y1, loc-x3-y3, and loc-x2-y3. The available propositions are: (at-robot ?x) - The robot is in place ?x and (visited ?x) - Place ?x is visited.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-0-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x3-y0) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y3) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": -8432329560296454738, "group": "reachable_atom_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x2-y1.The following places have been visited: loc-x1-y1, loc-x3-y2, loc-x2-y0, loc-x0-y3, loc-x0-y0, loc-x1-y2, loc-x3-y0, loc-x0-y1, loc-x1-y3, loc-x0-y2, loc-x2-y2, loc-x3-y3, and loc-x2-y1. The available propositions are: (at-robot ?x) - The robot is at ?x and (visited ?x) - Place ?x is visited.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-2-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x2-y1) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x3-y0) (visited loc-x3-y2) (visited loc-x3-y3))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": -1616468456074243120, "group": "reachable_atom_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y3, loc-x3-y1, loc-x1-y0, and loc-x0-y2. Currently, the robot is in place loc-x2-y1.The following places have been visited: loc-x1-y1, loc-x0-y1, and loc-x2-y1. The available propositions are: (at-robot ?x) - The robot is at ?x and (visited ?x) - Place ?x is visited.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-4-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y3 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 - place)\n    (:init (at-robot loc-x2-y1) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (visited loc-x0-y1) (visited loc-x1-y1) (visited loc-x2-y1))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y3) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2)))\n)"}
{"id": 8399841765701321513, "group": "reachable_atom_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The only unavailable cell is loc-x0-y0. \nCurrently, the robot is in place loc-x3-y3.Place loc-x3-y3 has been visited. The available propositions are: (at-robot ?x) - The robot is at ?x and (visited ?x) - Place ?x is visited.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-1-2020)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x3-y3) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x3-y3))\n    (:goal (and (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": 8090810606399617291, "group": "reachable_atom_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y3, loc-x3-y3, and loc-x0-y0. \nCurrently, the robot is in place loc-x1-y3.The following places have been visited: loc-x1-y1, loc-x3-y2, loc-x2-y0, loc-x1-y0, loc-x3-y0, loc-x1-y3, loc-x2-y2, loc-x3-y1, loc-x2-y3, and loc-x2-y1. The available propositions are: (at-robot ?x) - The robot is at ?x and (visited ?x) - Place ?x is visited.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-3-2020)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y1 loc-x0-y2 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 - place)\n    (:init (at-robot loc-x1-y3) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x1-y2) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2))\n    (:goal (and (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2)))\n)"}
{"id": -5632945655800586221, "group": "reachable_atom_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. There are no unavailable cells. \nCurrently, the robot is in place loc-x1-y2.The following places have been visited: loc-x1-y1, loc-x3-y2, loc-x2-y0, loc-x0-y0, loc-x1-y0, loc-x1-y2, loc-x3-y1, loc-x3-y0, loc-x0-y1, loc-x1-y3, loc-x0-y3, loc-x2-y2, loc-x3-y3, loc-x2-y3, and loc-x2-y1. The available propositions are: (at-robot ?x) - The robot is at ?x and (visited ?x) - Place ?x is visited.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-0-2020)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x1-y2) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": -3702367751643574541, "group": "reachable_atom_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. There are no unavailable cells. \nCurrently, the robot is in place loc-x2-y2.The following places have been visited: loc-x1-y1, loc-x0-y3, loc-x1-y2, loc-x0-y1, loc-x1-y3, loc-x0-y2, loc-x2-y2, and loc-x2-y1. The available propositions are: (at-robot ?x) - The robot is at ?x and (visited ?x) - Place ?x is visited.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-0-2020)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x2-y2) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y1) (visited loc-x2-y2))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": -6102569867477223794, "group": "reachable_atom_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The only unavailable cell is loc-x3-y1. \nCurrently, the robot is in place loc-x3-y2.The following places have been visited: loc-x3-y2, loc-x0-y3, loc-x1-y3, loc-x3-y3, and loc-x2-y3. The available propositions are: (at-robot ?x) - The robot is at ?x and (visited ?x) - Place ?x is visited.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-1-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x3-y2) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x0-y3) (visited loc-x1-y3) (visited loc-x2-y3) (visited loc-x3-y2) (visited loc-x3-y3))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
