{"id": -989245294227982764, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f0-4f and its arm is empty. All the positions are open except the following: f2-1f has shape1 shaped lock, f3-4f has shape1 shaped lock. Key key1-0 is at position f1-2f. Key key1-1 is at position f3-0f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Key key1-0 is at f0-4f location and Key key1-0 is at f1-1f location. B. Robot is at f0-2f location. C. Location f2-3f is locked. D. Robot is at shape0 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key1-0 is at f0-4f location and Key key1-0 is at f1-1f location", "Robot is at f0-2f location", "Location f2-3f is locked", "Robot is at shape0 location"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 8596659750862324339, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f0-2f and its arm is empty. All the positions are open except the following: f0-1f has shape0 shaped lock. Key key0-0 is at position f3-1f. Key key0-1 is at position f4-1f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Location f2-4f is locked. B. Robot is at f3-2f location and Robot is at f3-1f location. C. Robot is at f0-4f location. D. Location f4-3f is open and Location f2-3f is locked.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Location f2-4f is locked", "Robot is at f3-2f location and Robot is at f3-1f location", "Robot is at f0-4f location", "Location f4-3f is open and Location f2-3f is locked"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 8365064581165078754, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f4-1f and its arm is empty. All the positions are open except the following: f2-1f has shape0 shaped lock, f0-1f has shape0 shaped lock. Key key0-0 is at position f3-1f. Key key0-1 is at position f3-1f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Key f1-3f is at f4-1f location. B. Location f4-1f is locked. C. Robot is holding key0-0 and Robot is at f3-1f location. D. Key key0-1 is at f1-4f location and Robot is holding key0-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key f1-3f is at f4-1f location", "Location f4-1f is locked", "Robot is holding key0-0 and Robot is at f3-1f location", "Key key0-1 is at f1-4f location and Robot is holding key0-1"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -9079134825059331541, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f0-2f and its arm is empty. All the positions are open except the following: f2-0f has shape0 shaped lock, f4-2f has shape0 shaped lock. Key key0-0 is at position f1-3f. Key key0-1 is at position f1-2f. Key key0-2 is at position f3-3f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot is at f0-4f location. B. Location f2-3f is locked. C. Robot is holding key0-1 and Robot is holding key0-2. D. Robot is at key0-1 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot is at f0-4f location", "Location f2-3f is locked", "Robot is holding key0-1 and Robot is holding key0-2", "Robot is at key0-1 location"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -7789321838638536209, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f4-4f and its arm is empty. All the positions are open except the following: f2-1f has shape1 shaped lock. Key key1-1 is at position f4-4f. Key key1-0 is at position f1-0f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Location f2-0f is open and Location f2-3f is locked. B. Location key1-0 is open. C. Robot is at f3-3f location. D. Robot is holding key1-0 and Robot's arm is empty.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Location f2-0f is open and Location f2-3f is locked", "Location key1-0 is open", "Robot is at f3-3f location", "Robot is holding key1-0 and Robot's arm is empty"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -3967746676511258343, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f1-3f and is holding key0-2. All the positions are open except the following: f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock, f4-4f has shape0 shaped lock. Key key0-1 is at position f1-2f. Key key0-0 is at position f0-1f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Key key0-1 is at f0-2f location and Key key0-1 is at f2-0f location. B. Location f3-2f is locked. C. Robot is at f0-4f location. D. Location shape0 is locked.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-1 is at f0-2f location and Key key0-1 is at f2-0f location", "Location f3-2f is locked", "Robot is at f0-4f location", "Location shape0 is locked"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -4583866225020199652, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-1 is of shape shape0, Key key0-3 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f4-1f and is holding key0-3. All the positions are open except the following: f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock, f0-2f has shape0 shaped lock, f0-0f has shape0 shaped lock. Key key0-0 is at position f0-0f. Key key0-1 is at position f4-2f. Key key0-2 is at position f3-0f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot is at f4-2f location and Key key0-3 is at f4-2f location. B. Location f3-1f is open and Location f1-1f is locked. C. Key key0-0 is at f0-4f location and Key key0-0 is at f2-0f location. D. Location f4-4f is locked.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot is at f4-2f location and Key key0-3 is at f4-2f location", "Location f3-1f is open and Location f1-1f is locked", "Key key0-0 is at f0-4f location and Key key0-0 is at f2-0f location", "Location f4-4f is locked"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -2068590970820042144, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-1 is of shape shape0, Key key0-3 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f2-4f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f0-2f has shape0 shaped lock. Key key0-2 is at position f4-0f. Key key0-3 is at position f0-1f. Key key0-1 is at position f4-2f. Key key0-0 is at position f0-1f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot is at f4-4f location. B. Location f1-1f is locked. C. Robot is at f4-0f location and Robot is at f4-2f location. D. Location f2-0f is locked.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot is at f4-4f location", "Location f1-1f is locked", "Robot is at f4-0f location and Robot is at f4-2f location", "Location f2-0f is locked"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 8066425899228182667, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f2-4f and is holding key0-0. All the positions are open except the following: f2-0f has shape0 shaped lock, f4-2f has shape0 shaped lock. Key key0-1 is at position f2-2f. Key key0-2 is at position f0-4f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Key key0-0 is at f2-4f location and Robot is at f3-4f location. B. Location f2-4f is locked and Location f4-1f is open. C. Robot is at f4-1f location and Robot is at f1-0f location. D. Location f2-3f is locked.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-0 is at f2-4f location and Robot is at f3-4f location", "Location f2-4f is locked and Location f4-1f is open", "Robot is at f4-1f location and Robot is at f1-0f location", "Location f2-3f is locked"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -4387882018975386720, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f1-2f and is holding key0-0. All the positions are open except the following: f2-1f has shape0 shaped lock, f0-1f has shape0 shaped lock. Key key0-1 is at position f4-1f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Location f4-0f is locked. B. Robot is at f1-1f location and Robot's arm is empty. C. Key key0-0 is at f2-1f location and Key key0-0 is at f3-0f location. D. Robot is at shape0 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Location f4-0f is locked", "Robot is at f1-1f location and Robot's arm is empty", "Key key0-0 is at f2-1f location and Key key0-0 is at f3-0f location", "Robot is at shape0 location"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 3722695615200984129, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f1-3f and its arm is empty. All the positions are open except the following: f2-1f has shape0 shaped lock, f0-1f has shape0 shaped lock, f2-2f has shape0 shaped lock. Key key0-1 is at position f2-4f. Key key0-0 is at position f1-0f. Key key0-2 is at position f1-3f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Location f3-2f is locked. B. Key f4-1f is at f4-1f location. C. Robot is at f2-4f location. D. Robot is at f2-1f location and Robot is at f0-0f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Location f3-2f is locked", "Key f4-1f is at f4-1f location", "Robot is at f2-4f location", "Robot is at f2-1f location and Robot is at f0-0f location"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 7150444647310033162, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f3-3f and its arm is empty. All the positions are open except the following: f4-0f has shape0 shaped lock. Key key0-0 is at position f4-4f. Key key0-1 is at position f3-2f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Key key0-1 is at f2-0f location and Key key0-1 is at f1-0f location. B. Robot is at f1-3f location. C. Location f0-0f is open and Location f3-2f is locked. D. Location f2-1f is open and Location key0-1 is open.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-1 is at f2-0f location and Key key0-1 is at f1-0f location", "Robot is at f1-3f location", "Location f0-0f is open and Location f3-2f is locked", "Location f2-1f is open and Location key0-1 is open"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 2961316145726088840, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f1-4f and is holding key0-0. All the positions are open except the following: f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-1 is at position f4-2f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Key key0-0 is at f2-4f location and Robot is holding key0-0. B. Robot is holding shape0. C. Location f0-0f is locked. D. Robot is at f2-3f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-0 is at f2-4f location and Robot is holding key0-0", "Robot is holding shape0", "Location f0-0f is locked", "Robot is at f2-3f location"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 935628270425025889, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f3-1f and is holding key0-0. All the positions are open except the following: f4-2f has shape0 shaped lock. Key key0-1 is at position f1-3f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Key f1-2f is at f1-3f location and Location f2-0f is open. B. Robot is at f4-1f location and Robot is not holding anything. C. Robot is at f2-4f location and Robot is at f1-3f location. D. Location f0-4f is locked.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key f1-2f is at f1-3f location and Location f2-0f is open", "Robot is at f4-1f location and Robot is not holding anything", "Robot is at f2-4f location and Robot is at f1-3f location", "Location f0-4f is locked"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -4562131633541256367, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f1-1f and its arm is empty. All the positions are open except the following: f2-1f has shape0 shaped lock, f0-1f has shape0 shaped lock, f2-2f has shape0 shaped lock. Key key0-1 is at position f2-4f. Key key0-2 is at position f1-2f. Key key0-0 is at position f2-0f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Key key0-1 is at f4-0f location and Key key0-1 is at f0-0f location. B. Robot is at f2-0f location. C. Location f3-1f is open and Robot is at key0-2 location. D. Location f4-0f is locked and Location f2-0f is open.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-1 is at f4-0f location and Key key0-1 is at f0-0f location", "Robot is at f2-0f location", "Location f3-1f is open and Robot is at key0-2 location", "Location f4-0f is locked and Location f2-0f is open"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
