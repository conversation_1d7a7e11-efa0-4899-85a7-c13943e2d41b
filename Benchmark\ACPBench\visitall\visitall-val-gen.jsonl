{"id": 5125834266482188479, "group": "validation_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x2-y3, loc-x0-y4, and loc-x1-y2. Currently, the robot is in place loc-x0-y2.Place loc-x0-y2 has been visited. The goal is to reach a state where the following facts hold: Place loc-x0-y2 is visited, Place loc-x1-y3 is visited, Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x0-y1 is visited, Place loc-x3-y1 is visited, Place loc-x3-y3 is visited, Place loc-x3-y4 is visited, Place loc-x1-y4 is visited, Place loc-x3-y2 is visited, Place loc-x0-y3 is visited, Place loc-x2-y2 is visited, Place loc-x2-y4 is visited, Place loc-x3-y0 is visited, Place loc-x0-y0 is visited, Place loc-x2-y1 is visited, and Place loc-x1-y0 is visited. The available actions are: (move ?curpos ?nextpos) - navigate from ?curpos to ?nextpos.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move loc-x0-y2 loc-x0-y3) (move loc-x0-y3 loc-x1-y3) (move loc-x1-y3 loc-x1-y4) (move loc-x1-y4 loc-x2-y4) (move loc-x2-y4 loc-x3-y4) (move loc-x3-y4 loc-x3-y3) (move loc-x3-y0 loc-x2-y0) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y1) (move loc-x2-y1 loc-x2-y2) (move loc-x2-y2 loc-x2-y1) (move loc-x2-y1 loc-x3-y1) (move loc-x3-y1 loc-x3-y0) (move loc-x3-y0 loc-x2-y0) (move loc-x2-y0 loc-x1-y0) (move loc-x1-y0 loc-x1-y1) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y0)\"?", "answer": 6, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-5-3-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y3 loc-x1-y4 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y4 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 loc-x3-y4 - place)\n    (:init (at-robot loc-x0-y2) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y4) (connected loc-x1-y4 loc-x1-y3) (connected loc-x1-y4 loc-x2-y4) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y4 loc-x1-y4) (connected loc-x2-y4 loc-x3-y4) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x3-y2) (connected loc-x3-y3 loc-x3-y4) (connected loc-x3-y4 loc-x2-y4) (connected loc-x3-y4 loc-x3-y3) (visited loc-x0-y2))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y3) (visited loc-x1-y4) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y4) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3) (visited loc-x3-y4)))\n)"}
{"id": 3977526191403039740, "group": "validation_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x3-y0.Place loc-x3-y0 has been visited. The goal is to reach a state where the following facts hold: Place loc-x0-y2 is visited, Place loc-x1-y3 is visited, Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x0-y1 is visited, Place loc-x3-y3 is visited, Place loc-x1-y2 is visited, Place loc-x3-y2 is visited, Place loc-x0-y3 is visited, Place loc-x2-y3 is visited, Place loc-x2-y2 is visited, Place loc-x3-y0 is visited, Place loc-x0-y0 is visited, and Place loc-x2-y1 is visited. The available actions are: (move ?curpos ?nextpos) - Please move to ?nextpos position from ?curpos position.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move loc-x3-y0 loc-x2-y0) (move loc-x2-y0 loc-x3-y0) (move loc-x3-y0 loc-x2-y0) (move loc-x2-y0 loc-x2-y1) (move loc-x3-y0 loc-x2-y0) (move loc-x2-y2 loc-x3-y2) (move loc-x3-y2 loc-x3-y3) (move loc-x3-y3 loc-x2-y3) (move loc-x2-y3 loc-x1-y3) (move loc-x1-y3 loc-x0-y3) (move loc-x0-y3 loc-x0-y2) (move loc-x0-y2 loc-x1-y2) (move loc-x1-y2 loc-x1-y1) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y0)\"?", "answer": 4, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-2-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x3-y0) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x3-y0))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": -6493220978406622760, "group": "validation_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x0-y2.Place loc-x0-y2 has been visited. The goal is to reach a state where the following facts hold: Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x0-y1 is visited, Place loc-x0-y4 is visited, Place loc-x3-y0 is visited, Place loc-x0-y0 is visited, Place loc-x3-y1 is visited, Place loc-x2-y2 is visited, Place loc-x2-y4 is visited, Place loc-x0-y2 is visited, Place loc-x3-y3 is visited, Place loc-x3-y4 is visited, Place loc-x0-y3 is visited, Place loc-x1-y0 is visited, Place loc-x1-y3 is visited, Place loc-x1-y2 is visited, Place loc-x3-y2 is visited, Place loc-x1-y4 is visited, and Place loc-x2-y1 is visited. The available actions are: (move ?curpos ?nextpos) - move from place ?curpos to place ?nextpos.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move loc-x0-y2 loc-x0-y3) (move loc-x0-y3 loc-x0-y4) (move loc-x0-y4 loc-x1-y4) (move loc-x1-y4 loc-x1-y3) (move loc-x1-y3 loc-x1-y2) (move loc-x1-y2 loc-x1-y1) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y0) (move loc-x0-y0 loc-x1-y0) (move loc-x1-y0 loc-x2-y0) (move loc-x2-y0 loc-x3-y0) (move loc-x3-y0 loc-x3-y1) (move loc-x0-y1 loc-x0-y0) (move loc-x2-y1 loc-x2-y2) (move loc-x2-y2 loc-x3-y2) (move loc-x3-y2 loc-x3-y3) (move loc-x3-y3 loc-x3-y4) (move loc-x3-y4 loc-x2-y4) (move loc-x2-y4 loc-x1-y4)\"?", "answer": 12, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-5-1-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x0-y4 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x1-y4 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y4 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 loc-x3-y4 - place)\n    (:init (at-robot loc-x0-y2) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x0-y4) (connected loc-x0-y3 loc-x1-y3) (connected loc-x0-y4 loc-x0-y3) (connected loc-x0-y4 loc-x1-y4) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x1-y4) (connected loc-x1-y4 loc-x0-y4) (connected loc-x1-y4 loc-x1-y3) (connected loc-x1-y4 loc-x2-y4) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y4 loc-x1-y4) (connected loc-x2-y4 loc-x3-y4) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x3-y2) (connected loc-x3-y3 loc-x3-y4) (connected loc-x3-y4 loc-x2-y4) (connected loc-x3-y4 loc-x3-y3) (visited loc-x0-y2))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x0-y4) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x1-y4) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y4) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3) (visited loc-x3-y4)))\n)"}
{"id": -6132786567168723036, "group": "validation_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x2-y3, loc-x0-y4, and loc-x1-y2. Currently, the robot is in place loc-x0-y2.Place loc-x0-y2 has been visited. The goal is to reach a state where the following facts hold: Place loc-x0-y2 is visited, Place loc-x1-y3 is visited, Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x0-y1 is visited, Place loc-x3-y1 is visited, Place loc-x3-y3 is visited, Place loc-x3-y4 is visited, Place loc-x1-y4 is visited, Place loc-x3-y2 is visited, Place loc-x0-y3 is visited, Place loc-x2-y2 is visited, Place loc-x2-y4 is visited, Place loc-x3-y0 is visited, Place loc-x0-y0 is visited, Place loc-x2-y1 is visited, and Place loc-x1-y0 is visited. The available actions are: (move ?curpos ?nextpos) - travel from ?curpos to ?nextpos.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move loc-x0-y2 loc-x0-y1) (move loc-x0-y1 loc-x0-y0) (move loc-x0-y0 loc-x1-y0) (move loc-x1-y0 loc-x2-y0) (move loc-x2-y0 loc-x3-y0) (move loc-x3-y0 loc-x3-y1) (move loc-x3-y1 loc-x3-y2) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y1) (move loc-x0-y0 loc-x1-y0) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y2) (move loc-x0-y2 loc-x0-y3) (move loc-x0-y3 loc-x1-y3) (move loc-x1-y3 loc-x1-y4) (move loc-x1-y4 loc-x2-y4) (move loc-x2-y4 loc-x3-y4) (move loc-x3-y4 loc-x3-y3)\"?", "answer": 9, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-5-3-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y3 loc-x1-y4 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y4 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 loc-x3-y4 - place)\n    (:init (at-robot loc-x0-y2) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y4) (connected loc-x1-y4 loc-x1-y3) (connected loc-x1-y4 loc-x2-y4) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y4 loc-x1-y4) (connected loc-x2-y4 loc-x3-y4) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x3-y2) (connected loc-x3-y3 loc-x3-y4) (connected loc-x3-y4 loc-x2-y4) (connected loc-x3-y4 loc-x3-y3) (visited loc-x0-y2))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y3) (visited loc-x1-y4) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y4) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3) (visited loc-x3-y4)))\n)"}
{"id": -1667610423974238675, "group": "validation_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x1-y0.Place loc-x1-y0 has been visited. The goal is to reach a state where the following facts hold: Place loc-x0-y2 is visited, Place loc-x1-y3 is visited, Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x0-y1 is visited, Place loc-x3-y1 is visited, Place loc-x3-y3 is visited, Place loc-x1-y2 is visited, Place loc-x3-y2 is visited, Place loc-x0-y3 is visited, Place loc-x2-y3 is visited, Place loc-x2-y2 is visited, Place loc-x3-y0 is visited, Place loc-x0-y0 is visited, Place loc-x2-y1 is visited, and Place loc-x1-y0 is visited. The available actions are: (move ?curpos ?nextpos) - move to place ?nextpos from place ?curpos.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move loc-x3-y3 loc-x2-y3) (move loc-x0-y0 loc-x0-y1) (move loc-x0-y1 loc-x1-y1) (move loc-x1-y1 loc-x1-y0) (move loc-x1-y0 loc-x2-y0) (move loc-x2-y0 loc-x3-y0) (move loc-x3-y0 loc-x3-y1) (move loc-x3-y1 loc-x3-y2) (move loc-x3-y2 loc-x3-y3) (move loc-x3-y3 loc-x2-y3) (move loc-x2-y3 loc-x1-y3) (move loc-x1-y3 loc-x0-y3) (move loc-x0-y3 loc-x0-y2) (move loc-x0-y2 loc-x1-y2) (move loc-x1-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y1)\"?", "answer": 0, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-0-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x1-y0) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x1-y0))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": 2106774141284844319, "group": "validation_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1, loc-x3-y3, loc-x1-y0, and loc-x0-y2. Currently, the robot is in place loc-x2-y1.Place loc-x2-y1 has been visited. The goal is to reach a state where the following facts hold: Place loc-x1-y3 is visited, Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x0-y1 is visited, Place loc-x1-y2 is visited, Place loc-x3-y2 is visited, Place loc-x0-y3 is visited, Place loc-x2-y3 is visited, Place loc-x2-y2 is visited, Place loc-x3-y0 is visited, Place loc-x0-y0 is visited, and Place loc-x2-y1 is visited. The available actions are: (move ?curpos ?nextpos) - travel from the current position ?curpos to the next position ?nextpos.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move loc-x2-y1 loc-x2-y0) (move loc-x2-y0 loc-x3-y0) (move loc-x3-y0 loc-x2-y0) (move loc-x2-y3 loc-x2-y2) (move loc-x2-y1 loc-x2-y2) (move loc-x2-y2 loc-x3-y2) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y3) (move loc-x2-y3 loc-x2-y2) (move loc-x2-y2 loc-x1-y2) (move loc-x1-y2 loc-x1-y3) (move loc-x1-y3 loc-x0-y3) (move loc-x0-y3 loc-x1-y3) (move loc-x1-y3 loc-x1-y2) (move loc-x1-y2 loc-x1-y1) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y0) (move loc-x0-y0 loc-x0-y1)\"?", "answer": 3, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-4-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y3 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 - place)\n    (:init (at-robot loc-x2-y1) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (visited loc-x2-y1))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y3) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2)))\n)"}
{"id": 7958024478646155330, "group": "validation_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x0-y2.Place loc-x0-y2 has been visited. The goal is to reach a state where the following facts hold: Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x0-y1 is visited, Place loc-x0-y4 is visited, Place loc-x3-y0 is visited, Place loc-x0-y0 is visited, Place loc-x3-y1 is visited, Place loc-x2-y2 is visited, Place loc-x2-y4 is visited, Place loc-x0-y2 is visited, Place loc-x3-y3 is visited, Place loc-x3-y4 is visited, Place loc-x0-y3 is visited, Place loc-x1-y0 is visited, Place loc-x1-y3 is visited, Place loc-x1-y2 is visited, Place loc-x3-y2 is visited, Place loc-x1-y4 is visited, and Place loc-x2-y1 is visited. The available actions are: (move ?curpos ?nextpos) - transition from the current position ?curpos to the next position ?nextpos.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move loc-x1-y1 loc-x1-y0) (move loc-x1-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y1) (move loc-x2-y1 loc-x1-y1) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y0) (move loc-x0-y0 loc-x1-y0) (move loc-x1-y0 loc-x2-y0) (move loc-x2-y0 loc-x3-y0) (move loc-x3-y0 loc-x3-y1) (move loc-x3-y1 loc-x3-y2) (move loc-x3-y2 loc-x3-y3) (move loc-x3-y3 loc-x3-y4) (move loc-x3-y4 loc-x2-y4) (move loc-x2-y4 loc-x1-y4) (move loc-x1-y4 loc-x0-y4) (move loc-x0-y4 loc-x0-y3) (move loc-x0-y3 loc-x1-y3) (move loc-x1-y3 loc-x1-y2)\"?", "answer": 0, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-5-1-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x0-y4 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x1-y4 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y4 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 loc-x3-y4 - place)\n    (:init (at-robot loc-x0-y2) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x0-y4) (connected loc-x0-y3 loc-x1-y3) (connected loc-x0-y4 loc-x0-y3) (connected loc-x0-y4 loc-x1-y4) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x1-y4) (connected loc-x1-y4 loc-x0-y4) (connected loc-x1-y4 loc-x1-y3) (connected loc-x1-y4 loc-x2-y4) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y4 loc-x1-y4) (connected loc-x2-y4 loc-x3-y4) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x3-y2) (connected loc-x3-y3 loc-x3-y4) (connected loc-x3-y4 loc-x2-y4) (connected loc-x3-y4 loc-x3-y3) (visited loc-x0-y2))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x0-y4) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x1-y4) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y4) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3) (visited loc-x3-y4)))\n)"}
{"id": -5059074452021990552, "group": "validation_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1, loc-x3-y3, loc-x1-y0, and loc-x0-y2. Currently, the robot is in place loc-x2-y1.Place loc-x2-y1 has been visited. The goal is to reach a state where the following facts hold: Place loc-x1-y3 is visited, Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x0-y1 is visited, Place loc-x1-y2 is visited, Place loc-x3-y2 is visited, Place loc-x0-y3 is visited, Place loc-x2-y3 is visited, Place loc-x2-y2 is visited, Place loc-x3-y0 is visited, Place loc-x0-y0 is visited, and Place loc-x2-y1 is visited. The available actions are: (move ?curpos ?nextpos) - move from ?curpos to ?nextpos.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move loc-x2-y1 loc-x2-y0) (move loc-x2-y0 loc-x3-y0) (move loc-x3-y0 loc-x2-y0) (move loc-x2-y0 loc-x2-y1) (move loc-x2-y1 loc-x2-y2) (move loc-x2-y2 loc-x3-y2) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y3) (move loc-x2-y3 loc-x1-y3) (move loc-x1-y3 loc-x0-y3) (move loc-x0-y3 loc-x1-y3) (move loc-x1-y2 loc-x1-y3) (move loc-x1-y2 loc-x2-y2) (move loc-x2-y2 loc-x1-y2) (move loc-x1-y2 loc-x1-y1) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y0) (move loc-x0-y0 loc-x0-y1) (move loc-x0-y1 loc-x1-y1)\"?", "answer": 11, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-4-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y3 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 - place)\n    (:init (at-robot loc-x2-y1) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (visited loc-x2-y1))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y3) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2)))\n)"}
{"id": -3467183166396758184, "group": "validation_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x3-y0.Place loc-x3-y0 has been visited. The goal is to reach a state where the following facts hold: Place loc-x0-y2 is visited, Place loc-x1-y3 is visited, Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x0-y1 is visited, Place loc-x3-y3 is visited, Place loc-x1-y2 is visited, Place loc-x3-y2 is visited, Place loc-x0-y3 is visited, Place loc-x2-y3 is visited, Place loc-x2-y2 is visited, Place loc-x3-y0 is visited, Place loc-x0-y0 is visited, and Place loc-x2-y1 is visited. The available actions are: (move ?curpos ?nextpos) - Please move to ?nextpos position from ?curpos position.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move loc-x3-y0 loc-x2-y0) (move loc-x2-y0 loc-x2-y1) (move loc-x2-y1 loc-x1-y1) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y0) (move loc-x0-y0 loc-x0-y1) (move loc-x1-y1 loc-x1-y2) (move loc-x0-y2 loc-x0-y3) (move loc-x0-y3 loc-x1-y3) (move loc-x1-y3 loc-x1-y2) (move loc-x1-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y3) (move loc-x2-y3 loc-x2-y2) (move loc-x2-y2 loc-x3-y2) (move loc-x3-y2 loc-x3-y3) (move loc-x3-y3 loc-x3-y2)\"?", "answer": 6, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-2-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x3-y0) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x3-y0))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": 4355002892141720746, "group": "validation_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x2-y3, loc-x0-y4, and loc-x1-y2. Currently, the robot is in place loc-x0-y2.Place loc-x0-y2 has been visited. The goal is to reach a state where the following facts hold: Place loc-x0-y2 is visited, Place loc-x1-y3 is visited, Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x0-y1 is visited, Place loc-x3-y1 is visited, Place loc-x3-y3 is visited, Place loc-x3-y4 is visited, Place loc-x1-y4 is visited, Place loc-x3-y2 is visited, Place loc-x0-y3 is visited, Place loc-x2-y2 is visited, Place loc-x2-y4 is visited, Place loc-x3-y0 is visited, Place loc-x0-y0 is visited, Place loc-x2-y1 is visited, and Place loc-x1-y0 is visited. The available actions are: (move ?curpos ?nextpos) - move from ?curpos to ?nextpos.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move loc-x0-y2 loc-x0-y1) (move loc-x0-y1 loc-x0-y0) (move loc-x0-y0 loc-x0-y1) (move loc-x0-y1 loc-x0-y2) (move loc-x0-y2 loc-x0-y3) (move loc-x0-y3 loc-x1-y3) (move loc-x1-y3 loc-x1-y4) (move loc-x1-y4 loc-x2-y4) (move loc-x2-y4 loc-x3-y4) (move loc-x3-y4 loc-x3-y3) (move loc-x1-y3 loc-x1-y4) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y1) (move loc-x2-y1 loc-x1-y1) (move loc-x1-y1 loc-x1-y0) (move loc-x1-y0 loc-x2-y0) (move loc-x2-y0 loc-x3-y0) (move loc-x3-y0 loc-x3-y1)\"?", "answer": 10, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-5-3-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y3 loc-x1-y4 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y4 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 loc-x3-y4 - place)\n    (:init (at-robot loc-x0-y2) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y4) (connected loc-x1-y4 loc-x1-y3) (connected loc-x1-y4 loc-x2-y4) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y4 loc-x1-y4) (connected loc-x2-y4 loc-x3-y4) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x3-y2) (connected loc-x3-y3 loc-x3-y4) (connected loc-x3-y4 loc-x2-y4) (connected loc-x3-y4 loc-x3-y3) (visited loc-x0-y2))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y3) (visited loc-x1-y4) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y4) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3) (visited loc-x3-y4)))\n)"}
{"id": 6168951149728630819, "group": "validation_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y3, loc-x0-y0, and loc-x0-y3. \nCurrently, the robot is in place loc-x3-y2.Place loc-x3-y2 has been visited. The goal is to reach a state where the following facts hold: Place loc-x0-y2 is visited, Place loc-x1-y3 is visited, Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x0-y1 is visited, Place loc-x3-y1 is visited, Place loc-x1-y2 is visited, Place loc-x3-y2 is visited, Place loc-x2-y3 is visited, Place loc-x2-y2 is visited, Place loc-x3-y0 is visited, Place loc-x2-y1 is visited, and Place loc-x1-y0 is visited. The available actions are: (move ?curpos ?nextpos) - go to ?nextpos from ?curpos.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y1) (move loc-x2-y1 loc-x2-y2) (move loc-x2-y2 loc-x3-y2) (move loc-x3-y2 loc-x3-y1) (move loc-x3-y1 loc-x3-y0) (move loc-x3-y0 loc-x2-y0) (move loc-x2-y0 loc-x1-y0) (move loc-x3-y0 loc-x3-y1) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y2) (move loc-x0-y2 loc-x1-y2) (move loc-x1-y2 loc-x1-y3) (move loc-x1-y3 loc-x2-y3)\"?", "answer": 8, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-3-2020)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y1 loc-x0-y2 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 - place)\n    (:init (at-robot loc-x3-y2) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x1-y2) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (visited loc-x3-y2))\n    (:goal (and (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2)))\n)"}
{"id": -6860106509883848266, "group": "validation_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The only unavailable cell is loc-x0-y0. \nCurrently, the robot is in place loc-x3-y3.Place loc-x3-y3 has been visited. The goal is to reach a state where the following facts hold: Place loc-x0-y2 is visited, Place loc-x1-y3 is visited, Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x0-y1 is visited, Place loc-x3-y1 is visited, Place loc-x3-y3 is visited, Place loc-x1-y2 is visited, Place loc-x3-y2 is visited, Place loc-x0-y3 is visited, Place loc-x2-y3 is visited, Place loc-x2-y2 is visited, Place loc-x3-y0 is visited, Place loc-x2-y1 is visited, and Place loc-x1-y0 is visited. The available actions are: (move ?curpos ?nextpos) - move from place ?curpos to place ?nextpos.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move loc-x3-y3 loc-x3-y2) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x1-y2) (move loc-x1-y2 loc-x1-y1) (move loc-x1-y1 loc-x1-y0) (move loc-x1-y0 loc-x2-y0) (move loc-x2-y0 loc-x3-y0) (move loc-x0-y2 loc-x0-y3) (move loc-x3-y1 loc-x2-y1) (move loc-x2-y1 loc-x1-y1) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y2) (move loc-x0-y2 loc-x0-y3) (move loc-x0-y3 loc-x1-y3) (move loc-x1-y3 loc-x2-y3)\"?", "answer": 7, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-1-2020)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x3-y3) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x3-y3))\n    (:goal (and (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": 712407344306479888, "group": "validation_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The only unavailable cell is loc-x3-y1. \nCurrently, the robot is in place loc-x0-y3.Place loc-x0-y3 has been visited. The goal is to reach a state where the following facts hold: Place loc-x0-y2 is visited, Place loc-x1-y3 is visited, Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x0-y1 is visited, Place loc-x3-y3 is visited, Place loc-x1-y2 is visited, Place loc-x3-y2 is visited, Place loc-x0-y3 is visited, Place loc-x2-y3 is visited, Place loc-x2-y2 is visited, Place loc-x3-y0 is visited, Place loc-x0-y0 is visited, Place loc-x2-y1 is visited, and Place loc-x1-y0 is visited. The available actions are: (move ?curpos ?nextpos) - move to place ?nextpos from place ?curpos.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move loc-x0-y3 loc-x1-y3) (move loc-x1-y3 loc-x2-y3) (move loc-x2-y3 loc-x3-y3) (move loc-x3-y3 loc-x3-y2) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x1-y2) (move loc-x1-y2 loc-x0-y2) (move loc-x0-y2 loc-x1-y2) (move loc-x1-y2 loc-x1-y1) (move loc-x1-y1 loc-x0-y1) (move loc-x0-y1 loc-x0-y0) (move loc-x0-y0 loc-x1-y0) (move loc-x1-y0 loc-x1-y1) (move loc-x1-y1 loc-x2-y1) (move loc-x2-y1 loc-x2-y0) (move loc-x1-y0 loc-x0-y0)\"?", "answer": 15, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-1-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x0-y3) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x0-y3))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": -4346974138748284013, "group": "validation_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y0 and loc-x0-y3. \nCurrently, the robot is in place loc-x3-y3.Place loc-x3-y3 has been visited. The goal is to reach a state where the following facts hold: Place loc-x0-y2 is visited, Place loc-x1-y3 is visited, Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x0-y1 is visited, Place loc-x3-y1 is visited, Place loc-x3-y3 is visited, Place loc-x1-y2 is visited, Place loc-x3-y2 is visited, Place loc-x2-y3 is visited, Place loc-x2-y2 is visited, Place loc-x3-y0 is visited, Place loc-x2-y1 is visited, and Place loc-x1-y0 is visited. The available actions are: (move ?curpos ?nextpos) - move from ?curpos to ?nextpos.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move loc-x3-y3 loc-x3-y2) (move loc-x3-y2 loc-x2-y2) (move loc-x2-y2 loc-x2-y3) (move loc-x2-y1 loc-x3-y1) (move loc-x1-y3 loc-x1-y2) (move loc-x1-y2 loc-x0-y2) (move loc-x0-y2 loc-x0-y1) (move loc-x0-y1 loc-x1-y1) (move loc-x1-y1 loc-x1-y0) (move loc-x1-y0 loc-x1-y1) (move loc-x1-y1 loc-x2-y1) (move loc-x2-y1 loc-x3-y1) (move loc-x3-y1 loc-x3-y0) (move loc-x3-y0 loc-x2-y0)\"?", "answer": 3, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-2-2020)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y1 loc-x0-y2 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x3-y3) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x1-y2) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x3-y3))\n    (:goal (and (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": -7762072371428479018, "group": "validation_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y0 and loc-x0-y3. \nCurrently, the robot is in place loc-x3-y3.Place loc-x3-y3 has been visited. The goal is to reach a state where the following facts hold: Place loc-x0-y2 is visited, Place loc-x1-y3 is visited, Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x0-y1 is visited, Place loc-x3-y1 is visited, Place loc-x3-y3 is visited, Place loc-x1-y2 is visited, Place loc-x3-y2 is visited, Place loc-x2-y3 is visited, Place loc-x2-y2 is visited, Place loc-x3-y0 is visited, Place loc-x2-y1 is visited, and Place loc-x1-y0 is visited. The available actions are: (move ?curpos ?nextpos) - Please move to ?nextpos position from ?curpos position.", "question": "What is the first inapplicable action in the next sequence of actions: \"(move loc-x3-y3 loc-x2-y3) (move loc-x2-y3 loc-x2-y2) (move loc-x2-y2 loc-x3-y2) (move loc-x3-y2 loc-x3-y1) (move loc-x3-y1 loc-x3-y0) (move loc-x3-y0 loc-x2-y0) (move loc-x2-y0 loc-x1-y0) (move loc-x1-y0 loc-x1-y1) (move loc-x2-y0 loc-x2-y1) (move loc-x2-y1 loc-x2-y2) (move loc-x2-y2 loc-x2-y3) (move loc-x2-y3 loc-x1-y3) (move loc-x1-y3 loc-x1-y2) (move loc-x1-y2 loc-x0-y2) (move loc-x0-y2 loc-x0-y1)\"?", "answer": 8, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-2-2020)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y1 loc-x0-y2 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x3-y3) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x1-y2) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x3-y3))\n    (:goal (and (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
