{"id": -2946746452105794113, "group": "action_justification_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 4 agents: steve, bob, vic, and zoe. There are 4 items/roles: book03, book04, book01, and book02. Currently, steve is assigned book02, zoe is assigned book04, bob is assigned book01, and vic is assigned book03. The goal is to reach a state where the following facts hold: vic is assigned book04, steve is assigned book03, zoe is assigned book01, and bob is assigned book02.", "question": "Given the plan: \"trade book02 of steve for book01 of bob, trade book01 of steve for book02 of bob, trade book01 of bob for book02 of steve, trade book04 of zoe for book02 of bob, trade book04 of bob for book03 of vic, trade book03 of bob for book02 of zoe, trade book03 of zoe for book01 of steve\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. trade book04 of zoe for book02 of bob and trade book04 of bob for book03 of vic. B. trade book03 of bob for book02 of zoe and trade book03 of zoe for book01 of steve. C. trade book04 of bob for book03 of vic and trade book03 of bob for book02 of zoe. D. trade book01 of steve for book02 of bob and trade book01 of bob for book02 of steve.", "choices": {"label": ["A", "B", "C", "D"], "text": ["trade book04 of zoe for book02 of bob and trade book04 of bob for book03 of vic", "trade book03 of bob for book02 of zoe and trade book03 of zoe for book01 of steve", "trade book04 of bob for book03 of vic and trade book03 of bob for book02 of zoe", "trade book01 of steve for book02 of bob and trade book01 of bob for book02 of steve"]}, "query": "Given the plan: \"trade book02 of steve for book01 of bob, trade book01 of steve for book02 of bob, trade book01 of bob for book02 of steve, trade book04 of zoe for book02 of bob, trade book04 of bob for book03 of vic, trade book03 of bob for book02 of zoe, trade book03 of zoe for book01 of steve\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
{"id": 4783844411715665015, "group": "action_justification_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: heidi, xena, bob, alice, kevin, ted, and dave. There are 7 items/roles: leek, mushroom, yam, parsnip, ulluco, valerian, and quince. Currently, alice is assigned yam, heidi is assigned quince, ted is assigned ulluco, kevin is assigned mushroom, xena is assigned parsnip, dave is assigned valerian, and bob is assigned leek. The goal is to reach a state where the following facts hold: bob is assigned parsnip, xena is assigned quince, dave is assigned ulluco, ted is assigned leek, kevin is assigned yam, heidi is assigned mushroom, and alice is assigned valerian.", "question": "Given the plan: \"swap xena:parsnip with bob:leek, swap xena:leek with bob:parsnip, swap heidi:quince with xena:parsnip, swap heidi:parsnip with ted:ulluco, swap bob:leek with ted:parsnip, swap alice:yam with heidi:ulluco, swap heidi:yam with kevin:mushroom, swap alice:ulluco with dave:valerian\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. swap xena:leek with bob:parsnip and swap heidi:quince with xena:parsnip. B. swap heidi:parsnip with ted:ulluco and swap bob:leek with ted:parsnip. C. swap xena:parsnip with bob:leek and swap xena:leek with bob:parsnip. D. swap heidi:quince with xena:parsnip and swap heidi:parsnip with ted:ulluco.", "choices": {"label": ["A", "B", "C", "D"], "text": ["swap xena:leek with bob:parsnip and swap heidi:quince with xena:parsnip", "swap heidi:parsnip with ted:ulluco and swap bob:leek with ted:parsnip", "swap xena:parsnip with bob:leek and swap xena:leek with bob:parsnip", "swap heidi:quince with xena:parsnip and swap heidi:parsnip with ted:ulluco"]}, "query": "Given the plan: \"swap xena:parsnip with bob:leek, swap xena:leek with bob:parsnip, swap heidi:quince with xena:parsnip, swap heidi:parsnip with ted:ulluco, swap bob:leek with ted:parsnip, swap alice:yam with heidi:ulluco, swap heidi:yam with kevin:mushroom, swap alice:ulluco with dave:valerian\"; which pair of consecutive actions can be removed from this plan?", "answer": "C"}
{"id": 2386443726430708227, "group": "action_justification_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: xena, bob, quentin, vic, frank, and liam. There are 6 items/roles: wrench, pliers, knead, ratchet, sander, and nibbler. Currently, liam is assigned knead, bob is assigned wrench, xena is assigned pliers, vic is assigned nibbler, frank is assigned sander, and quentin is assigned ratchet. The goal is to reach a state where the following facts hold: bob is assigned nibbler, quentin is assigned knead, xena is assigned wrench, frank is assigned pliers, liam is assigned sander, and vic is assigned ratchet.", "question": "Given the plan: \"swap liam with quentin, knead for ratchet, swap liam with quentin, ratchet for knead, swap frank with quentin, sander for ratchet, swap quentin with liam, sander for knead, swap frank with vic, ratchet for nibbler, swap bob with xena, wrench for pliers, swap frank with bob, nibbler for pliers\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. swap frank with vic, ratchet for nibbler and swap bob with xena, wrench for pliers. B. swap quentin with liam, sander for knead and swap frank with vic, ratchet for nibbler. C. swap bob with xena, wrench for pliers and swap frank with bob, nibbler for pliers. D. swap liam with quentin, knead for ratchet and swap liam with quentin, ratchet for knead.", "choices": {"label": ["A", "B", "C", "D"], "text": ["swap frank with vic, ratchet for nibbler and swap bob with xena, wrench for pliers", "swap quentin with liam, sander for knead and swap frank with vic, ratchet for nibbler", "swap bob with xena, wrench for pliers and swap frank with bob, nibbler for pliers", "swap liam with quentin, knead for ratchet and swap liam with quentin, ratchet for knead"]}, "query": "Given the plan: \"swap liam with quentin, knead for ratchet, swap liam with quentin, ratchet for knead, swap frank with quentin, sander for ratchet, swap quentin with liam, sander for knead, swap frank with vic, ratchet for nibbler, swap bob with xena, wrench for pliers, swap frank with bob, nibbler for pliers\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
{"id": 31814599546558794, "group": "action_justification_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: heidi, xena, bob, alice, kevin, ted, and dave. There are 7 items/roles: leek, mushroom, yam, parsnip, ulluco, valerian, and quince. Currently, alice is assigned yam, heidi is assigned quince, ted is assigned ulluco, kevin is assigned mushroom, xena is assigned parsnip, dave is assigned valerian, and bob is assigned leek. The goal is to reach a state where the following facts hold: bob is assigned parsnip, xena is assigned quince, dave is assigned ulluco, ted is assigned leek, kevin is assigned yam, heidi is assigned mushroom, and alice is assigned valerian.", "question": "Given the plan: \"exchange parsnip of xena with leek of bob, exchange leek of xena with parsnip of bob, exchange valerian of dave with yam of alice, exchange parsnip of xena with mushroom of kevin, exchange leek of bob with ulluco of ted, exchange quince of heidi with mushroom of xena, exchange yam of dave with parsnip of kevin, exchange ulluco of bob with parsnip of dave\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. exchange valerian of dave with yam of alice and exchange parsnip of xena with mushroom of kevin. B. exchange parsnip of xena with leek of bob and exchange leek of xena with parsnip of bob. C. exchange leek of xena with parsnip of bob and exchange valerian of dave with yam of alice. D. exchange leek of bob with ulluco of ted and exchange quince of heidi with mushroom of xena.", "choices": {"label": ["A", "B", "C", "D"], "text": ["exchange valerian of dave with yam of alice and exchange parsnip of xena with mushroom of kevin", "exchange parsnip of xena with leek of bob and exchange leek of xena with parsnip of bob", "exchange leek of xena with parsnip of bob and exchange valerian of dave with yam of alice", "exchange leek of bob with ulluco of ted and exchange quince of heidi with mushroom of xena"]}, "query": "Given the plan: \"exchange parsnip of xena with leek of bob, exchange leek of xena with parsnip of bob, exchange valerian of dave with yam of alice, exchange parsnip of xena with mushroom of kevin, exchange leek of bob with ulluco of ted, exchange quince of heidi with mushroom of xena, exchange yam of dave with parsnip of kevin, exchange ulluco of bob with parsnip of dave\"; which pair of consecutive actions can be removed from this plan?", "answer": "B"}
{"id": -8921323183973813048, "group": "action_justification_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: carol, zoe, heidi, xena, michelle, vic, alice, and dave. There are 8 items/roles: whale, zebra, guitar, slinky, frisbee, iceskates, quadcopter, and necklace. Currently, heidi is assigned necklace, dave is assigned slinky, vic is assigned quadcopter, zoe is assigned frisbee, carol is assigned guitar, xena is assigned whale, alice is assigned iceskates, and michelle is assigned zebra. The goal is to reach a state where the following facts hold: vic is assigned necklace, michelle is assigned quadcopter, xena is assigned slinky, dave is assigned iceskates, alice is assigned zebra, heidi is assigned guitar, zoe is assigned whale, and carol is assigned frisbee.", "question": "Given the plan: \"swap alice with michelle, iceskates for zebra, swap alice with michelle, zebra for iceskates, swap xena with dave, whale for slinky, swap dave with alice, whale for iceskates, swap alice with zoe, whale for frisbee, swap carol with heidi, guitar for necklace, swap michelle with alice, zebra for frisbee, swap michelle with vic, frisbee for quadcopter, swap carol with vic, necklace for frisbee\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. swap dave with alice, whale for iceskates and swap alice with zoe, whale for frisbee. B. swap xena with dave, whale for slinky and swap dave with alice, whale for iceskates. C. swap alice with michelle, iceskates for zebra and swap alice with michelle, zebra for iceskates. D. swap michelle with alice, zebra for frisbee and swap michelle with vic, frisbee for quadcopter.", "choices": {"label": ["A", "B", "C", "D"], "text": ["swap dave with alice, whale for iceskates and swap alice with zoe, whale for frisbee", "swap xena with dave, whale for slinky and swap dave with alice, whale for iceskates", "swap alice with michelle, iceskates for zebra and swap alice with michelle, zebra for iceskates", "swap michelle with alice, zebra for frisbee and swap michelle with vic, frisbee for quadcopter"]}, "query": "Given the plan: \"swap alice with michelle, iceskates for zebra, swap alice with michelle, zebra for iceskates, swap xena with dave, whale for slinky, swap dave with alice, whale for iceskates, swap alice with zoe, whale for frisbee, swap carol with heidi, guitar for necklace, swap michelle with alice, zebra for frisbee, swap michelle with vic, frisbee for quadcopter, swap carol with vic, necklace for frisbee\"; which pair of consecutive actions can be removed from this plan?", "answer": "C"}
{"id": 4158185534986061187, "group": "action_justification_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: carol, zoe, heidi, xena, michelle, vic, alice, and dave. There are 8 items/roles: whale, zebra, guitar, slinky, frisbee, iceskates, quadcopter, and necklace. Currently, heidi is assigned necklace, dave is assigned slinky, vic is assigned quadcopter, zoe is assigned frisbee, carol is assigned guitar, xena is assigned whale, alice is assigned iceskates, and michelle is assigned zebra. The goal is to reach a state where the following facts hold: vic is assigned necklace, michelle is assigned quadcopter, xena is assigned slinky, dave is assigned iceskates, alice is assigned zebra, heidi is assigned guitar, zoe is assigned whale, and carol is assigned frisbee.", "question": "Given the plan: \"trade iceskates of alice for zebra of michelle, trade zebra of alice for iceskates of michelle, trade slinky of dave for iceskates of alice, trade whale of xena for slinky of alice, trade whale of alice for frisbee of zoe, trade guitar of carol for frisbee of alice, trade guitar of alice for zebra of michelle, trade necklace of heidi for quadcopter of vic, trade quadcopter of heidi for guitar of michelle\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. trade iceskates of alice for zebra of michelle and trade zebra of alice for iceskates of michelle. B. trade whale of xena for slinky of alice and trade whale of alice for frisbee of zoe. C. trade necklace of heidi for quadcopter of vic and trade quadcopter of heidi for guitar of michelle. D. trade whale of alice for frisbee of zoe and trade guitar of carol for frisbee of alice.", "choices": {"label": ["A", "B", "C", "D"], "text": ["trade iceskates of alice for zebra of michelle and trade zebra of alice for iceskates of michelle", "trade whale of xena for slinky of alice and trade whale of alice for frisbee of zoe", "trade necklace of heidi for quadcopter of vic and trade quadcopter of heidi for guitar of michelle", "trade whale of alice for frisbee of zoe and trade guitar of carol for frisbee of alice"]}, "query": "Given the plan: \"trade iceskates of alice for zebra of michelle, trade zebra of alice for iceskates of michelle, trade slinky of dave for iceskates of alice, trade whale of xena for slinky of alice, trade whale of alice for frisbee of zoe, trade guitar of carol for frisbee of alice, trade guitar of alice for zebra of michelle, trade necklace of heidi for quadcopter of vic, trade quadcopter of heidi for guitar of michelle\"; which pair of consecutive actions can be removed from this plan?", "answer": "A"}
{"id": 8651638380055942222, "group": "action_justification_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: heidi, xena, bob, alice, kevin, ted, and dave. There are 7 items/roles: leek, mushroom, yam, parsnip, ulluco, valerian, and quince. Currently, alice is assigned yam, heidi is assigned quince, ted is assigned ulluco, kevin is assigned mushroom, xena is assigned parsnip, dave is assigned valerian, and bob is assigned leek. The goal is to reach a state where the following facts hold: bob is assigned parsnip, xena is assigned quince, dave is assigned ulluco, ted is assigned leek, kevin is assigned yam, heidi is assigned mushroom, and alice is assigned valerian.", "question": "Given the plan: \"swap xena with bob, parsnip for leek, swap xena with bob, leek for parsnip, swap alice with dave, yam for valerian, swap kevin with xena, mushroom for parsnip, swap bob with ted, leek for ulluco, swap heidi with xena, quince for mushroom, swap dave with kevin, yam for parsnip, swap bob with dave, ulluco for parsnip\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. swap kevin with xena, mushroom for parsnip and swap bob with ted, leek for ulluco. B. swap xena with bob, parsnip for leek and swap xena with bob, leek for parsnip. C. swap alice with dave, yam for valerian and swap kevin with xena, mushroom for parsnip. D. swap heidi with xena, quince for mushroom and swap dave with kevin, yam for parsnip.", "choices": {"label": ["A", "B", "C", "D"], "text": ["swap kevin with xena, mushroom for parsnip and swap bob with ted, leek for ulluco", "swap xena with bob, parsnip for leek and swap xena with bob, leek for parsnip", "swap alice with dave, yam for valerian and swap kevin with xena, mushroom for parsnip", "swap heidi with xena, quince for mushroom and swap dave with kevin, yam for parsnip"]}, "query": "Given the plan: \"swap xena with bob, parsnip for leek, swap xena with bob, leek for parsnip, swap alice with dave, yam for valerian, swap kevin with xena, mushroom for parsnip, swap bob with ted, leek for ulluco, swap heidi with xena, quince for mushroom, swap dave with kevin, yam for parsnip, swap bob with dave, ulluco for parsnip\"; which pair of consecutive actions can be removed from this plan?", "answer": "B"}
{"id": -9085710616988911279, "group": "action_justification_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: carol, zoe, heidi, xena, michelle, vic, alice, and dave. There are 8 items/roles: whale, zebra, guitar, slinky, frisbee, iceskates, quadcopter, and necklace. Currently, heidi is assigned necklace, dave is assigned slinky, vic is assigned quadcopter, zoe is assigned frisbee, carol is assigned guitar, xena is assigned whale, alice is assigned iceskates, and michelle is assigned zebra. The goal is to reach a state where the following facts hold: vic is assigned necklace, michelle is assigned quadcopter, xena is assigned slinky, dave is assigned iceskates, alice is assigned zebra, heidi is assigned guitar, zoe is assigned whale, and carol is assigned frisbee.", "question": "Given the plan: \"trade iceskates of alice for zebra of michelle, trade zebra of alice for iceskates of michelle, trade necklace of heidi for quadcopter of vic, trade zebra of michelle for iceskates of alice, trade quadcopter of heidi for guitar of carol, trade quadcopter of carol for frisbee of zoe, trade slinky of dave for whale of xena, trade iceskates of michelle for quadcopter of zoe, trade whale of dave for iceskates of zoe\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. trade necklace of heidi for quadcopter of vic and trade zebra of michelle for iceskates of alice. B. trade quadcopter of heidi for guitar of carol and trade quadcopter of carol for frisbee of zoe. C. trade iceskates of michelle for quadcopter of zoe and trade whale of dave for iceskates of zoe. D. trade iceskates of alice for zebra of michelle and trade zebra of alice for iceskates of michelle.", "choices": {"label": ["A", "B", "C", "D"], "text": ["trade necklace of heidi for quadcopter of vic and trade zebra of michelle for iceskates of alice", "trade quadcopter of heidi for guitar of carol and trade quadcopter of carol for frisbee of zoe", "trade iceskates of michelle for quadcopter of zoe and trade whale of dave for iceskates of zoe", "trade iceskates of alice for zebra of michelle and trade zebra of alice for iceskates of michelle"]}, "query": "Given the plan: \"trade iceskates of alice for zebra of michelle, trade zebra of alice for iceskates of michelle, trade necklace of heidi for quadcopter of vic, trade zebra of michelle for iceskates of alice, trade quadcopter of heidi for guitar of carol, trade quadcopter of carol for frisbee of zoe, trade slinky of dave for whale of xena, trade iceskates of michelle for quadcopter of zoe, trade whale of dave for iceskates of zoe\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
{"id": -5526594134774079221, "group": "action_justification_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 4 agents: steve, bob, vic, and zoe. There are 4 items/roles: book03, book04, book01, and book02. Currently, steve is assigned book02, zoe is assigned book04, bob is assigned book01, and vic is assigned book03. The goal is to reach a state where the following facts hold: vic is assigned book04, steve is assigned book03, zoe is assigned book01, and bob is assigned book02.", "question": "Given the plan: \"swap steve with bob, book02 for book01, swap bob with steve, book02 for book01, swap vic with steve, book03 for book02, swap bob with zoe, book01 for book04, swap vic with bob, book02 for book04\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. swap bob with steve, book02 for book01 and swap vic with steve, book03 for book02. B. swap steve with bob, book02 for book01 and swap bob with steve, book02 for book01. C. swap bob with zoe, book01 for book04 and swap vic with bob, book02 for book04. D. swap vic with steve, book03 for book02 and swap bob with zoe, book01 for book04.", "choices": {"label": ["A", "B", "C", "D"], "text": ["swap bob with steve, book02 for book01 and swap vic with steve, book03 for book02", "swap steve with bob, book02 for book01 and swap bob with steve, book02 for book01", "swap bob with zoe, book01 for book04 and swap vic with bob, book02 for book04", "swap vic with steve, book03 for book02 and swap bob with zoe, book01 for book04"]}, "query": "Given the plan: \"swap steve with bob, book02 for book01, swap bob with steve, book02 for book01, swap vic with steve, book03 for book02, swap bob with zoe, book01 for book04, swap vic with bob, book02 for book04\"; which pair of consecutive actions can be removed from this plan?", "answer": "B"}
{"id": -2377099082917283675, "group": "action_justification_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 4 agents: steve, bob, vic, and zoe. There are 4 items/roles: book03, book04, book01, and book02. Currently, steve is assigned book02, zoe is assigned book04, bob is assigned book01, and vic is assigned book03. The goal is to reach a state where the following facts hold: vic is assigned book04, steve is assigned book03, zoe is assigned book01, and bob is assigned book02.", "question": "Given the plan: \"swap steve with bob, book02 for book01, swap bob with steve, book02 for book01, swap bob with zoe, book01 for book04, swap steve with bob, book02 for book04, swap vic with steve, book03 for book04\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. swap bob with steve, book02 for book01 and swap bob with zoe, book01 for book04. B. swap steve with bob, book02 for book01 and swap bob with steve, book02 for book01. C. swap bob with zoe, book01 for book04 and swap steve with bob, book02 for book04. D. swap steve with bob, book02 for book04 and swap vic with steve, book03 for book04.", "choices": {"label": ["A", "B", "C", "D"], "text": ["swap bob with steve, book02 for book01 and swap bob with zoe, book01 for book04", "swap steve with bob, book02 for book01 and swap bob with steve, book02 for book01", "swap bob with zoe, book01 for book04 and swap steve with bob, book02 for book04", "swap steve with bob, book02 for book04 and swap vic with steve, book03 for book04"]}, "query": "Given the plan: \"swap steve with bob, book02 for book01, swap bob with steve, book02 for book01, swap bob with zoe, book01 for book04, swap steve with bob, book02 for book04, swap vic with steve, book03 for book04\"; which pair of consecutive actions can be removed from this plan?", "answer": "B"}
