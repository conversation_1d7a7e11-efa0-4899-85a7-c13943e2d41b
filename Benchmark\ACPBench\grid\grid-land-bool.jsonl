{"id": 6597636164923934416, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f2-4f and is holding key0-1. All the positions are open except the following: f4-4f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-0 is at position f2-2f. Key key0-2 is at position f1-4f. The goal is to reach a state where the following facts hold: Key key0-2 is at f1-4f location, Key key0-1 is at f3-2f location, and Key key0-0 is at f2-2f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot is at f3-2f location", "answer": "yes"}
{"id": -8221057251287060871, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f3-0f and is holding key0-1. All the positions are open except the following: f4-4f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-0 is at position f2-2f. Key key0-2 is at position f1-4f. The goal is to reach a state where the following facts hold: Key key0-2 is at f1-4f location, Key key0-1 is at f3-2f location, and Key key0-0 is at f2-2f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot is at f3-2f location", "answer": "yes"}
{"id": 6753197458102839486, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-3 is of shape shape0, Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f4-1f and its arm is empty. All the positions are open except the following: f0-2f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-2 is at position f4-0f. Key key0-1 is at position f4-2f. Key key0-0 is at position f0-1f. Key key0-3 is at position f0-1f. The goal is to reach a state where the following facts hold: Key key0-0 is at f0-1f location, Key key0-1 is at f1-4f location, Key key0-2 is at f4-0f location, and Key key0-3 is at f0-1f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot is holding key0-1", "answer": "yes"}
{"id": 2514193642834254210, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f2-4f and its arm is empty. All the positions are open except the following: f2-1f has shape1 shaped lock, f3-4f has shape1 shaped lock. Key key1-0 is at position f1-0f. Key key1-1 is at position f3-2f. The goal is to reach a state where the following facts hold: Key key1-0 is at f1-0f location and Key key1-1 is at f4-4f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot is at f3-2f location", "answer": "yes"}
{"id": -2150187257571845619, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f3-3f and is holding key1-1. All the positions are open except the following: f3-4f has shape1 shaped lock. Key key1-0 is at position f1-0f. The goal is to reach a state where the following facts hold: Key key1-0 is at f1-0f location and Key key1-1 is at f4-4f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot is at f4-4f location", "answer": "yes"}
{"id": 4628971803681032817, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f1-0f and its arm is empty. All the positions are open except the following: f2-1f has shape1 shaped lock, f3-4f has shape1 shaped lock. Key key1-1 is at position f3-0f. Key key1-0 is at position f1-1f. The goal is to reach a state where the following facts hold: Key key1-0 is at f1-0f location and Key key1-1 is at f4-4f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot is at f1-1f location", "answer": "yes"}
{"id": 335322009804562048, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f3-3f and is holding key0-0. All the positions are open except the following: f4-2f has shape0 shaped lock, f2-0f has shape0 shaped lock. Key key0-2 is at position f0-4f. Key key0-1 is at position f2-2f. The goal is to reach a state where the following facts hold: Key key0-0 is at f3-3f location, Key key0-2 is at f3-3f location, and Key key0-1 is at f1-2f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot is at f1-4f location", "answer": "no"}
{"id": 7288279625544347787, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f1-3f and its arm is empty. All the positions are open except the following: f0-1f has shape0 shaped lock. Key key0-1 is at position f4-1f. Key key0-0 is at position f2-3f. The goal is to reach a state where the following facts hold: Key key0-0 is at f3-1f location and Key key0-1 is at f4-1f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Key key0-0 is at f1-3f location", "answer": "no"}
{"id": -8940300753236745654, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f1-2f and its arm is empty. All the positions are open except the following: f4-4f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-2 is at position f4-3f. Key key0-0 is at position f0-1f. Key key0-1 is at position f1-2f. The goal is to reach a state where the following facts hold: Key key0-2 is at f1-4f location, Key key0-1 is at f3-2f location, and Key key0-0 is at f2-2f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Key key0-1 is at f4-3f location", "answer": "no"}
{"id": -5625793508802672460, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f2-2f and is holding key0-2. All the positions are open except the following: f4-2f has shape0 shaped lock, f2-0f has shape0 shaped lock. Key key0-0 is at position f1-3f. Key key0-1 is at position f2-2f. The goal is to reach a state where the following facts hold: Key key0-0 is at f3-3f location, Key key0-2 is at f3-3f location, and Key key0-1 is at f1-2f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Key key0-0 is at f4-0f location", "answer": "no"}
{"id": 6201321203772062025, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f1-4f and its arm is empty. All the positions are open except the following: f0-0f has shape0 shaped lock. Key key0-1 is at position f3-0f. Key key0-0 is at position f1-4f. The goal is to reach a state where the following facts hold: Key key0-1 is at f3-0f location and Key key0-0 is at f0-4f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot is at f0-4f location", "answer": "yes"}
{"id": -8203515767434186474, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f0-1f and its arm is empty. All the positions are open except the following: f0-0f has shape0 shaped lock. Key key0-1 is at position f3-0f. Key key0-0 is at position f0-1f. The goal is to reach a state where the following facts hold: Key key0-1 is at f3-0f location and Key key0-0 is at f0-4f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Key key0-0 is at f2-2f location", "answer": "no"}
{"id": -8725356111934371763, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f1-3f and its arm is empty. All the positions are open except the following: f4-2f has shape0 shaped lock. Key key0-1 is at position f1-3f. Key key0-0 is at position f2-1f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-3f location and Key key0-0 is at f2-0f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot is at f2-1f location", "answer": "yes"}
{"id": -5152466019411708765, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-4f and its arm is empty. All the positions are open except the following: f2-1f has shape0 shaped lock. Key key0-0 is at position f1-0f. Key key0-1 is at position f4-3f. The goal is to reach a state where the following facts hold: Key key0-1 is at f4-4f location and Key key0-0 is at f1-0f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Key key0-1 is at f1-1f location", "answer": "no"}
{"id": 5996452493255214976, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f0-4f and its arm is empty. All the positions are open except the following: f2-1f has shape0 shaped lock, f3-4f has shape0 shaped lock. Key key0-0 is at position f1-2f. Key key0-1 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key0-1 is at f4-4f location and Key key0-0 is at f1-0f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot is at f1-0f location", "answer": "yes"}
