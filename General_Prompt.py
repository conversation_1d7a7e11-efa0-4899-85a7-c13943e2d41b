ACTION_ROLL_OUT = f"Based on the domain description and the current state, list all possible actions. Think as hard as you can. Your answer must satisfy all following requirements: 1.All actions in your answer must be legal(defined in the domain description) and executable(the current state satisfy all preconditions of an action). 2.Organize all possible actions into one paragraph, and each sentence only contains one action. 3.Don't use any markdown formatting. 4.After your analysis, return all possible actions into a new paragraph as the end of the answer."

ACTION_RANKING = f"Here are some possible actions based on the current state. Score each action from 0-5 based on the current state, goal state and the domain description. Your answer must satisfy all following requirements: 1.Assign a score ranging from 0 to 5, rounded to one decimal place. A higher score indicates that you believe the action is more likely to achieve the goal state. 2.Do not use any markdown syntax in your response. 3.After your analysis, organize your scores in a new concluding paragraph. For example, if there are three actions, your final paragraph should be: '1.5, 2.0, 4.5.' "

ACTION_LIST_CHECK = f"Here are two action lists, you are required to check whether the two action lists are the same. You can start by checking whether the number of actions in two lists are same or not. Your answer must satisfy all following requirements: 1.If the two action lists are the same, return 'True'. Otherwise, return 'False'. 2.Don't use any markdown formatting. 3.After your analysis, return your final answer in the last paragraph."
