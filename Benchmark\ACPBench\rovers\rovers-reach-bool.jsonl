{"id": 1132511168930332617, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera0 and camera1 on board. Rover rover1 has camera2 on board. Camera camera0 can be calibrated on objective1. Camera camera2 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour. Camera camera1 supports low_res. Camera camera2 supports colour and high_res. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint2. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint0; Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has its camera camera2 calibrated. Store(s) store1 and store0 are full. ", "question": "Is it possible to transition to a state where the following holds: Rover rover0 has its camera camera1 calibrated and Store(s) store0 is empty?", "answer": "yes"}
{"id": -1043496969056018846, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera0 supports low_res. Camera camera1 supports low_res and high_res. Rover rover0 can traverse from waypoint2 to waypoint1, waypoint2 to waypoint0, waypoint0 to waypoint2, waypoint1 to waypoint2. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint1. Rocks can be sampled at the following location(s): waypoint1. Soil can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Rover rover1 has image objective1 in mode low_res. Store(s) store0 and store1 are empty. ", "question": "Is it possible to transition to a state where the following holds: Rover rover0 is at waypoint0 and Store(s) store1 is full?", "answer": "yes"}
{"id": -1902777571294228735, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera0 supports low_res. Camera camera1 supports low_res and high_res. Rover rover0 can traverse from waypoint2 to waypoint1, waypoint2 to waypoint0, waypoint0 to waypoint2, waypoint1 to waypoint2. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint2. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint1; Soil data was communicated from waypoint waypoint1; Image objective0 was communicated in mode low_res. Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover0 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective1 in mode low_res. Rover rover0 has image objective0 in mode low_res. Store(s) store1 is empty. Store(s) store0 is full. ", "question": "Is it possible to transition to a state where the following holds: Soil can be sampled at the following location(s): waypoint0?", "answer": "no"}
{"id": 6532627485091435659, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera0 supports low_res. Camera camera1 supports low_res and high_res. Rover rover0 can traverse from waypoint2 to waypoint1, waypoint2 to waypoint0, waypoint0 to waypoint2, waypoint1 to waypoint2. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint1. Soil can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint1; Image objective1 was communicated in mode low_res. Image objective0 was communicated in mode low_res. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover1 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode low_res. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Is it possible to transition to a state where the following holds: Rocks can be sampled at the following location(s): waypoint0?", "answer": "no"}
{"id": -2130439532138217037, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera0 and camera1 on board. Rover rover1 has camera2 on board. Camera camera0 can be calibrated on objective1. Camera camera2 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour. Camera camera1 supports low_res. Camera camera2 supports colour and high_res. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint0. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Image objective1 was communicated in mode colour. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has image objective1 in mode colour. Rover rover0 has its camera camera1 calibrated. Store(s) store1 and store0 are full. ", "question": "Is it possible to transition to a state where the following holds: Soil data was communicated from waypoint waypoint1;?", "answer": "no"}
{"id": -5920828701192469606, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera0 and camera1 on board. Rover rover1 has camera2 on board. Camera camera0 can be calibrated on objective1. Camera camera2 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour. Camera camera1 supports low_res. Camera camera2 supports colour and high_res. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint1. Rovers rover0 and rover1 are available. Soil data was communicated from waypoint waypoint0; Image objective1 was communicated in mode colour. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has image objective1 in mode colour. Store(s) store1 and store0 are full. ", "question": "Is it possible to transition to a state where the following holds: Rover rover1 has rock analyzed in waypoint waypoint0 and Rocks can be sampled at the following location(s): waypoint2?", "answer": "no"}
{"id": -1016262709697167809, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera0 on board. Rover rover1 has camera1 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera1 supports colour and low_res. Camera camera0 supports colour and low_res. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint4 to waypoint1, waypoint0 to waypoint1, waypoint1 to waypoint4. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint4: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2, waypoint0, and waypoint4. Waypoint(s) are visible from waypoint0: waypoint2, waypoint3, waypoint4, and waypoint1. Waypoint(s) are visible from waypoint2: waypoint0, waypoint3, and waypoint1. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint2 and waypoint1. Lander general is at waypoint waypoint3.  Currently, Rover rover0 is at waypoint4. Rover rover1 is at waypoint1. Rocks can be sampled at the following location(s): waypoint1 and waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Rover rover1 has image objective0 in mode colour. Rover rover0 has image objective1 in mode low_res. Rover rover1 has its camera camera1 calibrated. Store(s) store0 and store1 are empty. ", "question": "Is it possible to transition to a state where the following holds: Rover rover1 has image objective1 in mode high_res?", "answer": "no"}
{"id": -2233591958937444481, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 7 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  No rovers are equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera2 on board. Rover rover1 has camera1 and camera0 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective1. Camera camera2 supports colour and low_res. Camera camera0 supports low_res and colour and high_res. Camera camera1 supports colour. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint6 to waypoint0, waypoint2 to waypoint0, waypoint3 to waypoint0, waypoint5 to waypoint0, waypoint6 to waypoint1, waypoint0 to waypoint3, waypoint2 to waypoint4, waypoint1 to waypoint6, waypoint0 to waypoint5, waypoint4 to waypoint2, waypoint0 to waypoint6. Rover rover0 can traverse from waypoint5 to waypoint0, waypoint5 to waypoint2, waypoint3 to waypoint0, waypoint4 to waypoint5, waypoint5 to waypoint1, waypoint0 to waypoint3, waypoint0 to waypoint5, waypoint6 to waypoint5, waypoint1 to waypoint5, waypoint5 to waypoint4, waypoint2 to waypoint5, waypoint5 to waypoint6. Waypoint(s) are visible from waypoint3: waypoint0, waypoint6, waypoint4, waypoint1, waypoint5, and waypoint2. Waypoint(s) are visible from waypoint1: waypoint5, waypoint2, waypoint3, waypoint6, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint6, waypoint1, waypoint5, waypoint2, and waypoint3. Waypoint(s) are visible from waypoint5: waypoint6, waypoint0, waypoint1, waypoint4, waypoint2, and waypoint3. Waypoint(s) are visible from waypoint6: waypoint0, waypoint1, waypoint4, waypoint3, and waypoint5. Waypoint(s) are visible from waypoint0: waypoint6, waypoint2, waypoint3, and waypoint5. Waypoint(s) are visible from waypoint2: waypoint0, waypoint4, waypoint1, waypoint5, and waypoint3. Objective objective1 is visible from waypoint2, waypoint0, waypoint4, and waypoint5. Objective objective0 is visible from waypoint0. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint5. Rocks can be sampled at the following location(s): waypoint4, waypoint6, and waypoint2. Soil can be sampled at the following location(s): waypoint3, waypoint4, waypoint6, and waypoint1. Rovers rover0 and rover1 are available. Image objective1 was communicated in mode colour. Image objective0 was communicated in mode high_res. Rover rover1 has rock analyzed in waypoint waypoint3. Rover rover0 has rock analyzed in waypoint waypoint5. Rover rover1 has image objective0 in mode high_res. Rover rover1 has image objective1 in mode colour. Rover rover1 has its camera camera0 calibrated. Store(s) store1 and store0 are full. ", "question": "Is it possible to transition to a state where the following holds: Store(s) store0 is empty and Rover rover0 is at waypoint1?", "answer": "yes"}
{"id": -7828064559952906372, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera0 on board. Rover rover1 has camera1 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera1 supports colour and low_res. Camera camera0 supports colour and low_res. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint4 to waypoint1, waypoint0 to waypoint1, waypoint1 to waypoint4. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint4: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2, waypoint0, and waypoint4. Waypoint(s) are visible from waypoint0: waypoint2, waypoint3, waypoint4, and waypoint1. Waypoint(s) are visible from waypoint2: waypoint0, waypoint3, and waypoint1. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint2 and waypoint1. Lander general is at waypoint waypoint3.  Currently, Rover rover0 is at waypoint4. Rover rover1 is at waypoint2. Rocks can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint0; Image objective0 was communicated in mode low_res. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover0 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode low_res. Rover rover1 has its camera camera1 calibrated. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Is it possible to transition to a state where the following holds: Rover rover1 has rock analyzed in waypoint waypoint1 and Rocks can be sampled at the following location(s): waypoint1?", "answer": "no"}
{"id": 7840568113505898601, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 7 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  No rovers are equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera2 on board. Rover rover1 has camera1 and camera0 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective1. Camera camera2 supports colour and low_res. Camera camera0 supports low_res and colour and high_res. Camera camera1 supports colour. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint6 to waypoint0, waypoint2 to waypoint0, waypoint3 to waypoint0, waypoint5 to waypoint0, waypoint6 to waypoint1, waypoint0 to waypoint3, waypoint2 to waypoint4, waypoint1 to waypoint6, waypoint0 to waypoint5, waypoint4 to waypoint2, waypoint0 to waypoint6. Rover rover0 can traverse from waypoint5 to waypoint0, waypoint5 to waypoint2, waypoint3 to waypoint0, waypoint4 to waypoint5, waypoint5 to waypoint1, waypoint0 to waypoint3, waypoint0 to waypoint5, waypoint6 to waypoint5, waypoint1 to waypoint5, waypoint5 to waypoint4, waypoint2 to waypoint5, waypoint5 to waypoint6. Waypoint(s) are visible from waypoint3: waypoint0, waypoint6, waypoint4, waypoint1, waypoint5, and waypoint2. Waypoint(s) are visible from waypoint1: waypoint5, waypoint2, waypoint3, waypoint6, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint6, waypoint1, waypoint5, waypoint2, and waypoint3. Waypoint(s) are visible from waypoint5: waypoint6, waypoint0, waypoint1, waypoint4, waypoint2, and waypoint3. Waypoint(s) are visible from waypoint6: waypoint0, waypoint1, waypoint4, waypoint3, and waypoint5. Waypoint(s) are visible from waypoint0: waypoint6, waypoint2, waypoint3, and waypoint5. Waypoint(s) are visible from waypoint2: waypoint0, waypoint4, waypoint1, waypoint5, and waypoint3. Objective objective1 is visible from waypoint2, waypoint0, waypoint4, and waypoint5. Objective objective0 is visible from waypoint0. Lander general is at waypoint waypoint3.  Currently, Rover rover0 is at waypoint4. Rover rover1 is at waypoint5. Rocks can be sampled at the following location(s): waypoint4, waypoint6, waypoint2, and waypoint3. Soil can be sampled at the following location(s): waypoint3, waypoint4, waypoint6, and waypoint1. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint5; Image objective1 was communicated in mode high_res. Image objective0 was communicated in mode high_res. Rover rover0 has rock analyzed in waypoint waypoint5. Rover rover1 has image objective0 in mode high_res. Rover rover1 has image objective1 in mode high_res. Rover rover0 has its camera camera2 calibrated. Store(s) store1 is empty. Store(s) store0 is full. ", "question": "Is it possible to transition to a state where the following holds: Rover rover0 has rock analyzed in waypoint waypoint3 and Rover rover1 has rock analyzed in waypoint waypoint3?", "answer": "no"}
{"id": 4802916355221978023, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera0 can be calibrated on objective2. Camera camera1 can be calibrated on objective1. Camera camera0 supports low_res. Camera camera1 supports low_res and high_res. Rover rover0 can traverse from waypoint2 to waypoint1, waypoint2 to waypoint0, waypoint0 to waypoint2, waypoint1 to waypoint2. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint2. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Soil data was communicated from waypoint waypoint2; Rover rover0 has soil analyzed in waypoint waypoint2. Rover rover1 has rock analyzed in waypoint waypoint2. Rover rover0 has image objective1 in mode low_res. Rover rover0 has image objective0 in mode low_res. Rover rover0 has image objective1 in mode high_res. Store(s) store1 and store0 are full. ", "question": "Is it possible to transition to a state where the following holds: Rover rover0 is at waypoint0 and Rover rover1 is at waypoint0?", "answer": "yes"}
{"id": -7669604320220958070, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera2 on board. Rover rover1 has camera1 and camera0 on board. Camera camera2 can be calibrated on objective2. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera0 supports low_res and colour. Camera camera2 supports high_res. Camera camera1 supports high_res. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint2 to waypoint1, waypoint0 to waypoint1, waypoint1 to waypoint2. Rover rover1 can traverse from waypoint1 to waypoint0, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Objective objective1 is visible from waypoint0 and waypoint1. Objective objective2 is visible from waypoint0. Objective objective0 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Image objective1 was communicated in mode high_res. Rover rover1 has image objective1 in mode low_res. Rover rover1 has image objective1 in mode high_res. Store(s) store0 and store1 are empty. ", "question": "Is it possible to transition to a state where the following holds: Rover rover1 is at waypoint1 and Rover rover1 has soil analyzed in waypoint waypoint0?", "answer": "yes"}
{"id": -4030372106350146955, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 5 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera2 on board. Rover rover1 has camera1 and camera0 on board. Camera camera2 can be calibrated on objective4. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera0 supports low_res and colour. Camera camera2 supports high_res. Camera camera1 supports high_res. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint2 to waypoint1, waypoint0 to waypoint1, waypoint1 to waypoint2. Rover rover1 can traverse from waypoint1 to waypoint0, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Objective objective1 is visible from waypoint0 and waypoint1. Objective objective3 is visible from waypoint0. Objective objective2 is visible from waypoint0. Objective objective4 is visible from waypoint1. Objective objective0 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint1. Rover rover1 is at waypoint1. Rocks can be sampled at the following location(s): waypoint2. Soil can be sampled at the following location(s): waypoint2 and waypoint0. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint1; Image objective0 was communicated in mode high_res. Image objective2 was communicated in mode high_res. Rover rover0 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective0 in mode high_res. Rover rover0 has image objective2 in mode high_res. Rover rover0 has its camera camera2 calibrated. Store(s) store0 and store1 are empty. ", "question": "Is it possible to transition to a state where the following holds: Rocks can be sampled at the following location(s): waypoint0 and Channel general is free?", "answer": "no"}
{"id": 6156317977928621649, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera2 on board. Rover rover1 has camera1 and camera0 on board. Camera camera2 can be calibrated on objective2. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera0 supports low_res and colour. Camera camera2 supports high_res. Camera camera1 supports high_res. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint2 to waypoint1, waypoint0 to waypoint1, waypoint1 to waypoint2. Rover rover1 can traverse from waypoint1 to waypoint0, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Objective objective1 is visible from waypoint0 and waypoint1. Objective objective2 is visible from waypoint0. Objective objective0 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint1. Rocks can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Rover rover1 has soil analyzed in waypoint waypoint0. Rover rover1 has image objective1 in mode low_res. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Is it possible to transition to a state where the following holds: Rover rover0 is at waypoint1 and Rover rover1 is at waypoint2?", "answer": "yes"}
{"id": -8403308311277403202, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera2 on board. Rover rover1 has camera1 and camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective1. Camera camera0 can be calibrated on objective0. Camera camera0 supports low_res and colour. Camera camera2 supports high_res. Camera camera1 supports high_res. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint2 to waypoint1, waypoint0 to waypoint1, waypoint1 to waypoint2. Rover rover1 can traverse from waypoint1 to waypoint0, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Objective objective1 is visible from waypoint0 and waypoint1. Objective objective0 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint1. Rocks can be sampled at the following location(s): waypoint0 and waypoint2. Soil can be sampled at the following location(s): waypoint0 and waypoint1. Rovers rover0 and rover1 are available. Image objective0 was communicated in mode high_res. Rover rover0 has image objective0 in mode high_res. Store(s) store0 and store1 are empty. ", "question": "Is it possible to transition to a state where the following holds: Rocks can be sampled at the following location(s): waypoint1 and Image objective0 was communicated in mode high_res?", "answer": "no"}
